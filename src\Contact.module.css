/* Import Satoshi font */
@import url('https://api.fontshare.com/v2/css?f[]=satoshi@900,700,500,301,300,400&display=swap');

/* Import local Telegraf fonts */
@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Archivo';
  src: url('/fonts/archivo-black-regular.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Lenis smooth scrolling styles */
html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}

canvas {
  touch-action: none;
}

/* Scroll Overlay Styles */
.scrollOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  z-index: 10;
  pointer-events: auto;
  /* Hide scrollbar but keep functionality */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Specific class for contact section's scroll overlay */
.contactScrollOverlay {
  overflow: hidden !important;
  height: 100vh;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollOverlay::-webkit-scrollbar {
  display: none;
}

.contentSection {
  position: relative;
  min-height: 250vh; /* Increased to accommodate contact section */
  width: 100%;
}

/* Base paragraph styling */
.paragraph {
  position: absolute;
  width: 40%;
  max-width: 1000px;
}

/* Individual paragraph positioning - vertical spacing and horizontal positioning */
.paragraph1 {
  top: 100vh; /* Slightly below the first 100vh */
  left: 12%; /* Custom left positioning */
}

.paragraphContainer {
  padding: 2rem;
  border-radius: 8px;
  color: white;
}

.paragraphContainer h2 {
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  font-size: 3rem;
  margin-bottom: 1rem;
}

.paragraphContainer p {
  font-family: 'Telegraf', sans-serif;
  font-weight: 400;
  font-size: 1.2rem;
  line-height: 1.5;
  max-width: 500px;
}

/* Make the overlay responsive */
@media (max-width: 768px) {
  .paragraph {
    width: 85%;
  }
  
  /* Individual mobile positioning - override desktop positioning */
  .paragraph1 {
    left: 5%;
    right: auto;
  }
}

/* Logo container styling */
.logoContainer {
  display: flex;
  justify-content: left;
  margin-bottom: 1.5rem;
}

.sectionLogo {
  max-width: 400px; /* Adjust the size as needed */
  height: auto;
}

.smallHeading {
  margin-bottom: 0.75rem;
}

.smallHeading h3 {
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  font-size: 2rem; /* Slightly bigger than paragraph text (1.2rem) */
  line-height: 1.3;
  margin: 0;
  color: white;
}

/* Contact section specific styles for fixed 100vh view */
.contactComponent {
  width: 100%;
  height: 100vh;
  background-color: #000;
  position: relative;
  overflow: hidden;
}

/* Override scrolling behavior for contact section */
.contactSection {
  position: relative;
  height: 100vh; /* Fixed 100vh height */
  width: 100%;
  z-index: 5;
  background-color: transparent;
  overflow: hidden; /* Prevent scrolling */
}

/* Contact section wrapper */
.contactWrapper {
  position: relative; 
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 20;
  display: flex;
  flex-direction: column;
}

/* Updated contact content section to be exactly 100vh */
.contentSection {
  height: 100vh !important;
  min-height: 100vh !important;
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* Contact section styles */
.contactContent {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 2rem;
  position: relative;
  z-index: 2;
  overflow: hidden; /* Prevent scrolling */
}

.contactEmailContainer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.contactEmailHeading {
  font-size: 4rem;
  font-weight: 500;
  color: #ffffff;
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.contactEmailLink {
  text-decoration: none;
  color: inherit; /* Preserves the original text color */
}

.contactEmailLink:hover {
  text-decoration: underline;
  cursor: pointer;
}

.contactEmailHeading:hover {
  transform: scale(1.05);
  opacity: 0.7;
}

.contactEmailHeading::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #ffffff;
  transition: transform 0.4s ease;
  transform-origin: left;
  transform: scaleX(0); /* Start with no line */
}

.contactEmailHeading:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Contact Bottom - updated for the new layout */
.contactBottom {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-bottom: 0;
  position: relative;
}

/* Copyright and Built By positioning */
.contactCopyright {
  position: absolute;
  bottom: 0px;
  left: 0%;
  font-size: 0.7rem;
  color: white;
  opacity: 0.75;
  letter-spacing: 1px;
  font-family: 'Telegraf', sans-serif;
}

.contactBuiltBy {
  position: absolute;
  bottom: 0px;
  right: 0%;
  font-size: 0.7rem;
  color: white;
  opacity: 0.75;
  letter-spacing: 1px;
  font-family: 'Telegraf', sans-serif;
}

/* Footer section layout - 3 grid layout */
.footerSection {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  width: 100%;
  max-width: 800px;
  position: absolute;
  bottom: 100px;
  /* Center the grid horizontally */
  margin: 0 auto;
  left: 0;
  right: 0;
}

/* Footer links container styling */
.footerLinksContainer {
  display: flex;
  flex-direction: column;
  /* Center content within its grid cell */
  align-items: center;
  justify-content: center;
}

/* Footer link text styling */
.footerLinks {
  display: flex;
  flex-direction: column;
  gap: 12px;
  /* Center the content */
  align-items: center;
}

.footerLink {
  color: #ffffff;
  text-decoration: none;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 1px;
  transition: opacity 0.3s ease;
}

.footerLink:hover {
  opacity: 0.7;
}

/* Social icons styling - positioned in the center grid */
.socialIcons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}

.socialIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: transparent;
  transition: opacity 0.3s ease;
}

.socialIcon:hover {
  opacity: 0.7;
}

.socialIcon img {
  width: 24px;
  height: 24px;
}

/* QR Code container styling */
.qrCodeContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.qrCodeImage {
  width: 100px;
  height: 100px;
  margin-bottom: 0px;
}

.qrCodeHeading {
  color: #ffffff;
  opacity: 0.7;
  font-size: 10px;
  font-weight: 400;
  text-align: center;
}

/* Media queries */
@media (max-width: 768px) {
  .contactComponent.contactComponent {
    height: 100vh;
    overflow: hidden;
  }
  
  /* Move the entire content up by 50px */
  .contactContent {
    height: 100vh;
    overflow: hidden;
    padding: 1.5rem;
    padding-bottom: 4rem;
    justify-content: space-between;
    /* Transform to move everything up by 50px */
    transform: translateY(-15%);
  }
  
  .contactEmailHeading {
    font-size: 2.5rem;
    /* Move email heading higher */
    margin-top: -40px;
  }
  
  .contactWrapper {
    top: 0;
    height: 100vh;
  }
  
  .contactSection {
    height: 100vh;
    overflow: hidden;
  }
  
  .contentSection {
    height: 100vh !important;
    min-height: 100vh !important;
    padding: 0;
    overflow: hidden;
  }
  
  /* Increase space between image containers in About section */
  .imageContainer + .imageContainer {
    margin-top: 30px; /* Increased spacing between image containers */
  }
  
  /* Reduce space between subheading and bullet points in Services section */
  .smallHeading + .bulletPoints {
    margin-top: 8px; /* Reduced space between subheading and bullet points */
  }
  
  /* Modified footer grid for mobile - Moved higher up */
  .footerSection {
    grid-template-columns: 1fr;
    gap: 25px; /* Reduced gap between grid items */
    width: 90%;
    bottom: 180px; /* Moved higher up by 50px (from 130px to 180px) */
  }
  
  .contactCopyright,
  .contactBuiltBy {
    position: static;
    text-align: center;
    margin-top: 30px; /* Reduced from 40px */
    margin-bottom: 10px; /* Reduced to create less space */
  }
  
  .contactBottom {
    margin-top: auto;
    padding-top: 10px; /* Reduced from 20px */
    padding-bottom: 60px; /* Increased by 50px (from 30px to 80px) */
  }
  
  .footerLinksContainer, 
  .socialIcons, 
  .qrCodeContainer {
    justify-content: center;
    align-items: center;
  }
  
  .footerLinks {
    flex-direction: row;
    gap: 15px;
    justify-content: center;
  }
  
  .qrCodeImage {
    width: 80px;
    height: 80px;
  }
}

/* Smaller mobile devices */
@media (max-width: 480px) {
  .contactEmailHeading {
    font-size: 2rem;
    text-align: center;
    /* Keep the email heading high position */
    margin-top: -50px;
  }
  
  .footerSection {
    width: 95%;
    gap: 15px; /* Reduced gap further */
    bottom: 180px; /* Kept consistent with the larger mobile adjustment */
  }
  
  .contactCopyright,
  .contactBuiltBy {
    font-size: 0.6rem;
    margin-bottom: 15px; /* Reduced to bring elements closer */
    margin-top: 20px; /* Reduced from 30px */
  }
  
  /* Reduce gap between copyright and built by */
  .contactCopyright + .contactBuiltBy {
    margin-top: 5px; /* Small gap between them */
  }
  
  .footerLinks {
    flex-direction: column;
    align-items: center;
    gap: 10px; /* Reduced gap between footer links */
  }
  
  .qrCodeImage {
    width: 70px;
    height: 70px;
  }
  
  .contactBottom {
    padding-bottom: 60px; /* Kept consistent with larger mobile adjustment */
  }
}