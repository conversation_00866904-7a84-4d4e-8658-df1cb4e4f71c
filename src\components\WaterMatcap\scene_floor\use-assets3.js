import { useGLTF, useTexture } from "@react-three/drei";
import { useMemo } from "react";
import * as THREE from "three";

export function useAssets() {
  // Load the GLB and extract the mesh
  const { nodes } = useGLTF("/models/extruded-try-soft.glb");
  const { nodes: cubeNodes } = useGLTF("/models/extruded-try-soft-cube-round-whole.glb");

  const pyramid = nodes["Cylinder"];
  // Try to find the main mesh node for the cube. Adjust the key if needed.
  const cubeRoundWhole = cubeNodes["Cylinder"];

  const envMap = useTexture("/textures/hdri4.png");
  const matcap = useTexture("/textures/matcap-daniel-90-B-C-15.jpg");

  envMap.magFilter = THREE.NearestFilter;
  envMap.minFilter = THREE.NearestFilter;
  envMap.wrapS = THREE.RepeatWrapping;
  envMap.wrapT = THREE.RepeatWrapping;

  const noiseMap = useTexture("/textures/noise-LDR_RGBA_63.png");

  noiseMap.wrapS = THREE.RepeatWrapping;
  noiseMap.wrapT = THREE.RepeatWrapping;

  const assets = useMemo(() => {
    return {
      envMap,
      noiseMap,
      matcap,
      pyramid,
      cubeRoundWhole
    };
  }, [envMap, noiseMap, matcap, pyramid, cubeRoundWhole]);

  return assets;
}