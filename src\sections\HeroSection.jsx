import React, { Suspense, useRef, useState, useEffect } from 'react';
import { Canvas, useThree, useLoader, useFrame } from '@react-three/fiber';
import * as THREE from 'three';
import styles from './HeroSection.module.css';
import WaterMatcapScene from '../components/WaterMatcap/WaterMatcapScene';
import WaterMatcapBackground from '../components/WaterMatcap/WaterMatcapBackground';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader';
import { MeshTransmissionMaterial } from '@react-three/drei';
import { useControls } from 'leva';
import AboutOverlaySection from './AboutOverlaySection';
import ServicesOverlaySection from './ServicesOverlaySection';

const logoUrl = '/blcks_white_short.svg';

function BlackBackground() {
  const { scene } = useThree();
  React.useEffect(() => {
    scene.background = new THREE.Color('#000000');
    return () => {
      scene.background = null;
    };
  }, [scene]);
  return null;
}

function Overlay({ style }) {
  return (
    <div
      className={styles.overlay}
      style={{ pointerEvents: 'none', opacity: 1, ...style }}
    >
      <div className={styles.topNavBar}>
        <img src="/blcks_logo_icon.svg" alt="BLCKS Logo" className={styles.logoImg} />
        <img src="/menu_icon.svg" alt="Menu Icon" className={styles.menuIconTopRight} />
      </div>
      <div className={styles.heroContentWrapper}>
        <h1 className={styles.heroHeading}>
          Wir entwickeln leistungsstarke<br />
          KI-Systeme für skalierbares<br />
          Wachstum
        </h1>
        <button className={styles.heroButton}>EXPLORE OUR SERVICES</button>
      </div>
      {/* 6 absolutely positioned hero texts inside overlay */}
      <div className={styles.heroText1}>BLCK.LABS V01<br />GRAZ. AUT<br />NUM ID : A420</div>
      <div className={styles.heroText2}>AI.RESEARCH<br />LAB 10</div>
      <div className={styles.heroText3}>PROFIL #01</div>
      <div className={styles.heroText4}>BLCKS V01<br />REFERENCE ID 2308106</div>
      <div className={styles.heroText5}>AI DEVELOPMENT<br />STUDIO COMPANY</div>
      <div className={styles.heroText6}>WEB RENDERED v0.2.4</div>
      <div className={styles.heroText7}>KEY GENERATION v0.1.2</div>
      <div className={styles.subText}>
        Wir entwickeln, optimieren und skalieren intelligente Automatisierungslösungen, die Prozesse verschlanken, Effizienz steigern und den ROI maximieren.
      </div>
      <div className={styles.scrollIndicator}>
        <p className={styles.scrollText}>SCROLL DOWN</p>
        <img src="/scrolldownpfeil_white.svg" alt="Scroll Down" className={styles.scrollArrow} />
      </div>
    </div>
  );
}

const Block1Mesh = React.forwardRef(function Block1Mesh(props, ref) {
  const gl = useThree((state) => state.gl);
  const geometry = useLoader(
    DRACOLoader,
    '/models/block2.drc',
    loader => {
      loader.setDecoderPath('https://www.gstatic.com/draco/v1/decoders/');
    }
  );
  const normalMap = useLoader(KTX2Loader, '/textures/block2_normal.ktx2', loader => {
    loader.setTranscoderPath('https://unpkg.com/three@0.152.2/examples/jsm/libs/basis/');
    loader.detectSupport(gl);
  });
  const roughnessMap = useLoader(KTX2Loader, '/textures/block2_roughness.ktx2', loader => {
    loader.setTranscoderPath('https://unpkg.com/three@0.152.2/examples/jsm/libs/basis/');
    loader.detectSupport(gl);
  });
  return (
    <mesh ref={ref} geometry={geometry} {...props}>
      <MeshTransmissionMaterial
        normalMap={normalMap}
        roughnessMap={roughnessMap}
        color={'#fff'}
        backside
        samples={4}
        thickness={3}
        chromaticAberration={0.01}
        anisotropy={0.1}
        distortion={0}
        distortionScale={0}
        temporalDistortion={0}
        iridescence={1}
        iridescenceIOR={1}
        iridescenceThicknessRange={[0, 1400]}
        side={THREE.DoubleSide}
      />
    </mesh>
  );
});

function ScrollInterpolatedBlock({ positions, rotations, scrollProgress, ...props }) {
  // Calculate which positions to interpolate between
  const totalSections = 3;
  const sectionProgress = scrollProgress * totalSections;
  
  // Define the position mappings for each section
  const positionMappings = [
    { start: 0, end: 0.33, from: 'B', to: 'C' },    // Section 1: B to C (start moving early)
    { start: 0.33, end: 0.66, from: 'C', to: 'D' }, // Section 2: C to D
    { start: 0.66, end: 1, from: 'D', to: 'D' }     // Section 3: D to D (static)
  ];
  
  // Find current section
  const currentSection = positionMappings.find(section => 
    sectionProgress >= section.start * totalSections && sectionProgress < section.end * totalSections
  ) || positionMappings[0];
  
  // Calculate local progress within the current section
  const sectionStart = currentSection.start * totalSections;
  const sectionEnd = currentSection.end * totalSections;
  const localProgress = Math.max(0, Math.min(1, (sectionProgress - sectionStart) / (sectionEnd - sectionStart)));
  
  // Get the positions to interpolate between
  const fromPosition = positions[currentSection.from];
  const toPosition = positions[currentSection.to];
  const fromRotation = rotations[currentSection.from];
  const toRotation = rotations[currentSection.to];
  
  // Interpolate position and rotation
  const interpolatedPosition = fromPosition.map((v, i) => 
    v + (toPosition[i] - v) * localProgress
  );
  const interpolatedRotation = fromRotation.map((v, i) => 
    v + (toRotation[i] - v) * localProgress
  );
  
  return (
    <Block1Mesh position={interpolatedPosition} rotation={interpolatedRotation} {...props} />
  );
}

function BlockPositionSwitcher({ activeBlock, setActiveBlock }) {
  return (
    <div className={styles.blockSwitcherWrapper}>
      <button
        className={activeBlock === 'A' ? styles.activeSwitcherButton : styles.switcherButton}
        onClick={() => setActiveBlock('A')}
      >
        Block Position A
      </button>
      <button
        className={activeBlock === 'B' ? styles.activeSwitcherButton : styles.switcherButton}
        onClick={() => setActiveBlock('B')}
      >
        Block Position B
      </button>
      <button
        className={activeBlock === 'C' ? styles.activeSwitcherButton : styles.switcherButton}
        onClick={() => setActiveBlock('C')}
      >
        Block Position C
      </button>
      <button
        className={activeBlock === 'D' ? styles.activeSwitcherButton : styles.switcherButton}
        onClick={() => setActiveBlock('D')}
      >
        Block Position D
      </button>
      <button
        className={activeBlock === 'E' ? styles.activeSwitcherButton : styles.switcherButton}
        onClick={() => setActiveBlock('E')}
      >
        Block Position E
      </button>
      <button
        className={activeBlock === 'F' ? styles.activeSwitcherButton : styles.switcherButton}
        onClick={() => setActiveBlock('F')}
      >
        Block Position F
      </button>
    </div>
  );
}

// Card component for Section 3
function InfoCard({ title, region, description, onClick }) {
  return (
    <div className={styles.infoCard} onClick={onClick} style={{ cursor: onClick ? 'pointer' : undefined }}>
      <div className={styles.infoCardContentBottom}>
        {/* <div className={styles.infoCardRegion}>{region}</div> */}
        <img src="/Blcks_Icon.svg" alt="Blcks Icon" className={styles.infoCardIcon} />
        <div className={styles.infoCardTitle}>{title}</div>
        <div className={styles.infoCardDescription}>{description}</div>
      </div>
      <div className={styles.infoCardArrowBox}>
        <img src="/arrow-up-right-grey.svg" alt="Arrow Up Right" className={styles.infoCardArrowIcon} />
      </div>
    </div>
  );
}

export default function HeroSection() {
  const [{ activeBlock, block1PositionA, block1RotationA, block1PositionB, block1RotationB, block1PositionC, block1RotationC, block1PositionD, block1RotationD, block1PositionE, block1RotationE, block1PositionF, block1RotationF }, set] = useControls(() => ({
    activeBlock: {
      value: 'A',
      options: { A: 'A', B: 'B', C: 'C', D: 'D', E: 'E', F: 'F' },
      label: 'Active Block Position',
    },
    block1PositionA: {
      value: [-1, 2, 0],
      label: 'Block1 Position A',
      step: 0.01,
      min: -10,
      max: 10,
    },
    block1RotationA: {
      value: [0.75, 0, 3],
      label: 'Block1 Rotation A',
      step: 0.01,
      min: -Math.PI,
      max: Math.PI,
    },
    block1PositionB: {
      value: [0.8899999999999937, 0.15999999999999653, 0.030000000000000023],
      label: 'Block1 Position B',
      step: 0.01,
      min: -10,
      max: 10,
    },
    block1RotationB: {
      value: [-1.321592653589802, -0.8600000000000004, 1.6600000000000013],
      label: 'Block1 Rotation B',
      step: 0.01,
      min: -Math.PI,
      max: Math.PI,
    },
    block1PositionC: {
      value: [-1.06, 0.16, -0.01],
      label: 'Block1 Position C',
      step: 0.01,
      min: -10,
      max: 10,
    },
    block1RotationC: {
      value: [0.61, 0.18, -0.16],
      label: 'Block1 Rotation C',
      step: 0.01,
      min: -Math.PI,
      max: Math.PI,
    },
    block1PositionD: {
      value: [0.65, 3.50, 0.20],
      label: 'Block1 Position D',
      step: 0.01,
      min: -10,
      max: 10,
    },
    block1RotationD: {
      value: [-1.40, -0.87, 0.19],
      label: 'Block1 Rotation D',
      step: 0.01,
      min: -Math.PI,
      max: Math.PI,
    },
    block1PositionE: {
      value: [0.14, 2.51, 0.20], // Same as D
      label: 'Block1 Position E',
      step: 0.01,
      min: -10,
      max: 10,
    },
    block1RotationE: {
      value: [-1.40, -0.87, 0.19], // Same as D
      label: 'Block1 Rotation E',
      step: 0.01,
      min: -Math.PI,
      max: Math.PI,
    },
    block1PositionF: {
      value: [0.14, 2.51, 0.20], // Default value, adjust as needed
      label: 'Block1 Position F',
      step: 0.01,
      min: -10,
      max: 10,
    },
    block1RotationF: {
      value: [-1.40, -0.87, 0.19], // Default value, adjust as needed
      label: 'Block1 Rotation F',
      step: 0.01,
      min: -Math.PI,
      max: Math.PI,
    },
  }));

  // Scroll-based navigation state
  const [scrollActiveBlock, setScrollActiveBlock] = useState('B');
  const [scrollProgress, setScrollProgress] = useState(0);
  const containerRef = useRef(null);

  // Overlay state
  const [aboutOverlayOpen, setAboutOverlayOpen] = useState(false);
  const [servicesOverlayState, setServicesOverlayState] = useState({ open: false, initialSection: null });

  // Animation state for moving to E and back to C
  const [isAnimatingToE, setIsAnimatingToE] = useState(false);
  const [isAnimatingToC, setIsAnimatingToC] = useState(false);
  const [animationProgress, setAnimationProgress] = useState(0);
  const animationRef = useRef();
  const [fromPosition, setFromPosition] = useState(null);
  const [fromRotation, setFromRotation] = useState(null);

  // State to lock the block at E after animation
  const [blockLockedToE, setBlockLockedToE] = useState(false);

  // --- Animation state for A->B on mount ---
  const [isAnimatingAToB, setIsAnimatingAToB] = useState(true); // Start animating on mount
  const [animationAToBProgress, setAnimationAToBProgress] = useState(0);
  const animationAToBRef = useRef();

  // Handle scroll-based navigation
  useEffect(() => {
    if (isAnimatingAToB) return; // Prevent scroll logic during A->B animation
    const handleScroll = () => {
      if (!containerRef.current) return;
      const scrollTop = window.scrollY;
      const windowHeight = window.innerHeight;
      const totalHeight = windowHeight * 3; // 3 sections of 100vh each
      // Calculate scroll progress (0 to 1)
      const progress = Math.max(0, Math.min(1, scrollTop / totalHeight));
      setScrollProgress(progress);
      // Calculate which section we're in (0-2)
      const sectionIndex = Math.floor(progress * 3);
      // Map section index to block position
      // Section 1: B, Section 2: C, Section 3: D
      let newActiveBlock;
      if (sectionIndex === 0) {
        newActiveBlock = 'B';
      } else if (sectionIndex === 1) {
        newActiveBlock = 'C';
      } else {
        newActiveBlock = 'D';
      }
      if (newActiveBlock !== scrollActiveBlock) {
        setScrollActiveBlock(newActiveBlock);
        set({ activeBlock: newActiveBlock });
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [scrollActiveBlock, set, isAnimatingAToB]);

  // Animation effect (to E or to C)
  useEffect(() => {
    if (!isAnimatingToE && !isAnimatingToC) return;
    let start;
    function animate(ts) {
      if (!start) start = ts;
      const elapsed = ts - start;
      const duration = 1000; // 1 second
      const progress = Math.min(1, elapsed / duration);
      setAnimationProgress(progress);
      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        if (isAnimatingToE) setIsAnimatingToE(false);
        if (isAnimatingToC) setIsAnimatingToC(false);
      }
    }
    animationRef.current = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationRef.current);
  }, [isAnimatingToE, isAnimatingToC]);

  // --- Animation effect for A->B on mount ---
  useEffect(() => {
    if (!isAnimatingAToB) return;
    let start;
    let delayTimeout;
    function animate(ts) {
      if (!start) start = ts;
      const elapsed = ts - start;
      const duration = 1000; // 1 second
      const progress = Math.min(1, elapsed / duration);
      setAnimationAToBProgress(progress);
      if (progress < 1) {
        animationAToBRef.current = requestAnimationFrame(animate);
      } else {
        setIsAnimatingAToB(false);
        setScrollActiveBlock('B');
        set({ activeBlock: 'B' });
      }
    }
    // Add 5 second delay before starting animation
    delayTimeout = setTimeout(() => {
      animationAToBRef.current = requestAnimationFrame(animate);
    }, 5000);
    return () => {
      clearTimeout(delayTimeout);
      cancelAnimationFrame(animationAToBRef.current);
    };
    // eslint-disable-next-line
  }, []);

  // Handler for button click (open About overlay and animate to E)
  const handleSection2ButtonClick = () => {
    setFromPosition(positions.C);
    setFromRotation(rotations.C);
    setAnimationProgress(0);
    setIsAnimatingToE(true);
    set({ activeBlock: 'E' });
    setBlockLockedToE(true);
    setAboutOverlayOpen(true);
  };

  // Handler for closing About overlay (animate back to C)
  const handleCloseAboutOverlay = () => {
    setFromPosition(positions.E);
    setFromRotation(rotations.E);
    setAnimationProgress(0);
    setIsAnimatingToC(true);
    set({ activeBlock: 'C' });
    setBlockLockedToE(false);
    setAboutOverlayOpen(false);
  };

  // Handler for closing Services overlay
  const handleCloseServicesOverlay = () => {
    setServicesOverlayState({ open: false, initialSection: null });
  };

  // Create position and rotation objects for interpolation
  const positions = {
    A: block1PositionA,
    B: block1PositionB,
    C: block1PositionC,
    D: block1PositionD,
    E: block1PositionE,
    F: block1PositionF
  };
  
  const rotations = {
    A: block1RotationA,
    B: block1RotationB,
    C: block1RotationC,
    D: block1RotationD,
    E: block1RotationE,
    F: block1RotationF
  };

  // In render logic, determine which block position to use
  let blockPositionProps = { positions, rotations, scrollProgress };
  if (isAnimatingAToB) {
    // Interpolate from A to B
    const interp = (a, b) => a.map((v, i) => v + (b[i] - v) * animationAToBProgress);
    // If still in delay, progress is 0, so block stays at A
    blockPositionProps = {
      positions: { B: interp(positions.A, positions.B) },
      rotations: { B: interp(rotations.A, rotations.B) },
      scrollProgress: 0, // always use B
      forceB: true
    };
  } else if (isAnimatingToE && fromPosition && fromRotation) {
    // Interpolate from C to E
    const toPosition = positions.E;
    const toRotation = rotations.E;
    const interp = (a, b) => a.map((v, i) => v + (b[i] - v) * animationProgress);
    blockPositionProps = {
      positions: { E: interp(fromPosition, toPosition) },
      rotations: { E: interp(fromRotation, toRotation) },
      scrollProgress: 0, // always use E
      forceE: true
    };
  } else if (isAnimatingToC && fromPosition && fromRotation) {
    // Interpolate from E to C
    const toPosition = positions.C;
    const toRotation = rotations.C;
    const interp = (a, b) => a.map((v, i) => v + (b[i] - v) * animationProgress);
    blockPositionProps = {
      positions: { C: interp(fromPosition, toPosition) },
      rotations: { C: interp(fromRotation, toRotation) },
      scrollProgress: 0, // always use C
      forceC: true
    };
  } else if (blockLockedToE) {
    blockPositionProps = {
      positions: { E: positions.E },
      rotations: { E: rotations.E },
      scrollProgress: 0,
      forceE: true
    };
  }

  // Prevent scrolling when overlay is open
  useEffect(() => {
    if (aboutOverlayOpen || servicesOverlayState.open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [aboutOverlayOpen, servicesOverlayState.open]);

  return (
    <div ref={containerRef} style={{ position: 'relative', width: '100vw', height: '300vh', overflow: 'auto' }}>
      {/* About Overlay */}
      <AboutOverlaySection open={aboutOverlayOpen} onClose={handleCloseAboutOverlay} />
      {/* Services Overlay */}
      <ServicesOverlaySection 
        open={servicesOverlayState.open} 
        onClose={handleCloseServicesOverlay}
        initialSection={servicesOverlayState.initialSection}
      >
        <h2>Unsere Services</h2>
        <p>Hier kannst du mehr über unsere Services erfahren. (Customize this content as needed.)</p>
      </ServicesOverlaySection>
      {/* Fixed 3D background */}
      <Canvas
        camera={{ position: [0, 3, 0], fov: 45 }}
        gl={{ alpha: true, antialias: true, powerPreference: 'high-performance', clearColor: [0, 0, 0, 1] }}
        className={styles.appAICanvas}
        style={{ position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', zIndex: 0 }}
      >
        <Suspense fallback={null}>
          <BlackBackground />
          <ambientLight intensity={0.5} />
          <pointLight position={[5, 5, 5]} intensity={1} />
          {blockPositionProps.forceB ? (
            <Block1Mesh position={blockPositionProps.positions.B} rotation={blockPositionProps.rotations.B} scale={[0.50, 0.50, 0.50]} />
          ) : blockPositionProps.forceE ? (
            <Block1Mesh position={blockPositionProps.positions.E} rotation={blockPositionProps.rotations.E} scale={[0.50, 0.50, 0.50]} />
          ) : blockPositionProps.forceC ? (
            <Block1Mesh position={blockPositionProps.positions.C} rotation={blockPositionProps.rotations.C} scale={[0.50, 0.50, 0.50]} />
          ) : (
            <ScrollInterpolatedBlock 
              positions={blockPositionProps.positions} 
              rotations={blockPositionProps.rotations} 
              scrollProgress={blockPositionProps.scrollProgress} 
              scale={[0.50, 0.50, 0.50]} 
            />
          )}
          {/* <WaterMatcapScene position={[0, 0, 0]} /> */}
          <WaterMatcapBackground position={[0, 0, 0]} />
        </Suspense>
      </Canvas>
      {/* Overlay content (not interactive) */}
      <div style={{ position: 'relative', zIndex: 2, pointerEvents: 'none' }}>
        <div style={{ height: '100vh', width: '100vw', overflowY: 'auto', maxHeight: '100vh', pointerEvents: 'none' }}>
          <Overlay style={{ pointerEvents: 'none' }} />
        </div>
        {/* Section 2 Hero (always rendered, but text hidden when overlay is open) */}
        <div style={{ height: '100vh', width: '100vw', position: 'relative', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#fff', fontSize: '2rem', pointerEvents: 'none' }}>
          <div className={styles.section2Hero} style={{ opacity: aboutOverlayOpen ? 0 : 1, transition: 'opacity 0.3s' }}>
            <div className={styles.section2Headline}>
              Wir revolutionieren die Art und Weise, wie smarte Unternehmen arbeiten.
            </div>
            <div className={styles.section2SubtextContainer}>
              <div className={styles.section2Subtext} style={{width: '100px'}}>
                <span className={styles.strike}>Du brauchst keine theoretische KI.</span>
              </div>
              <div className={styles.section2Subtext} style={{width: '150px'}}>
                <span className={styles.strike}>Du brauchst keine veralteten Systeme, die sich als KI tarnen.</span>
              </div>
              <div className={styles.section2Subtext} style={{width: '400px'}}>
                Was du brauchst, ist echte KI – KI, die heute funktioniert. KI, die deinem Unternehmen schon jetzt messbaren Mehrwert bringt – egal, wo du auf deiner digitalen Reise stehst.
              </div>
            </div>
            <button className={styles.section2Button} onClick={handleSection2ButtonClick} style={{ pointerEvents: 'auto' }}>ÜBER UNS</button>
          </div>
          <div className={styles.section2AbsText1}>BLCKS.TEAM<br />IS LOADING...</div>
          {/*<div className={styles.section2AbsText2}>FINISHED<br />LOADING</div>*/}
        </div>
        {/* Section 3 with 3 cards centered */}
        {/**
        <div
          style={{
            height: '100vh',
            width: '100vw',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            pointerEvents: 'none',
            background: 'rgba(0,0,0,0.6)',
            position: 'relative',
          }}
        >
          <div
            style={{
              opacity: servicesOverlayState.open ? 0 : 1,
              transition: 'opacity 0.3s',
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            }}
          >
            <div className={styles.section3Heading}>
              UNSERE SERVICES
            </div>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                gap: 0,
                flexWrap: 'wrap',
                maxWidth: '100vw',
                overflow: 'hidden',
                pointerEvents: 'auto',
              }}
            >
              <InfoCard
                title="KI-LÖSUNGEN"
                region="[SOLUTION]"
                description="Nicht nur ein bisschen KI – sondern maßgeschneiderte AI-Agenten, die Prozesse automatisieren, Kommunikation intelligent machen und dein Business wirklich smarter machen."
                onClick={() => setServicesOverlayState({ open: true, initialSection: 'AI SOLUTIONS' })}
              />
              <InfoCard
                title="AI-AS-A-SERVICE"
                region="[SOLUTION]"
                description="Smarte Automatisierung, sofort einsatzbereit. Die schnellste Abkürzung zur echten KI-Transformation – ohne IT-Chaos."
                onClick={() => setServicesOverlayState({ open: true, initialSection: 'X-AS-A-SERVICE' })}
              />
              <InfoCard
                title="CUSTOM AUTOMATION"
                region="[SOLUTION]"
                description="Weniger Aufwand. Mehr Wirkung. Wir analysieren, automatisieren und optimieren deine Abläufe – individuell auf dein Unternehmen zugeschnitten."
                onClick={() => setServicesOverlayState({ open: true, initialSection: 'PROFESSIONAL SERVICES' })}
              />
            </div>
          </div>
        </div>
        **/}
      </div>
      {/* Interactive UI (always clickable) */}
      <BlockPositionSwitcher activeBlock={activeBlock} setActiveBlock={val => set({ activeBlock: val })} />
    </div>
  );
} 