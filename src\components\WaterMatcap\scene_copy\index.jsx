import { create<PERSON>ort<PERSON>, use<PERSON><PERSON><PERSON>, useThree } from "@react-three/fiber";
import { useControls } from "leva";
import { useEffect, useMemo, useRef, useState } from "react";
import * as THREE from "three";

import { Cameras, useCameraStore, mainCamera, orbitCamera } from "./cameras2";
import { FLOW_SIM_SIZE, RAYMARCH_WATER_CENTER, RAYMARCH_FLOW_SIZE } from "./constants2";
import { DebugTextures } from "./debug-textures2";
import { Env } from "./env2";
import { renderFlow } from "./render-flow2";
import { useAssets } from "./use-assets2";
import { useLerpMouse } from "./use-lerp-mouse2";
import { useMaterials } from "./use-materials2";
import { useTargets } from "./use-targets2";

export function Scene({ opacity = 1, position = [0,0,0], ripples = [] }) {
  const activeCamera = useCameraStore((state) => state.camera);

  const [{ debugFloor, renderFloor, debugTextures, showFloorWireframe, wireframeColor, wireframeOpacity, showCornerMarkers }] = useControls(() => ({
    debugTextures: false,
    debugFloor: false,
    renderFloor: true,
    showFloorWireframe: {
      value: true,
      label: 'Show Floor Wireframe'
    },
    wireframeColor: {
      value: '#00ff00',
      label: 'Wireframe Color'
    },
    wireframeOpacity: {
      value: 1.0,
      label: 'Wireframe Opacity',
      min: 0.1,
      max: 1.0,
      step: 0.1
    },
    showCornerMarkers: {
      value: true,
      label: 'Show Corner Markers'
    }
  }));

  const targets = useTargets();
  const { flowFbo, orbeFlowFbo } = targets;
  const assets = useAssets();
  const materials = useMaterials(targets, assets);
  const { flowMaterial, raymarchMaterial, updateFlowCamera } = materials;

  updateFlowCamera(activeCamera);

  const [envMap, setEnvMap] = useState(null);

  // update environment
  useFrame(({ scene }) => {
    const env = scene.environment;
    if (!env) return;
    const currentEnv = raymarchMaterial.uniforms.envMap.value;
    if (currentEnv !== env) {
      raymarchMaterial.uniforms.envMap.value = env;
      const rotation = raymarchMaterial.uniforms.envMapRotation.value;

      const _e1 = new THREE.Euler();
      const _m1 = new THREE.Matrix4();

      _e1.copy(scene.environmentRotation);

      // accommodate left-handed frame
      _e1.x *= -1;
      _e1.y *= -1;
      _e1.z *= -1;

      _e1.y *= -1;
      _e1.z *= -1;

      rotation.setFromMatrix4(_m1.makeRotationFromEuler(_e1));

      setEnvMap(env);
    }
  });

  const [handlePointerMoveFloor, lerpMouseFloor, vRefsFloor] = useLerpMouse();

  const frameCount = useRef(0);

  const screenFbo = useMemo(() => {
    const fbo = new THREE.WebGLRenderTarget(10, 10, {
      depthBuffer: true,
      depthTexture: new THREE.DepthTexture(10, 10)
    });

    if (typeof window !== "undefined") {
      fbo.setSize(window.innerWidth, window.innerHeight);
    }

    return fbo;
  }, []);

  const size = useThree((state) => state.size);

  useEffect(() => {
    screenFbo.setSize(size.width, size.height);
  }, [size, screenFbo]);

  useEffect(() => {
    // for dev reasons, reset frame count when material gets reloaded
    frameCount.current = 0;
  }, [flowMaterial, flowFbo]);

  const flowScene = useMemo(() => new THREE.Scene(), []);

  // Update flow simulation
  useFrame(({ gl, scene, clock }, delta) => {
    const t = clock.getElapsedTime();

    let s = Math.sin(t * 1) - 0.2;
    if (s < 0) {
      s = 0;
    } else {
      s = 1;
    }

    let triangleHeight = s * 0.4 + 0.5;

    flowMaterial.uniforms.uTriangleHeight.value = triangleHeight;

    if (geoTryRef.current) {
      geoTryRef.current.scale.y = (triangleHeight - 0.5) * 3;
    }

    const shouldDoubleRender = delta > 1 / 75;

    gl.setRenderTarget(debugTextures ? screenFbo : null);

    // floor
    lerpMouseFloor(shouldDoubleRender ? delta / 2 : delta);
    renderFlow(
      gl,
      activeCamera,
      flowScene,
      clock,
      flowMaterial,
      flowFbo,
      vRefsFloor,
      frameCount.current
    );

    if (shouldDoubleRender) {
      // floor
      lerpMouseFloor(delta / 2);
      renderFlow(
        gl,
        activeCamera,
        flowScene,
        clock,
        flowMaterial,
        flowFbo,
        vRefsFloor,
        frameCount.current
      );
    }

    raymarchMaterial.uniforms.uFlowSize.value = FLOW_SIM_SIZE / 2;

    // --- Ripple uniform update ---
    if (!raymarchMaterial.uniforms.ripplePositions) {
      raymarchMaterial.uniforms.ripplePositions = { value: Array(5).fill().map(() => new THREE.Vector2(0, 0)) };
    }
    if (!raymarchMaterial.uniforms.rippleTimes) {
      raymarchMaterial.uniforms.rippleTimes = { value: Array(5).fill(0) };
    }
    // Prepare up to 5 ripples
    const now = performance.now();
    const ripplePositions = Array(5).fill().map(() => new THREE.Vector2(0, 0));
    const rippleTimes = Array(5).fill(0);
    ripples.slice(-5).forEach((r, i) => {
      ripplePositions[i].set(r.x, r.z);
      rippleTimes[i] = (now - r.startTime) * 0.001; // seconds since triggered
    });
    raymarchMaterial.uniforms.ripplePositions.value = ripplePositions;
    raymarchMaterial.uniforms.rippleTimes.value = rippleTimes;

    gl.render(scene, activeCamera);
    frameCount.current++;
  }, 1);

  const geoTryRef = useRef(null);

  useEffect(() => {
    // Set a custom uniform to indicate camera mode
    if (flowMaterial.uniforms.uCameraMode === undefined) {
      flowMaterial.uniforms.uCameraMode = { value: 0 };
    }
    // 0 = main, 1 = orbit
    flowMaterial.uniforms.uCameraMode.value = (activeCamera === orbitCamera) ? 1 : 0;
  }, [activeCamera, flowMaterial]);

  return (
    <group position={position}>
      {/* Flow simulation (floor) */}
      {createPortal(
        <mesh>
          {/* Has to be 2x2 to fill the screen using pos attr */}
          <planeGeometry args={[2, 2]} />
          <primitive object={flowMaterial} />
        </mesh>,
        flowScene
      )}

      {/* Pointer events (floor) */}
      <mesh
        visible={debugFloor}
        rotation={[Math.PI / -2, 0, 0]}
        position={[0, 0, 0]}
        onPointerMove={handlePointerMoveFloor}
        onPointerOver={() => (vRefsFloor.shouldReset = true)}
      >
        <planeGeometry args={[FLOW_SIM_SIZE, FLOW_SIM_SIZE]} />
        <meshBasicMaterial map={flowFbo.read.texture} />
      </mesh>

      {/* Raymarched water (floor) */}
      <mesh
        rotation={[Math.PI / -2, 0, 0]}
        visible={renderFloor}
        position={RAYMARCH_WATER_CENTER}
        onPointerMove={handlePointerMoveFloor}
        onPointerOver={() => (vRefsFloor.shouldReset = true)}
      >
        <planeGeometry args={[RAYMARCH_FLOW_SIZE * 2, RAYMARCH_FLOW_SIZE]} />
        <primitive object={raymarchMaterial} />
      </mesh>

      {/* Wireframe overlay for floor visualization */}
      {showFloorWireframe && (
        <group>
          {/* Main wireframe mesh */}
          <mesh
            rotation={[Math.PI / -2, 0, 0]}
            position={[
              RAYMARCH_WATER_CENTER.x,
              RAYMARCH_WATER_CENTER.y + 0.05, // More offset to ensure visibility
              RAYMARCH_WATER_CENTER.z
            ]}
          >
            <planeGeometry args={[RAYMARCH_FLOW_SIZE * 2, RAYMARCH_FLOW_SIZE, 20, 10]} />
            <meshBasicMaterial
              color={wireframeColor}
              wireframe={true}
              transparent={true}
              opacity={wireframeOpacity}
              side={THREE.DoubleSide}
              depthTest={false}
              depthWrite={false}
            />
          </mesh>

          {/* Border outline for better visibility */}
          <lineSegments
            rotation={[Math.PI / -2, 0, 0]}
            position={[
              RAYMARCH_WATER_CENTER.x,
              RAYMARCH_WATER_CENTER.y + 0.06,
              RAYMARCH_WATER_CENTER.z
            ]}
          >
            <edgesGeometry args={[new THREE.PlaneGeometry(RAYMARCH_FLOW_SIZE * 2, RAYMARCH_FLOW_SIZE)]} />
            <lineBasicMaterial
              color={wireframeColor}
              linewidth={3}
              transparent={true}
              opacity={wireframeOpacity}
            />
          </lineSegments>

          {/* Corner markers for reference */}
          {showCornerMarkers && [
            [-RAYMARCH_FLOW_SIZE, 0, -RAYMARCH_FLOW_SIZE/2],
            [RAYMARCH_FLOW_SIZE, 0, -RAYMARCH_FLOW_SIZE/2],
            [-RAYMARCH_FLOW_SIZE, 0, RAYMARCH_FLOW_SIZE/2],
            [RAYMARCH_FLOW_SIZE, 0, RAYMARCH_FLOW_SIZE/2]
          ].map((pos, index) => (
            <mesh
              key={index}
              position={[
                RAYMARCH_WATER_CENTER.x + pos[0],
                RAYMARCH_WATER_CENTER.y + 0.1,
                RAYMARCH_WATER_CENTER.z + pos[2]
              ]}
            >
              <sphereGeometry args={[0.05, 8, 8]} />
              <meshBasicMaterial
                color={wireframeColor}
                transparent={true}
                opacity={wireframeOpacity * 0.8}
              />
            </mesh>
          ))}
        </group>
      )}

      {/*<mesh
      rotation={[Math.PI / 2, 0, 0]}
      position={[0, -0.2, 0]}
      scale={[0.9, 0.1, 0.9]}
      >
        <primitive object={assets.pyramid.geometry} />
        <meshStandardMaterial
          color="black"
          metalness={1}
          roughness={0.2}
          envMap={envMap}
        />
      </mesh>*/}

      {/*<mesh position={[0, 0.35, 0]} scale={[0.35, 0.35, 0.35]}>
        <primitive object={assets.cubeRoundWhole} />
      </mesh> */}

      {/* <mesh position={[0, 0.2, 0]} rotation={[-Math.PI / 2, 0, 0]}>
        <meshMatcapMaterial matcap={assets.matcap} />
        <planeGeometry args={[10, 10]} />
      </mesh> */}

      {/* <pointLight decay={2} intensity={200} position={[-8, 0.5, -1.7]} />
      <pointLight decay={2} intensity={200} position={[2, 0.5, -1.7]} />
      <pointLight decay={2} intensity={200} position={[0, 0.5, 2]} /> */}

      <Cameras />
      <Env />

      {/* Display textures */}
      {debugTextures && (
        <DebugTextures
          textures={{
            flow: flowFbo.read.texture,
            pyramidFlow: orbeFlowFbo.read.texture,
            screen: screenFbo.texture,
            screenDepth: screenFbo.depthTexture
          }}
        />
      )}
    </group>
  );
}