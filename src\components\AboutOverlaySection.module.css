.overlayBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.25);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlayContent {
  background: #fff;
  color: #111;
  border-radius: 16px;
  padding: 2rem;
  min-width: 320px;
  min-height: 200px;
  box-shadow: 0 4px 32px rgba(0,0,0,0.15);
  position: relative;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}

.closeButton {
  position: absolute;
  top: 16px;
  right: 16px;
  background: #eee;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  font-size: 20px;
  cursor: pointer;
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}
.closeButton:hover, .closeButton:focus {
  background: #ddd;
}

.title {
  margin-top: 0;
} 