import { useRef, useMemo, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useThree } from '@react-three/fiber'
import * as THREE from 'three'

// Helper function to convert hex color to RGB array
const hexToRgb = (hex) => {
  // Make sure hex is a string
  if (typeof hex !== 'string') {
    console.warn('Color value is not a string:', hex);
    return [1, 1, 1]; // Default to white
  }
  
  // Remove # if present
  hex = hex.replace(/^#/, '')
  
  // Parse hex values
  let r, g, b
  if (hex.length === 3) {
    // #RGB format
    r = parseInt(hex.charAt(0) + hex.charAt(0), 16) / 255
    g = parseInt(hex.charAt(1) + hex.charAt(1), 16) / 255
    b = parseInt(hex.charAt(2) + hex.charAt(2), 16) / 255
  } else if (hex.length === 6) {
    // #RRGGBB format
    r = parseInt(hex.substring(0, 2), 16) / 255
    g = parseInt(hex.substring(2, 4), 16) / 255
    b = parseInt(hex.substring(4, 6), 16) / 255
  } else {
    // Default to white if invalid format
    console.warn('Invalid hex color format:', hex);
    return [1, 1, 1]
  }
  
  return [r, g, b]
}

// A customizable version of the Liquid Metal shader
export default function CustomizableShaderBackground({
  speed = 1.0,
  colorPrimary = '#FFCC99', // Default color in hex
  colorAccent = '#99CCFF',  // Default color in hex
  contrast = 0.6,
  brightness = 1.0,
  scale = 2.0,
}) {
  const meshRef = useRef()
  const { viewport } = useThree()
  
  // Convert hex colors to RGB arrays with proper error handling
  const primaryRgb = useMemo(() => {
    try {
      return hexToRgb(colorPrimary);
    } catch (err) {
      console.error('Error parsing primary color:', err);
      return [1.0, 0.8, 0.6]; // Fallback to default
    }
  }, [colorPrimary]);
  
  const accentRgb = useMemo(() => {
    try {
      return hexToRgb(colorAccent);
    } catch (err) {
      console.error('Error parsing accent color:', err);
      return [0.6, 0.8, 1.0]; // Fallback to default
    }
  }, [colorAccent]);
  
  // Define our shader material uniforms with customizable values
  const uniforms = useMemo(() => ({
    iTime: { value: 0 },
    iResolution: { value: new THREE.Vector3(window.innerWidth, window.innerHeight, 1) },
    uSpeed: { value: speed },
    uColorPrimary: { value: new THREE.Vector3(...primaryRgb) },
    uColorAccent: { value: new THREE.Vector3(...accentRgb) },
    uContrast: { value: contrast },
    uBrightness: { value: brightness },
    uScale: { value: scale },
  }), [speed, primaryRgb, accentRgb, contrast, brightness, scale])
  
  // Update uniform values when props change
  useEffect(() => {
    uniforms.uSpeed.value = speed
    uniforms.uColorPrimary.value.set(...primaryRgb)
    uniforms.uColorAccent.value.set(...accentRgb)
    uniforms.uContrast.value = contrast
    uniforms.uBrightness.value = brightness
    uniforms.uScale.value = scale
  }, [uniforms, speed, primaryRgb, accentRgb, contrast, brightness, scale])
  
  // Update uniforms on window resize
  useEffect(() => {
    const handleResize = () => {
      uniforms.iResolution.value.set(window.innerWidth, window.innerHeight, 1)
    }
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [uniforms])
  
  // Enhanced animation method to ensure continuous animation
  useEffect(() => {
    let animationId
    let lastTime = 0
    
    const animate = (time) => {
      if (!lastTime) lastTime = time
      const deltaTime = (time - lastTime) * 0.001 // Convert to seconds
      lastTime = time
      
      if (meshRef.current && uniforms.iTime) {
        uniforms.iTime.value += deltaTime * uniforms.uSpeed.value
      }
      
      animationId = requestAnimationFrame(animate)
    }
    
    animationId = requestAnimationFrame(animate)
    
    return () => {
      cancelAnimationFrame(animationId)
      lastTime = 0
    }
  }, [uniforms])
  
  // Update frame with Three.js's animation loop as well (belt and suspenders approach)
  useFrame((state) => {
    if (meshRef.current) {
      // Instead of setting directly from clock time, increment slowly to ensure animation
      uniforms.iTime.value += state.clock.getDelta() * uniforms.uSpeed.value
    }
  })
  
  // This is the vertex shader
  const vertexShader = `
    varying vec2 vUv;
    
    void main() {
      vUv = uv;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `
  
  // This is the fragment shader - converted from the Shadertoy code with customizable parameters
  const fragmentShader = `
    uniform float iTime;
    uniform vec3 iResolution;
    uniform float uSpeed;
    uniform vec3 uColorPrimary;
    uniform vec3 uColorAccent;
    uniform float uContrast;
    uniform float uBrightness;
    uniform float uScale;
    varying vec2 vUv;
    
    #define PI  3.141592654
    #define TAU (2.0*PI)
    
    void rot(inout vec2 p, float a) {
      float c = cos(a);
      float s = sin(a);
      p = vec2(c*p.x + s*p.y, -s*p.x + c*p.y);
    }
    
    float hash(in vec2 co) {
      return fract(sin(dot(co.xy ,vec2(12.9898,58.233))) * 13758.5453);
    }
    
    vec2 hash2(vec2 p) {
      p = vec2(dot(p,vec2(127.1,311.7)), dot(p,vec2(269.5,183.3)));
      return fract(sin(p)*18.5453);
    }
    
    float psin(float a) {
      return 0.5 + 0.5*sin(a);
    }
    
    float tanh_approx(float x) {
      float x2 = x*x;
      return clamp(x*(27.0 + x2)/(27.0+9.0*x2), -1.0, 1.0);
    }
    
    float onoise(vec2 x) {
      x *= 0.5;
      float a = sin(x.x);
      float b = sin(x.y);
      float c = mix(a, b, psin(TAU*tanh_approx(a*b+a+b)));
      
      return c;
    }
    
    float vnoise(vec2 x) {
      vec2 i = floor(x);
      vec2 w = fract(x);
        
      // quintic interpolation
      vec2 u = w*w*w*(w*(w*6.0-15.0)+10.0);
    
      float a = hash(i+vec2(0.0,0.0));
      float b = hash(i+vec2(1.0,0.0));
      float c = hash(i+vec2(0.0,1.0));
      float d = hash(i+vec2(1.0,1.0));
        
      float k0 =   a;
      float k1 =   b - a;
      float k2 =   c - a;
      float k3 =   d - c + a - b;
    
      float aa = mix(a, b, u.x);
      float bb = mix(c, d, u.x);
      float cc = mix(aa, bb, u.y);
      
      return k0 + k1*u.x + k2*u.y + k3*u.x*u.y;
    }
    
    float fbm1(vec2 p) {
      vec2 op = p;
      const float aa = 0.45;
      const float pp = 2.03;
      const vec2 oo = -vec2(1.23, 1.5);
      const float rr = 1.2;
      
      float h = 0.0;
      float d = 0.0;
      float a = 1.0;
      
      for (int i = 0; i < 5; ++i) {
        h += a*onoise(p);
        d += (a);
        a *= aa;
        p += oo;
        p *= pp;
        rot(p, rr);
      }
      
      return mix((h/d), -0.5*(h/d), pow(vnoise(0.9*op), 0.25));
    }
    
    float fbm2(vec2 p) {
      vec2 op = p;
      const float aa = 0.45;
      const float pp = 2.03;
      const vec2 oo = -vec2(1.23, 1.5);
      const float rr = 1.2;
      
      float h = 0.0;
      float d = 0.0;
      float a = 1.0;
      
      for (int i = 0; i < 7; ++i) {
        h += a*onoise(p);
        d += (a);
        a *= aa;
        p += oo;
        p *= pp;
        rot(p, rr);
      }
      
      return mix((h/d), -0.5*(h/d), pow(vnoise(0.9*op), 0.25));
    }
    
    float fbm3(vec2 p) {
      vec2 op = p;
      const float aa = 0.45;
      const float pp = 2.03;
      const vec2 oo = -vec2(1.23, 1.5);
      const float rr = 1.2;
      
      float h = 0.0;
      float d = 0.0;
      float a = 1.0;
      
      for (int i = 0; i < 3; ++i) {
        h += a*onoise(p);
        d += (a);
        a *= aa;
        p += oo;
        p *= pp;
        rot(p, rr);
      }
      
      return mix((h/d), -0.5*(h/d), pow(vnoise(0.9*op), 0.25));
    }
    
    
    float warp(vec2 p) {
      vec2 v = vec2(fbm1(p), fbm1(p+0.7*vec2(1.0, 1.0)));
      
      rot(v, 1.0+iTime*0.1*uSpeed);
      
      vec2 vv = vec2(fbm2(p + 3.7*v), fbm2(p + -2.7*v.yx+0.7*vec2(1.0, 1.0)));
    
      rot(vv, -1.0+iTime*0.21315*uSpeed);
        
      return fbm3(p + 1.4*vv);
    }
    
    float height(vec2 p) {
      float a = 0.005*iTime*uSpeed;
      p += 5.0*vec2(cos(a), sin(a));
      p *= uScale;
      p += 13.0;
      float h = warp(p);
      float rs = 3.0;
      return 0.35*tanh_approx(rs*h)/rs;
    }
    
    vec3 normal(vec2 p) {
      vec2 eps = -vec2(2.0/iResolution.y, 0.0);
      
      vec3 n;
      
      n.x = height(p + eps.xy) - height(p - eps.xy);
      n.y = 2.0*eps.x;
      n.z = height(p + eps.yx) - height(p - eps.yx);
      
      return normalize(n);
    }
    
    vec3 postProcess(vec3 col, vec2 q)  {
      col = pow(clamp(col,0.0,1.0), vec3(0.75));
      col = col*uContrast+0.4*col*col*(3.0-2.0*col);  // contrast
      col = mix(col, vec3(dot(col, vec3(0.33))), -0.4);  // saturation
      col *= 0.5+0.5*pow(19.0*q.x*q.y*(1.0-q.x)*(1.0-q.y),0.7);  // vignetting
      col *= uBrightness; // brightness adjustment
      return col;
    }
    
    void main() {
      vec2 fragCoord = vUv * iResolution.xy;
      vec2 q = fragCoord/iResolution.xy;
      vec2 p = -1. + 2. * q;
      p.x*=iResolution.x/iResolution.y;
     
      vec3 lp1 = vec3(0.9, -0.5, 0.8);
      vec3 lp2 = vec3(-0.9, -1.5, 0.9);
    
      float h = height(p);
      vec3 pp = vec3(p.x, h, p.y);
      float ll1 = length(lp1.xz - pp.xz);
      vec3 ld1 = normalize(lp1 - pp);
      vec3 ld2 = normalize(lp2 - pp);
     
      vec3 n = normal(p);
      float diff1 = max(dot(ld1, n), 0.0);
      float diff2 = max(dot(ld2, n), 0.0);
     
      vec3 baseCol = uColorPrimary;
    
      float oh = height(p + ll1*0.05*normalize(ld1.xz));
      const float level0 = 0.0;
      const float level1 = 0.125;
      // VERY VERY fake shadows + hilight
      vec3 scol = baseCol*(smoothstep(level0, level1, h) - smoothstep(level0, level1, oh));
    
      vec3 col = vec3(0.0);
      col += baseCol*pow(diff1, 6.0);
      col += 0.1*baseCol*pow(diff1, 1.5);
      col += 0.15*uColorAccent*pow(diff2, 8.0);
      col += 0.015*uColorAccent*pow(diff2, 2.0);
      col += scol*0.5;
    
      col = postProcess(col, q);
      
      gl_FragColor = vec4(col, 1.0);
    }
  `

  return (
    // Use a quad that fills the camera frustum
    <mesh 
      ref={meshRef} 
      scale={[viewport.width, viewport.height, 1]}
      position={[0, 0, 0]}
    >
      <planeGeometry args={[1, 1]} />
      <shaderMaterial 
        uniforms={uniforms}
        vertexShader={vertexShader}
        fragmentShader={fragmentShader}
        toneMapped={false}
      />
    </mesh>
  )
}