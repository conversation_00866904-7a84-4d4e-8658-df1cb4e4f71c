/* Import Satoshi font */
@import url('https://api.fontshare.com/v2/css?f[]=satoshi@900,700,500,301,300,400&display=swap');

/* Import local Telegraf fonts */
@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@import url('https://rsms.me/inter/inter.css');

* {
  box-sizing: border-box;
}

html,
body,
#root {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  background: #f0f0f0;
  color: black;
  font-family: 'Inter';
}

a {
  color: black;
}

a {
  pointer-events: all;
  color: black;
  text-decoration: none;
}

svg {
  fill: black;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

canvas {
  opacity: 0;
  touch-action: none;
  animation: fade-in 5s ease 1s forwards;
}

.back-button {
  position: absolute;
  top: 64px;
  left: 64px;
  background: white;
  padding: 8px 16px 8px 8px;
  border-radius: 8px;
  font-size: 0.8em;
  z-index: 100;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #efefef;
}

.back-button svg {
  width: 18px;
  height: 18px;
  transform: translateX(-1px);
}