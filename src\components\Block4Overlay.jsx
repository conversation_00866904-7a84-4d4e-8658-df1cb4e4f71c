import React, { useState } from 'react';
import styles from './Block4Overlay.module.css';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

export default function Block4Overlay({ onClose }) {
  const [statHovered1, setStatHovered1] = useState(false);
  const [statHovered2, setStatHovered2] = useState(false);
  const [statHovered3, setStatHovered3] = useState(false);

  return (
    <>
      <div className={styles.arrowRight}>
        <img src="/arrow_long.svg" alt="Arrow Right" width={40} height={40} />
      </div>
      <div className={styles.arrowLeft}>
        <img src="/arrow_long.svg" alt="Arrow Left" width={40} height={40} className={styles.arrowLeftImg} />
      </div>
      <button onClick={onClose} className={styles.closeButton} aria-label="Close overlay">CLOSE</button>
      <div className={styles.overlay}>
        <div className={styles.contentWrapper}>
          <h1 className={styles.title}>KI Automation: Ihre Vorteile auf einen Blick</h1>
          <p className={styles.description}>
            Automatisierte Abläufe reduzieren den manuellen Aufwand drastisch und ermöglichen so Einsparungen von bis zu 70 % der Arbeitszeit in vielen Bereichen. Dadurch werden Mitarbeitende von monotonen Routineaufgaben entlastet und können sich auf wertvollere Tätigkeiten konzentrieren. Gleichzeitig sinkt die Fehlerquote, denn KI-gesteuerte Prozesse sind weniger fehleranfällig und erhöhen so die Zuverlässigkeit und Qualität.
          </p>
          <h2 className={styles.infoHeading}>Typische Einsatzbereiche und Technologien der KI Automation:</h2>
          <div className={styles.infoRow}>
            <div className={styles.infoBlock}>
              <div className={styles.infoLabel}>KI-Agenten und Conversational AI</div>
              <div className={styles.infoValue}>Sie übernehmen rund um die Uhr den Kundenservice, beantworten Anfragen automatisch und senken so die Kosten pro Kontakt.</div>
            </div>
            <div className={styles.infoBlock}>
              <div className={styles.infoLabel}>KI-gestütztes Marketing & SEO</div>
              <div className={styles.infoValue}>
                Durch intelligente Analyse, personalisierte Kampagnen und automatisierte SEO-Optimierung steigern diese Systeme die Sichtbarkeit und Conversion-Raten deutlich.
              </div>
            </div>
            <div className={styles.infoBlock}>
              <div className={styles.infoLabel}>Automatisierte Dokumentenverarbeitung</div>
              <div className={styles.infoValue}>Mithilfe von OCR und Machine Learning werden Rechnungen, Verträge und andere Dokumente schnell und fehlerfrei verarbeitet – das spart Zeit und senkt die Bearbeitungskosten.</div>
            </div>
          </div>
          <div className={styles.statsGrid}>
            <div
              className={styles.statCard}
              onMouseEnter={() => setStatHovered1(true)}
              onMouseLeave={() => setStatHovered1(false)}
            >
              <div className={styles.statTopRow}>
                <span className={styles.statIcon}>
                  <img src="/clock.svg" alt="Logo" width={20} height={20} />
                </span>
                <div className={styles.statHeading}>70 % weniger Zeitaufwand für Routineaufgaben</div>
                <div className={styles.statHeading2}>KI Automation übernimmt repetitive Aufgaben – und gibt Mitarbeitenden bis zu 70 % ihrer Zeit zurück. <br/>Das schafft Raum für produktivere, strategische Arbeit.</div>
              </div>
              <div className={styles.statDesc}>Arbeitszeit für Routineaufgaben</div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                <div
                  className={styles.statNumber}
                  style={{ marginBottom: 0, marginRight: 12 }}
                >
                  {statHovered1 ? '2' : '72'}<span className={styles.statUnit}>%</span>
                </div>
                <span className={statHovered1 ? styles.statTagHover + ' ' + styles.statTag : styles.statTag}>
                  {statHovered1 ? '-70%' : '+ 2%'}<span className={styles.statTagArrow}>→</span>
                </span>
              </div>
              <div className={styles.statChartContainer}>
                <Line
                  data={{
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [
                      {
                        label: 'Closures',
                        data: [70, 76, 73, 75, 70, 72],
                        borderColor: statHovered1 ? 'transparent' : '#fff',
                        borderWidth: 2,
                        backgroundColor: 'rgba(75,192,192,0.2)',
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 0,
                      },
                      ...(statHovered1 ? [{
                        label: 'Vergleich',
                        data: [70, 76, 73, 75, 40, 2],
                        borderColor: '#c3ffc6',
                        borderWidth: 2,
                        backgroundColor: 'rgba(75,192,192,0.0)',
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 0,
                      }] : [])
                    ],
                  }}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: { display: false },
                      title: { display: false },
                    },
                    maintainAspectRatio: false,
                    animation: false,
                    scales: {
                      x: {
                        grid: { display: false, drawBorder: false },
                        ticks: { color: '#aaa', font: { family: 'Inter' }, padding: 10 }
                      },
                      y: {
                        grid: { display: false, drawBorder: false },
                        ticks: {
                          color: '#aaa',
                          font: { family: 'Inter' },
                          callback: function(value) { return value + '%'; },
                          stepSize: 25,
                          min: 0,
                          max: 100,
                        },
                        beginAtZero: true,
                      },
                    },
                  }}
                />
              </div>
            </div>
            <div
              className={styles.statCard}
              onMouseEnter={() => setStatHovered2(true)}
              onMouseLeave={() => setStatHovered2(false)}
            >
              <div className={styles.statTopRow}>
                <span className={styles.statIcon}>
                  <img src="/settings.svg" alt="Logo" width={20} height={20} />
                </span>
                <div className={styles.statHeading}>40 % mehr Output bei gleichem Aufwand</div>
                <div className={styles.statHeading2}>Durch den gezielten Einsatz von KI Automation steigt die Produktivität im Schnitt um 40 %. Teams schaffen mehr – ohne mehr zu arbeiten.</div>
              </div>
              <div className={styles.statDesc}>Automatisierte Geschäftsprozesse</div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                <div
                  className={styles.statNumber}
                  style={{ marginBottom: 0, marginRight: 12 }}
                >
                  {statHovered2 ? '140' : '100'}<span className={styles.statUnit}>%</span>
                </div>
                <span className={statHovered2 ? styles.statTagHover + ' ' + styles.statTag : styles.statTag}>
                  {statHovered2 ? '+40%' : '- 2%'}<span className={styles.statTagArrow2}>→</span>
                </span>
              </div>
              <div className={styles.statChartContainer}>
                <Line
                  data={{
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [
                      {
                        label: 'Automatisierung',
                        data: [100, 101, 99, 100, 101, 100],
                        borderColor: statHovered2 ? 'transparent' : '#fff',
                        borderWidth: 2,
                        backgroundColor: 'rgba(75,192,192,0.2)',
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 0,
                      },
                      ...(statHovered2 ? [{
                        label: 'Vergleich',
                        data: [98, 102, 100, 96, 120, 140],
                        borderColor: '#c3ffc6',
                        borderWidth: 2,
                        backgroundColor: 'rgba(75,192,192,0.0)',
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 0,
                      }] : [])
                    ],
                  }}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: { display: false },
                      title: { display: false },
                    },
                    maintainAspectRatio: false,
                    animation: false,
                    scales: {
                      x: {
                        grid: { display: false, drawBorder: false },
                        ticks: { color: '#aaa', font: { family: 'Inter' }, padding: 10 }
                      },
                      y: {
                        grid: { display: false, drawBorder: false },
                        ticks: {
                          color: '#aaa',
                          font: { family: 'Inter' },
                          callback: function(value) { return value + '%'; },
                          stepSize: 50,
                          min: 50,
                          max: 200,
                        },
                        beginAtZero: true,
                      },
                    },
                  }}
                />
              </div>
            </div>
            <div
              className={styles.statCard}
              onMouseEnter={() => setStatHovered3(true)}
              onMouseLeave={() => setStatHovered3(false)}
            >
              <div className={styles.statTopRow}>
                <span className={styles.statIcon}>
                  <img src="/clock.svg" alt="Logo" width={20} height={20} />
                </span>
                <div className={styles.statHeading}>Wartezeit bei Kundenanfragen</div>
                <div className={styles.statHeading2}>KI-Assistenten beantworten Anfragen in Echtzeit – statt stundenlang zu warten, erhalten Kund:innen sofort Hilfe. <br/>Das steigert die Kundenzufriedenheit und entlastet dein Team.</div>
              </div>
              <div className={styles.statDesc}>Wartezeit</div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                <div
                  className={styles.statNumber}
                  style={{ marginBottom: 0, marginRight: 12 }}
                >
                  {statHovered3 ? '<2' : '720'}<span className={styles.statUnit}>min</span>
                </div>
                <span className={statHovered3 ? styles.statTagHover + ' ' + styles.statTag : styles.statTag}>
                  {statHovered3 ? '- 718min' : '+ 20min'}<span className={styles.statTagArrow}>→</span>
                </span>
              </div>
              <div className={styles.statChartContainer}>
                <Line
                  data={{
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [
                      {
                        label: 'Produktivität',
                        data: [720, 720, 720, 720, 700, 720],
                        borderColor: statHovered3 ? 'transparent' : '#fff',
                        borderWidth: 2,
                        backgroundColor: 'rgba(75,192,192,0.2)',
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 0,
                      },
                      ...(statHovered3 ? [{
                        label: 'Vergleich',
                        data: [720, 715, 725, 712, 300, 2],
                        borderColor: '#c3ffc6',
                        borderWidth: 2,
                        backgroundColor: 'rgba(75,192,192,0.0)',
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 0,
                      }] : [])
                    ],
                  }}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: { display: false },
                      title: { display: false },
                    },
                    maintainAspectRatio: false,
                    animation: false,
                    scales: {
                      x: {
                        grid: { display: false, drawBorder: false },
                        ticks: { color: '#aaa', font: { family: 'Inter' }, padding: 10 }
                      },
                      y: {
                        grid: { display: false, drawBorder: false },
                        ticks: {
                          color: '#aaa',
                          font: { family: 'Inter' },
                          callback: function(value) { return value + 'min'; },
                          stepSize: 100,
                          min: 0,
                          max: 800,
                        },
                        beginAtZero: true,
                      },
                    },
                  }}
                />
              </div>
            </div>
          </div>
          <div className={styles.divider}></div>
          <h2 className={styles.bigTitle}>Wie Unternehmen mit KI Automation gewinnen</h2>
          <p className={styles.bigDescription}>
            Entdecken Sie anhand realer Case Studies, wie Unternehmen verschiedenster Branchen mit KI Automation ihre Prozesse effizienter gestalten, Kosten senken und nachhaltiges Wachstum erzielen. Unsere Beispiele zeigen, wie smarte Technologien den Unterschied machen — von automatisierter Kundenkommunikation über intelligente Marketingstrategien bis hin zu optimierten Finanzprozessen. Lassen Sie sich inspirieren, wie auch Ihr Unternehmen von den Vorteilen der KI profitieren kann.
          </p>
          <div className={styles.newsGrid}>
            <div className={styles.newsCard}>
              <div className={styles.newsTop}>
                <div className={styles.newsTag}>CASESTUDY</div>
                <div className={styles.newsTitle}>Effiziente Rechnungsverarbeitung durch AI Automation</div>
              </div>
              <div className={styles.newsDate}>November 6, 2024</div>
              <a className={styles.newsButton} href="https://live.handelsblatt.com/ki-im-einsatz-so-schaffen-unternehmen-echten-mehrwert-mit-automatisierung/" target="_blank" rel="noopener noreferrer">Zur Casestudy</a>
            </div>
            <div className={styles.newsCard}>
              {/* Empty card for Migration from SWIFT standards */}
            </div>
            <div className={styles.newsCard}>
              <div className={styles.newsTop}>
                <div className={styles.newsTag}>CASESTUDY</div>
                <div className={styles.newsTitle}>24/7 Kundenservice mit intelligenten Chatbots</div>
              </div>
              <div className={styles.newsDate}>April 29, 2025</div>
              <a className={styles.newsButton} href="https://www.informatik-aktuell.de/betrieb/kuenstliche-intelligenz/hyperautomation-durch-ki-chancen-und-risiken.html" target="_blank" rel="noopener noreferrer">Zur Casestudy</a>
            </div>
            <div className={styles.newsCard}>
              <div className={styles.newsTop}>
                <div className={styles.newsTag}>CASESTUDY</div>
                <div className={styles.newsTitle}>Steigerung der Conversion-Rate durch KI-gestütztes Marketing</div>
              </div>
              <div className={styles.newsDate}>August 7, 2024</div>
              <a className={styles.newsButton} href="https://www.it-p.de/blog/ki-bpa-prozesse/" target="_blank" rel="noopener noreferrer">Zur Casestudy</a>
            </div>
            <div className={styles.newsCard}>
              <div className={styles.newsTop}>
                <div className={styles.newsTag}>CASESTUDY</div>
                <div className={styles.newsTitle}>Optimierte Dokumentenverwaltung im Finanzwesen</div>
              </div>
              <div className={styles.newsDate}>April 1, 2025</div>
              <a className={styles.newsButton} href="https://industriemagazin.at/fertigen/einsatz-der-ki-in-der-industrie/" target="_blank" rel="noopener noreferrer">Zur Casestudy</a>
            </div>
            <div className={styles.newsCard}>
              {/* Empty card for All the latest news */}
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 