// --- <PERSON><PERSON> inlined get-surface.glsl ---
// --- <PERSON><PERSON> inlined getSceneHit and dependencies ---
const float PI = 3.14159265359;

// --- <PERSON><PERSON> inlined 2D simplex noise (snoise2) ---
vec3 mod289(vec3 x) {
  return x - floor(x * (1.0 / 289.0)) * 289.0;
}
vec2 mod289(vec2 x) {
  return x - floor(x * (1.0 / 289.0)) * 289.0;
}
vec3 permute(vec3 x) {
  return mod289(((x*34.0)+1.0)*x);
}
float snoise2(vec2 v)
{
  const vec4 C = vec4(0.211324865405187,  // (3.0-sqrt(3.0))/6.0
                      0.366025403784439,  // 0.5*(sqrt(3.0)-1.0)
                     -0.577350269189626,  // -1.0 + 2.0 * C.x
                      0.024390243902439); // 1.0 / 41.0
  vec2 i  = floor(v + dot(v, C.yy) );
  vec2 x0 = v -   i + dot(i, C.xx);
  vec2 i1;
  i1 = (x0.x > x0.y) ? vec2(1.0, 0.0) : vec2(0.0, 1.0);
  vec4 x12 = x0.xyxy + C.xxzz;
  x12.xy -= i1;
  i = mod289(i);
  vec3 p = permute( permute( i.y + vec3(0.0, i1.y, 1.0 ))
    + i.x + vec3(0.0, i1.x, 1.0 ));
  vec3 m = max(0.5 - vec3(dot(x0,x0), dot(x12.xy,x12.xy), dot(x12.zw,x12.zw)), 0.0);
  m = m*m ;
  m = m*m ;
  vec3 x = 2.0 * fract(p * C.www) - 1.0;
  vec3 h = abs(x) - 0.5;
  vec3 ox = floor(x + 0.5);
  vec3 a0 = x - ox;
  m *= 1.79284291400159 - 0.85373472095314 * ( a0*a0 + h*h );
  vec3 g;
  g.x  = a0.x  * x0.x  + h.x  * x0.y;
  g.yz = a0.yz * x12.xz + h.yz * x12.yw;
  return 130.0 * dot(m, g);
}
// --- End inlined 2D simplex noise ---

// --- Begin inlined 3D simplex noise (snoise3) ---
vec3 mod289_3d(vec3 x) {
  return x - floor(x * (1.0 / 289.0)) * 289.0;
}
vec4 mod289_4d(vec4 x) {
  return x - floor(x * (1.0 / 289.0)) * 289.0;
}
vec4 permute_4d(vec4 x) {
     return mod289_4d(((x*34.0)+1.0)*x);
}
vec4 taylorInvSqrt(vec4 r)
{
  return 1.79284291400159 - 0.85373472095314 * r;
}
float snoise3(vec3 v)
{
  const vec2  C = vec2(1.0/6.0, 1.0/3.0) ;
  const vec4  D = vec4(0.0, 0.5, 1.0, 2.0);
  vec3 i  = floor(v + dot(v, C.yyy) );
  vec3 x0 =   v - i + dot(i, C.xxx) ;
  vec3 g = step(x0.yzx, x0.xyz);
  vec3 l = 1.0 - g;
  vec3 i1 = min( g.xyz, l.zxy );
  vec3 i2 = max( g.xyz, l.zxy );
  vec3 x1 = x0 - i1 + C.xxx;
  vec3 x2 = x0 - i2 + C.yyy;
  vec3 x3 = x0 - D.yyy;
  i = mod289_3d(i);
  vec4 p = permute_4d( permute_4d( permute_4d(
             i.z + vec4(0.0, i1.z, i2.z, 1.0 ))
           + i.y + vec4(0.0, i1.y, i2.y, 1.0 ))
           + i.x + vec4(0.0, i1.x, i2.x, 1.0 ));
  float n_ = 0.142857142857; // 1.0/7.0
  vec3  ns = n_ * D.wyz - D.xzx;
  vec4 j = p - 49.0 * floor(p * ns.z * ns.z);
  vec4 x_ = floor(j * ns.z);
  vec4 y_ = floor(j - 7.0 * x_ );
  vec4 x = x_ *ns.x + ns.yyyy;
  vec4 y = y_ *ns.x + ns.yyyy;
  vec4 h = 1.0 - abs(x) - abs(y);
  vec4 b0 = vec4( x.xy, y.xy );
  vec4 b1 = vec4( x.zw, y.zw );
  vec4 s0 = floor(b0)*2.0 + 1.0;
  vec4 s1 = floor(b1)*2.0 + 1.0;
  vec4 sh = -step(h, vec4(0.0));
  vec4 a0 = b0.xzyw + s0.xzyw*sh.xxyy ;
  vec4 a1 = b1.xzyw + s1.xzyw*sh.zzww ;
  vec3 p0 = vec3(a0.xy,h.x);
  vec3 p1 = vec3(a0.zw,h.y);
  vec3 p2 = vec3(a1.xy,h.z);
  vec3 p3 = vec3(a1.zw,h.w);
  vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));
  p0 *= norm.x;
  p1 *= norm.y;
  p2 *= norm.z;
  p3 *= norm.w;
  vec4 m = max(0.6 - vec4(dot(x0,x0), dot(x1,x1), dot(x2,x2), dot(x3,x3)), 0.0);
  m = m * m;
  return 42.0 * dot( m*m, vec4( dot(p0,x0), dot(p1,x1),
                                dot(p2,x2), dot(p3,x3) ) );
}
// --- End inlined 3D simplex noise ---

// --- Begin inlined valueRemap ---
float valueRemap(
  float value,
  float min,
  float max,
  float newMin,
  float newMax
) {
  return newMin + (value - min) * (newMax - newMin) / (max - min);
}
// --- End inlined valueRemap ---

uniform vec3 uHitPosition;
uniform float noiseScale;
uniform float noiseLength;
uniform sampler2D uFlowTexture;
uniform float pyramidReveal;
uniform sampler2D uNoiseTexture;
uniform float mouseSpeed;
uniform mat4 uPyramidMatrix;
uniform float uFlowSize;
// AVAILABLE: uniform float time;

uniform mat4 uSphereMatrix;
uniform float uSphereMix;

float sdSphere(vec3 position, float radius) {
  return length(position) - radius;
}
float sdBox(vec3 p, vec3 b) {
  vec3 q = abs(p) - b;
  return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0);
}
float sdPlane(vec3 position) {
  return position.y;
}
float tetrahedron(vec3 p, float size) {
  p = (uPyramidMatrix * vec4(p, 1.0)).xyz;
  p /= size;
  float d = (max(abs(p.x + p.y) - p.z, abs(p.x - p.y) + p.z) - 1.0) / sqrt(3.0);
  return d * size;
}
float plane(vec3 p, vec3 c, vec3 n) {
  return dot(p - c, n);
}
float opUnion(float d1, float d2) {
  return min(d1, d2);
}
float opIntersection(float d1, float d2) {
  return max(d1, d2);
}
float opSmoothUnion(float d1, float d2, float k) {
  float h = clamp(0.5 + 0.5 * (d2 - d1) / k, 0.0, 1.0);
  return mix(d2, d1, h) - k * h * (1.0 - h);
}
float gain(float x, float k) {
  float a = 0.5 * pow(2.0 * (x < 0.5 ? x : 1.0 - x), k);
  return x < 0.5
    ? a
    : 1.0 - a;
}
float expStep(float x, float n) {
  return exp2(-exp2(n) * pow(x, n));
}
float mix3(float a, float b, float c, float t) {
  if (t < 0.5) {
    return mix(a, b, t * 2.0);
  } else {
    return mix(b, c, (t - 0.5) * 2.0);
  }
}
float almostUnitIdentity(float x) {
  return x * x * (2.0 - x);
}
vec4 blurTexture(sampler2D sam, vec2 uv) {
  vec2 e = vec2(1.0) / vec2(textureSize(sam, 0));
  vec4 sum = vec4(0.0);
  float weight = 0.0;
  float kernel[9] = float[](
    0.077847,
    0.123317,
    0.077847,
    0.123317,
    0.195346,
    0.123317,
    0.077847,
    0.123317,
    0.077847
  );
  for (int i = -1; i <= 1; i++) {
    for (int j = -1; j <= 1; j++) {
      vec2 offset = vec2(float(i), float(j)) * e;
      float w = kernel[(i + 1) * 3 + (j + 1)];
      sum += texture(sam, uv + offset) * w;
      weight += w;
    }
  }
  return sum / weight;
}
vec3 getNoise(vec2 uv) {
  vec3 noise = texture(uNoiseTexture, uv).xyz;
  return noise;
}
float getCircleSin(vec3 p) {
  float d = distance(p, uHitPosition * 0.5);
  float s = sin(d * 30.0);
  return s * 0.5 + 0.5;
}
float sdPyramid(vec3 p, float h) {
  float m2 = h * h + 0.25;
  p.xz = abs(p.xz);
  p.xz = p.z > p.x ? p.zx : p.xz;
  p.xz -= 0.5;
  vec3 q = vec3(p.z, h * p.y - 0.5 * p.x, h * p.x + 0.5 * p.y);
  float s = max(-q.x, 0.0);
  float t = clamp((q.y - 0.5 * p.z) / (m2 + 0.25), 0.0, 1.0);
  float a = m2 * (q.x + s) * (q.x + s) + q.y * q.y;
  float b =
    m2 * (q.x + 0.5 * t) * (q.x + 0.5 * t) + (q.y - m2 * t) * (q.y - m2 * t);
  float d2 = min(q.y, -q.x * m2 - q.y * 0.5) > 0.0 ? 0.0 : min(a, b);
  return sqrt((d2 + q.z * q.z) / m2) * sign(max(q.z, -p.y));
}
float getSpikesHit(vec3 pos, float flow) {
  vec3 p = pos;
  float shiftInlfuence = p.y * 10.0;
  p -= uHitPosition;
  float dist = length(p) * 2.0;
  vec3 direction = normalize(p);
  float dist2 = dist - 0.01;
  dist2 = max(dist2, 0.0);
  p.xz -= direction.xz * shiftInlfuence * dist2 * 0.1;
  p += uHitPosition;
  p += uHitPosition * 0.2;
  float sinCos = sin(p.x * 60.0) * cos(p.z * 60.0);
  return sinCos;
}
float flowEdge = 0.05;
float getFlowHit2(vec3 p) {
  vec3 normal = normalize(p);
  float longitude = atan(normal.x, normal.z);
  float latitude = asin(normal.y);
  vec2 uv = vec2(
    0.5 + longitude / (2.0 * PI),
    0.5 - log(tan(PI / 4.0 + latitude / 2.0)) / PI
  );
  uv = clamp(uv, 0.0, 1.0);
  float flow = texture(uFlowTexture, uv).x;
  flow *= 2.0;
  flow -= 1.0;
  flow *= 0.05;
  float edge = smoothstep(0.0, flowEdge, uv.x);
  edge *= smoothstep(0.0, flowEdge, uv.y);
  edge *= smoothstep(1.0, 1.0 - flowEdge, uv.x);
  edge *= smoothstep(1.0, 1.0 - flowEdge, uv.y);
  return flow;
}
float maxPoint = 0.2698;
float gainConstant = 1.0 / 2.0;
float getFlowHit(vec3 p) {
  vec2 uv = vec2(
    valueRemap(p.x, -maxPoint, maxPoint, 0.0, 1.0),
    valueRemap(p.z, -maxPoint, maxPoint, 0.0, 1.0)
  );
  uv = clamp(uv, 0.0, 1.0);
  uv = vec2(gain(uv.x, gainConstant), gain(uv.y, gainConstant));
  float flow = texture(uFlowTexture, uv).x;
  flow *= 2.0;
  flow -= 1.0;
  float edge = smoothstep(0.0, flowEdge, uv.x);
  edge *= smoothstep(0.0, flowEdge, uv.y);
  edge *= smoothstep(1.0, 1.0 - flowEdge, uv.x);
  edge *= smoothstep(1.0, 1.0 - flowEdge, uv.y);
  flow *= edge;
  return flow;
}
float getOrbeHit(vec3 p, float flow) {
  float pyramidShift = -0.1;
  p.y -= pyramidShift;
  vec3 flowShift = vec3(p.x, 0.0, p.z);
  flowShift *= 0.4;
  flowShift *= flow;
  p += flowShift;
  float pyramidScale = 0.5;
  float hit = sdPyramid(p / pyramidScale, 0.9) * pyramidScale;
  hit -= 0.001;
  return hit;
}
float getOrbe2hit(vec3 pIn, float flow) {
  vec3 p = (uSphereMatrix * vec4(pIn, 1.0)).xyz;
  float noise = getNoise(p.xz * 0.3).x;
  noise = max(noise, getNoise(p.xy * 0.3).x);
  flow *= valueRemap(uSphereMix, 0.0, 1.0, 1.0, 1.0);
  float n = noise * 0.05 + flow * 0.05;
  float hit = sdSphere(p, 0.25 + n);
  return hit;
}
float getSceneHit(vec3 p) {
  vec3 pPyramid = (uPyramidMatrix * vec4(p, 1.0)).xyz;
  float flow = getFlowHit(pPyramid);
  float hit1 = getOrbeHit(pPyramid, flow);
  float hit2 = getOrbe2hit(p, flow);
  float hit = mix(hit1, hit2, uSphereMix);
  return hit * 0.3;
}
// --- End inlined getSceneHit and dependencies ---

// --- Begin inlined getEnvColor and dependencies ---
vec2 normalToEnvUv(vec3 normal) {
  vec3 n = normalize(normal);
  vec2 uv = vec2(
    atan(n.x, n.z) * 0.15915494309189533576888376337251 + 0.5,
    acos(n.y) * 0.318309886183790671537767526745
  );
  return uv;
}
vec4 textureGaussian(sampler2D sam, vec2 uv) {
  float kernel[9] = float[](
    0.0625,
    0.125,
    0.0625,
    0.125,
    0.25,
    0.125,
    0.0625,
    0.125,
    0.0625
  );
  vec2 texelSize = vec2(1.0) / vec2(textureSize(sam, 0));
  vec4 result = vec4(0.0);
  int index = 0;
  for (int i = -1; i <= 1; i++) {
    for (int j = -1; j <= 1; j++) {
      vec2 offset = vec2(float(i), float(j)) * texelSize;
      result += texture(sam, uv + offset) * kernel[index];
      index++;
    }
  }
  return result;
}
float getFresnel(vec3 normal, vec3 viewDir) {
  float cosTheta = dot(normal, viewDir);
  return cosTheta;
}
float desaturate(vec3 col) {
  return dot(col, vec3(0.299, 0.587, 0.114));
}
vec2 textureScale = vec2(2.0, 4.0);
vec3 getEnvColor(sampler2D envMap, vec3 normal, vec3 viewDir) {
  vec2 uv = normalToEnvUv(normal);
  uv.x += 0.2;
  vec3 col = textureGaussian(envMap, uv * textureScale).rgb;
  vec3 desat = vec3(desaturate(col));
  float fresnel = getFresnel(normal, -viewDir);
  fresnel *= 2.0;
  fresnel = clamp(fresnel, 0.0, 1.0);
  fresnel = pow(fresnel, 2.0);
  col = mix(col, desat, fresnel);
  return col;
}
// --- End inlined getEnvColor and dependencies ---

uniform sampler2D uEnvMap;

// Normal calculation function (using gradient):
const vec3 GRADIENT_STEP = vec3(0.02, 0.0, 0.0);
vec3 getNormal(vec3 p) {
  float gradientX =
    getSceneHit(p + GRADIENT_STEP.xyy) - getSceneHit(p - GRADIENT_STEP.xyy);
  float gradientY =
    getSceneHit(p + GRADIENT_STEP.yxy) - getSceneHit(p - GRADIENT_STEP.yxy);
  float gradientZ =
    getSceneHit(p + GRADIENT_STEP.yyx) - getSceneHit(p - GRADIENT_STEP.yyx);
  return normalize(vec3(gradientX, gradientY, gradientZ));
}

vec3 mainColor = vec3(0.1);

vec3 lightDirection = normalize(vec3(1.0, 1.5, 1.0));

vec3 getLight(vec3 p, vec3 reflectedNormal) {
  float lambert = dot(reflectedNormal, lightDirection);
  lambert = clamp(lambert, 0.0, 1.0) * 0.9;
  return vec3(lambert) * 2.0;
}

vec3 getSurface(vec3 p, vec3 rayDirection) {
  vec3 viewDir = -rayDirection;
  vec3 normal = getNormal(p);
  vec3 reflectedNormal = reflect(viewDir, normal);
  vec3 light = getEnvColor(uEnvMap, reflectedNormal, viewDir);
  return light;
}
// --- End inlined get-surface.glsl ---

// --- Begin inlined structs.glsl ---
struct RayResult {
  bool hit;
  vec3 position;
  float distance;
};

struct RaymarchResult {
  vec4 color;
  float depth;
};
// --- End inlined structs.glsl ---

const float SURFACE_DIST = 0.001;
const int MAX_STEPS = 200;

RayResult castRay(
  vec3 ro,
  vec3 rd,
  float maxDistance,
  float surfaceDistance,
  int maxSteps
) {
  float d0 = 0.0;
  float hitPoint = getSceneHit(ro);
  for (int i = 0; i < MAX_STEPS; i++) {
    vec3 p = ro + d0 * rd;
    hitPoint = getSceneHit(p);
    d0 += hitPoint;
    if (hitPoint < surfaceDistance || d0 >= maxDistance) {
      break;
    }
  }
  bool isHit = hitPoint < surfaceDistance;
  vec3 p = ro + d0 * rd;
  return RayResult(isHit, p, d0);
}

RaymarchResult rayMarch(vec3 rayPosition, vec3 rayDirection, float maxDepth) {
  vec4 result = vec4(1.0, 1.0, 1.0, 0.0);
  float distance = 0.0;
  RayResult hit = castRay(
    rayPosition,
    rayDirection,
    maxDepth,
    SURFACE_DIST,
    MAX_STEPS
  );
  if (hit.hit) {
    vec3 color = getSurface(hit.position, rayDirection);
    color = hit.distance < 0.01 ? vec3(1.0) : color;
    result = vec4(color, 1.0);
    distance = hit.distance;
  } else {
    discard;
    distance = 1.0;
  }
  return RaymarchResult(result, distance);
}