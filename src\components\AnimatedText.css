.animated-text-container {
  overflow: visible;
  display: block;
  width: 100%;
}

.animated-text {
  visibility: visible;
  opacity: 1;
  transform: translate3d(0, 0, 0);
}

/* Character styling for the animation */
.animated-text .char {
  display: inline-block;
  position: relative;
}

/* Prevent layout shift by ensuring inline-block for all split elements */
.animated-text .word {
  position: relative;
  display: inline-block;
}

/* Add a little spacing between words */
.animated-text .word:not(:last-child) {
  margin-right: 0.25em;
}

/* Fade animations */
.fade-out {
  opacity: 0;
  transition: opacity 400ms;
}

.fade-in {
  opacity: 1;
  transition: opacity 2s;
}

.fade-init {
  opacity: 0;
  transition: opacity 1ms;
}

/* ScrollSpeed animations */
.scrollSpeed-out {
  opacity: 1 !important;
  transition:
    opacity 1s ease-out 0ms,
    transform 1s ease-out 0ms;
}

.scrollSpeed-in {
  opacity: 0;
  transform: translate3d(0, -50px, 0);
  transition:
    opacity 300ms ease-out 0ms,
    transform 300ms ease-out 0ms;
}

/* Transition utilities */
.no-transition {
  transition: unset;
}


/* Base container styling */
.animated-text-container {
  overflow: visible;
  position: relative;
}

/* Specifically for reveal lines effect */
.revealLines-text {
  position: relative;
  overflow: visible;
}

/* Specifically for fade lines effect */
.fadeLines-text {
  position: relative;
  overflow: visible;
}

.split-line {
  display: block; /* Ensure each line is a block */
}

/* For random letters effect */
.char {
  display: inline-block;
}