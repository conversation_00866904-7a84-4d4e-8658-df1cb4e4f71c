@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Thin.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-ExtraLight.woff2') format('woff2');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-ExtraBold.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Black.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  pointer-events: none;
}

.logoTopLeft {
  position: absolute;
  top: 2rem;
  left: 2rem;
  pointer-events: auto;
}

.logoImg {
  width: 100px;
  height: auto;
  display: block;
}

.heroContentWrapper {
  position: absolute;
  top: 10rem;
  left: 10rem;
  transform: none;
  max-width: 1000px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  pointer-events: none;
}

.topLeftLabel {
  color: #ffffff;
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 0rem;
  letter-spacing: 0.05em;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
}

.subText {
  position: absolute;
  left: 10rem;
  bottom: 7rem;
  color: #ffffff;
  font-size: 1rem;
  margin-bottom: 0;
  font-weight: 400;
  line-height: 1.5;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  max-width: 450px;
  pointer-events: none;
}

.scrollIndicator {
  position: absolute;
  bottom: 3rem;
  left: 10rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  pointer-events: auto;
}

.scrollText {
  color: #fff;
  font-size: 0.85rem;
  letter-spacing: 0.1em;
  font-weight: 300;
  margin: 0;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
}

.scrollArrow {
  width: 32px;
  height: auto;
}

.heroLabelRow {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0rem;
}

.heroLabelImgWrapper {
  display: flex;
  align-items: center;
  margin-bottom: 0.1rem;
}

.heroLabelImg {
  width: 12px;
  height: 12px;
  object-fit: contain;
  display: block;
}

.blockSwitcherWrapper {
  position: fixed;
  right: 2.5rem;
  bottom: 2.5rem;
  display: flex;
  flex-direction: row;
  gap: 1rem;
  z-index: 20;
}

.switcherButton {
  background: #222;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 0.7rem 1.3rem;
  font-size: 1rem;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  font-weight: 500;
  cursor: pointer;
  opacity: 0.7;
  transition: background 0.2s, opacity 0.2s;
}

.switcherButton:hover {
  background: #444;
  opacity: 1;
}

.activeSwitcherButton {
  background: #6ee7b7;
  color: #111;
  border: none;
  border-radius: 4px;
  padding: 0.7rem 1.3rem;
  font-size: 1rem;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  font-weight: 700;
  cursor: pointer;
  opacity: 1;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.infoCard {
  background: rgba(0,0,0,0.25);
  border: 1px solid #222222;
  border-radius: 4px;
  width: 600px;
  height: 600px;
  max-width: 95vw;
  margin: 6px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px;
  box-sizing: border-box;
  color: #fff;
  font-family: 'Inter', Arial, sans-serif;
  position: relative;
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.infoCardTitle {
  font-weight: 700;
  font-size: 2.75rem;
  line-height: 1.2;
  margin-bottom: 12px;
  max-width: 600px;
}

.infoCardSubtitle {
  font-weight: 400;
  font-size: 18px;
  line-height: 1.2;
  margin-bottom: 28px;
  max-width: 300px;
}

.infoCardRegion {
  font-weight: 500;
  font-size: 12px;
  color: #bdbdbd;
  margin-bottom: 12px;
}

.infoCardDescription {
  font-size: 18px;
  color: #bdbdbd;
  margin-bottom: 28px;
  min-height: 72px;
}

.infoCardArrowBox {
  position: absolute;
  top: 40px;
  right: 40px;
  width: 120px;
  height: 120px;
  border: 1px solid #222;
  display: flex;
  align-items: center;
  justify-content: center;
}

.infoCardContentBottom {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: 100%;
  min-height: 220px;
}

.section2Hero {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding-left: 45vw;
  padding-right: 0;
  box-sizing: border-box;
}

.section2Headline {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 4rem;
  font-weight: 500;
  line-height: 1.25;
  color: #fff;
  margin-bottom: 2rem;
  letter-spacing: 0em;
  text-align: left;
  max-width: 800px;
}

.section2SubtextContainer {
  display: flex;
  flex-direction: row;
  gap: 4.5rem;
  max-width: 850px;
  width: 100%;
  margin-bottom: 2.5rem;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
}

.section2Subtext {
  /* flex: 1 1 0; */ /* Removed to allow inline width */
  /* min-width: 200px; */ /* Removed to allow inline width */
  /* max-width: 260px; */ /* Removed to allow inline width */
  font-family: 'Supply', Arial, sans-serif;
  font-size: 0.90rem;
  font-weight: 300;
  color: #fff;
  margin-bottom: 0;
  line-height: 1.3;
  text-align: left;
  /* width is now set inline in JSX for each column */
}

@media (max-width: 900px) {
  .section2SubtextContainer {
    flex-direction: column;
    gap: 1.2rem;
    max-width: 98vw;
  }
  .section2Subtext {
    max-width: 100%;
    min-width: 0;
  }
}

.section2Button {
  margin-top: 0vw;
  padding: 0.75rem 2rem;
  font-size: 0.90rem;
  font-family: 'Supply', Arial, sans-serif;
  font-weight: 400;
  color: #000000;
  background: #ffffff;
  border-radius: 0px;
  border: none;
  cursor: pointer;
  outline: none;
  text-align: left;
  align-self: flex-start;
}

.section3Heading {
  position: absolute;
  top: 6rem;
  left: 3vw;
  z-index: 3;
  color: #fff;
  font-size: 3rem;
  font-weight: 700;
  letter-spacing: -0.01em;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  pointer-events: none;
}

.infoCardIcon {
  width: 16px;
  height: 16px;
  object-fit: contain;
  margin-bottom: 12px;
  margin-left: 2px;
}

.infoCardArrowIcon {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.topNavBar {
  width: 100%;
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 2rem 3.5rem 0 2.5rem;
  z-index: 20;
  pointer-events: auto;
  overflow-x: hidden;
}

.menuIconTopRight {
  width: 16px;
  height: 16px;
  object-fit: contain;
  cursor: pointer;
  display: block;
}

.heroHeading {
  color: #fff;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  font-size: 4rem;
  font-weight: 600;
  line-height: 1.25;
  text-align: left;
}

.heroButton {
  padding: 0.75rem 1.5rem;
  font-size: 0.90rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 400;
  color: #000000;
  background: #ffffff;
  border: none;
  cursor: pointer;
  align-self: flex-start;
}

.heroText1 {
  position: absolute;
  top: 2.5rem;
  left: 30rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.90rem;
  color: #fff;
}
.heroText2 {
  position: absolute;
  top: 2.5rem;
  left: 58rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.90rem;
  color: #fff;
}
.heroText3 {
  position: absolute;
  top: 2.5rem;
  right: 30rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.90rem;
  color: #fff;
}
.heroText4 {
  position: absolute;
  top: 32rem;
  left: 58rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.90rem;
  color: #fff;
}
.heroText5 {
  position: absolute;
  top: 35rem;
  left: 58rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.90rem;
  color: #fff;
}
.heroText6 {
  position: absolute;
  bottom: 3rem;
  right: 25rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.90rem;
  color: #fff;
}
.heroText7 {
  position: absolute;
  bottom: 3rem;
  right: 5rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.90rem;
  color: #fff;
}
.section2AbsText1 {
  position: absolute;
  top: 15rem;
  left: 10rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.90rem;
  color: #fff;
}
.section2AbsText2 {
  position: absolute;
  bottom: 12rem;
  left: 40rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.90rem;
  color: #fff;
}
.strike {
  text-decoration: line-through;
  color: #878782;
  font: inherit;
}