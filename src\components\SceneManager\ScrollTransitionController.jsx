import React, { useRef, useState, useEffect, useCallback } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';
import { useCameraStore } from '../WaterMatcap/scene_floor/cameras3';

/**
 * ScrollTransitionController manages scroll-based transitions between Scene1 and Scene2
 * 
 * Flow:
 * 1. Scene1 virtual scroll (0% to 100%)
 * 2. Transition phase (Scene1 → Scene2) with camera bridging
 * 3. Scene2 virtual scroll (0% to 100%)
 * 4. Reverse transition (Scene2 → Scene1) with camera bridging
 */
export default function ScrollTransitionController({ 
  onSceneChange, 
  onTransitionProgress,
  isActive = true 
}) {
  // Global scroll state
  const [globalScrollY, setGlobalScrollY] = useState(0);
  const globalScrollRef = useRef(0);
  const targetScrollRef = useRef(0);
  const scrollVelocityRef = useRef(0);

  // Scene and transition state
  const [currentScene, setCurrentScene] = useState(1); // 1 or 2
  const [transitionProgress, setTransitionProgress] = useState(0); // 0 to 1
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Scene scroll states (0 to 1 for each scene)
  const [scene1ScrollProgress, setScene1ScrollProgress] = useState(0);
  const [scene2ScrollProgress, setScene2ScrollProgress] = useState(0);

  // Camera store for smooth transitions
  const setOrbitPosition = useCameraStore((state) => state.setOrbitPosition);
  const setOrbitRotation = useCameraStore((state) => state.setOrbitRotation);

  // Define camera positions for smooth transitions
  const cameraPositions = {
    scene1Start: { x: 0, y: 0.35, z: 2 },
    scene1Final: { x: 0, y: 0.15, z: 5 },
    scene2Start: { x: 0, y: 3, z: 0 },
    scene2Final: { x: 0, y: 3, z: 0 } // Scene2 doesn't change camera much
  };

  const cameraRotations = {
    scene1Start: { x: -0.12, y: 0, z: 0 },
    scene1Final: { x: 0, y: 0, z: 0 },
    scene2Start: { x: -Math.PI / 2, y: 0, z: 0 },
    scene2Final: { x: -Math.PI / 2, y: 0, z: 0 }
  };

  // Calculate total scroll distance (Scene1 + Transition + Scene2 + Reverse Transition)
  const sceneScrollDistance = window.innerHeight * 3; // Each scene's scroll distance
  const transitionDistance = window.innerHeight * 1; // Transition distance
  const totalScrollDistance = sceneScrollDistance * 2 + transitionDistance * 2;

  // Global scroll event handling
  useEffect(() => {
    const handleWheel = (event) => {
      if (!isActive) return;

      event.preventDefault();
      event.stopPropagation();

      const deltaY = event.deltaY;
      const scrollSensitivity = 0.5;

      // Update target scroll position
      targetScrollRef.current = Math.max(0, Math.min(totalScrollDistance, targetScrollRef.current + deltaY * scrollSensitivity));

      // Add velocity for momentum
      scrollVelocityRef.current = deltaY * scrollSensitivity * 0.1;
    };

    const handleKeyDown = (event) => {
      if (!isActive) return;

      let scrollAmount = 0;

      switch(event.code) {
        case 'ArrowDown':
        case 'Space':
          scrollAmount = window.innerHeight * 0.3;
          break;
        case 'ArrowUp':
          scrollAmount = -window.innerHeight * 0.3;
          break;
        case 'PageDown':
          scrollAmount = window.innerHeight * 0.6;
          break;
        case 'PageUp':
          scrollAmount = -window.innerHeight * 0.6;
          break;
        default:
          return;
      }

      event.preventDefault();
      event.stopPropagation();

      targetScrollRef.current = Math.max(0, Math.min(totalScrollDistance, targetScrollRef.current + scrollAmount));
    };

    window.addEventListener('wheel', handleWheel, { passive: false });
    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isActive, totalScrollDistance]);

  // Smooth scroll interpolation and state calculation
  useFrame((_, delta) => {
    if (!isActive) return;

    // Smooth interpolation towards target scroll position
    const lerpFactor = 1 - Math.pow(0.001, delta);
    globalScrollRef.current = THREE.MathUtils.lerp(globalScrollRef.current, targetScrollRef.current, lerpFactor);

    // Apply velocity decay
    scrollVelocityRef.current *= 0.95;

    // Update state for debug display
    setGlobalScrollY(globalScrollRef.current);

    // Calculate current phase and progress
    const scrollProgress = globalScrollRef.current / totalScrollDistance;

    if (scrollProgress <= 0.25) {
      // Phase 1: Scene1 (0% to 25%)
      const scene1Progress = scrollProgress / 0.25;
      setCurrentScene(1);
      setIsTransitioning(false);
      setScene1ScrollProgress(scene1Progress);
      setTransitionProgress(0);
      
      // Notify parent components
      onSceneChange?.(1, scene1Progress, 0);
      onTransitionProgress?.(0);

    } else if (scrollProgress <= 0.375) {
      // Phase 2: Transition Scene1 → Scene2 (25% to 37.5%)
      const transitionProg = (scrollProgress - 0.25) / 0.125;
      setCurrentScene(1);
      setIsTransitioning(true);
      setTransitionProgress(transitionProg);
      
      // Handle camera transition
      handleCameraTransition(transitionProg, 'forward');
      
      // Notify parent components
      onSceneChange?.(1, 1, transitionProg);
      onTransitionProgress?.(transitionProg);

    } else if (scrollProgress <= 0.625) {
      // Phase 3: Scene2 (37.5% to 62.5%)
      const scene2Progress = (scrollProgress - 0.375) / 0.25;
      setCurrentScene(2);
      setIsTransitioning(false);
      setScene2ScrollProgress(scene2Progress);
      setTransitionProgress(0);
      
      // Notify parent components
      onSceneChange?.(2, scene2Progress, 0);
      onTransitionProgress?.(0);

    } else if (scrollProgress <= 0.75) {
      // Phase 4: Transition Scene2 → Scene1 (62.5% to 75%)
      const transitionProg = (scrollProgress - 0.625) / 0.125;
      setCurrentScene(2);
      setIsTransitioning(true);
      setTransitionProgress(transitionProg);
      
      // Handle camera transition (reverse)
      handleCameraTransition(transitionProg, 'reverse');
      
      // Notify parent components
      onSceneChange?.(2, 1, transitionProg);
      onTransitionProgress?.(transitionProg);

    } else {
      // Phase 5: Scene1 again (75% to 100%)
      const scene1Progress = (scrollProgress - 0.75) / 0.25;
      setCurrentScene(1);
      setIsTransitioning(false);
      setScene1ScrollProgress(scene1Progress);
      setTransitionProgress(0);
      
      // Notify parent components
      onSceneChange?.(1, scene1Progress, 0);
      onTransitionProgress?.(0);
    }
  });

  // Handle camera transitions between scenes
  const handleCameraTransition = useCallback((progress, direction) => {
    let fromPos, toPos, fromRot, toRot;

    if (direction === 'forward') {
      // Scene1 → Scene2
      fromPos = cameraPositions.scene1Final;
      toPos = cameraPositions.scene2Start;
      fromRot = cameraRotations.scene1Final;
      toRot = cameraRotations.scene2Start;
    } else {
      // Scene2 → Scene1 (reverse)
      fromPos = cameraPositions.scene2Final;
      toPos = cameraPositions.scene1Start;
      fromRot = cameraRotations.scene2Final;
      toRot = cameraRotations.scene1Start;
    }

    // Smooth easing for camera transitions
    const easeInOutCubic = (t) => {
      return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    };

    const easedProgress = easeInOutCubic(progress);

    // Interpolate camera position
    const currentPosition = {
      x: fromPos.x + (toPos.x - fromPos.x) * easedProgress,
      y: fromPos.y + (toPos.y - fromPos.y) * easedProgress,
      z: fromPos.z + (toPos.z - fromPos.z) * easedProgress,
    };

    // Interpolate camera rotation
    const currentRotation = {
      x: fromRot.x + (toRot.x - fromRot.x) * easedProgress,
      y: fromRot.y + (toRot.y - fromRot.y) * easedProgress,
      z: fromRot.z + (toRot.z - fromRot.z) * easedProgress,
    };

    // Update camera store
    setOrbitPosition(currentPosition);
    setOrbitRotation(currentRotation);
  }, [setOrbitPosition, setOrbitRotation]);

  // Expose scroll progress for individual scenes
  const getSceneScrollProgress = useCallback((sceneNumber) => {
    if (sceneNumber === 1) {
      return scene1ScrollProgress;
    } else if (sceneNumber === 2) {
      return scene2ScrollProgress;
    }
    return 0;
  }, [scene1ScrollProgress, scene2ScrollProgress]);

  // Expose current state for debugging
  const getDebugInfo = useCallback(() => {
    return {
      globalScrollY: globalScrollY.toFixed(1),
      currentScene,
      isTransitioning,
      transitionProgress: transitionProgress.toFixed(3),
      scene1ScrollProgress: scene1ScrollProgress.toFixed(3),
      scene2ScrollProgress: scene2ScrollProgress.toFixed(3),
      totalScrollDistance: totalScrollDistance.toFixed(1)
    };
  }, [globalScrollY, currentScene, isTransitioning, transitionProgress, scene1ScrollProgress, scene2ScrollProgress, totalScrollDistance]);

  // Auto-center functionality for Scene2
  const centerScroll = useCallback((targetPosition, duration = 2.0) => {
    // Convert target position to global scroll coordinates
    const targetGlobalScroll = Math.max(0, Math.min(totalScrollDistance, targetPosition));

    // Set the target scroll position directly
    targetScrollRef.current = targetGlobalScroll;

    // Note: The smooth interpolation in useFrame will handle the animation
    // The duration parameter is kept for API compatibility but the actual
    // animation speed is controlled by the lerp factor in useFrame
  }, [totalScrollDistance]);

  // Expose methods for parent components
  React.useImperativeHandle(React.forwardRef(() => null), () => ({
    getSceneScrollProgress,
    getDebugInfo,
    getCurrentScene: () => currentScene,
    getTransitionProgress: () => transitionProgress,
    isTransitioning: () => isTransitioning,
    centerScroll,
    scroll: {
      y: globalScrollRef.current
    }
  }));

  return null; // This component only manages state, no visual output
}
