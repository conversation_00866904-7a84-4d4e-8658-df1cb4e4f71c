import React, { useRef, useEffect, useState } from 'react'
import { Canvas, useFrame, useThree } from '@react-three/fiber'
import { OrbitControls } from '@react-three/drei'
import * as THREE from 'three'
import styles from './Services.module.css'
import Lenis from 'lenis'
import { useScrollNavigation } from './useScrollNavigationWithIndicator';
import AnimatedText from './components/AnimatedText'; // Import AnimatedText component
import { Fluid } from '@whatisjery/react-fluid-distortion'; // Import Fluid
import { EffectComposer } from '@react-three/postprocessing'; // Import EffectComposer

import ReflectiveFloor from './components/ReflectiveFloor.jsx'
import Romannumber from './components/Romannumber.jsx'

// Camera controller component that responds to scroll
function CameraController({ scrollY }) {
  // Your existing Camera Controller code...
  const { camera } = useThree()
  const initialPosition = useRef(null)
  
  useEffect(() => {
    if (!initialPosition.current) {
      initialPosition.current = { 
        x: camera.position.x,
        y: camera.position.y, 
        z: camera.position.z 
      }
    }
  }, [camera])
  
  useEffect(() => {
    if (!initialPosition.current) return
    
    const baseFOV = 60
    const maxFOV = 65
    const maxMoveDown = 0.5
    
    const scrollProgress = Math.min(scrollY / (window.innerHeight * 3.5), 1)
    const easedProgress = easeOutCubic(scrollProgress)
    
    camera.fov = baseFOV + easedProgress * (maxFOV - baseFOV)
    camera.position.y = initialPosition.current.y - (easedProgress * maxMoveDown)
    camera.updateProjectionMatrix()
    
  }, [scrollY, camera])
  
  const easeOutCubic = (x) => {
    return 1 - Math.pow(1 - x, 3)
  }
  
  return null
}

// Scene opacity controller
function SceneFadeController({ scrollY }) {
  // Your existing Scene Fade Controller code...
  const { scene } = useThree()
  
  useEffect(() => {
    const scrollStart = window.innerHeight * 0.5
    const scrollEnd = window.innerHeight * 2
    
    let opacity = 1
    if (scrollY > scrollStart) {
      opacity = 1 - Math.min((scrollY - scrollStart) / (scrollEnd - scrollStart), 1)
    }
    
    scene.traverse((object) => {
      if (object.isMesh && object.material) {
        if (Array.isArray(object.material)) {
          object.material.forEach(material => {
            if (material.opacity !== undefined) {
              material.opacity = opacity
              material.transparent = true
            }
          })
        } else {
          if (object.material.opacity !== undefined) {
            object.material.opacity = opacity
            object.material.transparent = true
          }
        }
      }
    })
    
  }, [scrollY, scene])
  
  return null
}

export default function Services({ onBack, onReady, onNext }) {
  const [scrollY, setScrollY] = useState(0)
  const overlayRef = useRef(null)
  const lenisRef = useRef(null)
  
  // Add the scroll navigation hook
  const scrollNavRef = useScrollNavigation({
    onNext,
    onBack,
    scrollThreshold: 3
  });

  // Add useEffect for the onReady callback
  useEffect(() => {
    if (onReady) {
      onReady();
    }
  }, [onReady]);

  // Initialize Lenis smooth scrolling with enhanced smoothness settings
  useEffect(() => {
    // Initialize Lenis with smoother settings
    if (!overlayRef.current) return;
    
    const contentSection = overlayRef.current.querySelector(`.${styles.contentSection}`);
    if (!contentSection) {
      console.error("Could not find content section in Services");
      return;
    }
    
    lenisRef.current = new Lenis({
      wrapper: overlayRef.current,
      content: contentSection,
      duration: 3,
      easing: (t) => {
        return 1 - Math.pow(1 - t, 5) + Math.sin(t * Math.PI) * 0.01;
      },
      smoothWheel: true,
      wheelMultiplier: 0.8,
      direction: 'vertical',
      gestureDirection: 'vertical',
      smooth: true,
      mouseMultiplier: 0.8,
      smoothTouch: true,
      touchMultiplier: 1.5,
      lerp: 0.075,
      infinite: false,
    })

    // Synchronize scroll values with React state
    const onScrollUpdate = (e) => {
      setScrollY(e.scroll);
    }

    lenisRef.current.on('scroll', onScrollUpdate)

    // Set up requestAnimationFrame for smoother animation
    function raf(time) {
      if (lenisRef.current) {
        lenisRef.current.raf(time)
      }
      requestAnimationFrame(raf)
    }
    
    // Start the animation loop
    requestAnimationFrame(raf)
    
    // Clean up function
    return () => {
      if (lenisRef.current) {
        lenisRef.current.off('scroll', onScrollUpdate)
        lenisRef.current.destroy()
        lenisRef.current = null
      }
    }
  }, [])

  // Handler for manual fluid effect on pointer move
  const handlePointerMove = (e) => {
    const canvasElement = document.querySelector('.services-canvas-container canvas');
    if (canvasElement) {
      const event = new PointerEvent('pointermove', {
        clientX: e.clientX,
        clientY: e.clientY,
        bubbles: true
      });
      canvasElement.dispatchEvent(event);
    }
  };
  
  // Get fluid color - same as in About.jsx
  const getFluidColor = () => {
    return '#101010';
  };
  
  // Merge the refs (Lenis ref and scroll navigation ref)
  const mergeRefs = (node) => {
    overlayRef.current = node;
    if (scrollNavRef) {
      scrollNavRef.current = node;
    }
  };
  
  return (
    <div className={styles.servicesComponent} style={{ width: '100%', height: '100vh', position: 'relative' }}>
      {/* Back button centered at top */}
      <button 
        className="back-Button"
        onClick={onBack}
        style={{
          position: 'fixed',
          top: '2rem',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 100,
          background: 'transparent',
          border: 'none',
          cursor: 'pointer',
          padding: '10px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '0.90rem',
        }}
      >
        Back
      </button>

      {/* Next button centered at bottom */}
      <button 
        className="next-Button"
        onClick={onNext}
        style={{
          position: 'fixed',
          bottom: '2rem',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 100,
          background: 'transparent',
          border: 'none',
          cursor: 'pointer',
          padding: '10px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '0.90rem',
        }}
      >
        Next
      </button>

      {/* 3D Canvas with added Fluid effect */}
      <div className="services-canvas-container" style={{ position: 'absolute', width: '100%', height: '100%' }}>
        <Canvas camera={{ position: [0, 0, 5], fov: 60 }}>
          <ambientLight intensity={0.5} />
          <pointLight position={[10, 10, 10]} intensity={0.5} />

          <color attach="background" args={['#000']} />
          <fog attach="fog" args={['#060606', 1, 200]} />

          <Romannumber position={[-0.425, 0.45, -1]} scale={[1.25, 1.25, 1.25]} />
          <Romannumber position={[0.775, 0.45, -1]} scale={[1.25, 1.25, 1.25]} />
          
          <ReflectiveFloor />
          
          {/* Camera controllers */}
          <CameraController scrollY={scrollY} />
          <SceneFadeController scrollY={scrollY} />
        </Canvas>
      </div>
      
      {/* Scroll Overlay - use mergeRefs to combine both refs */}
      <div 
        className={styles.scrollOverlay} 
        ref={mergeRefs}
        onPointerMove={handlePointerMove} // Added pointer move handler for manual fluid effect
      >
        <section className={styles.contentSection}>
          {/* First paragraph with logo instead of heading */}
          <div className={`${styles.paragraph} ${styles.paragraph1} ${styles.left}`}>
            <div className={styles.paragraphContainer}>
              <div className={styles.logoContainer}>
                {/* Replace h2 with AnimatedText */}
                <AnimatedText
                  effect="fadeLines"
                  threshold={0.2}
                  elementType="h2"
                >
                  SERVICES
                </AnimatedText>
              </div>
              {/* Replace p with AnimatedText */}
              <AnimatedText
                effect="fadeLines"
                threshold={0.2}
                elementType="p"
              >
                Unsere Leistungen reichen von modernen, smarten Websites mit Fokus auf Nutzerfreundlichkeit und Ästhetik bis hin zu interaktiven 3D-Animationen und Web-Erlebnissen, die Besucher:innen nachhaltig begeistern.
              </AnimatedText>
            </div>
          </div>
          
          <div className={`${styles.paragraph} ${styles.paragraph2} ${styles.right}`}>
            <div className={styles.paragraphContainer}>
                <div className={styles.smallSubheading}>
                    {/* Replace h3 with AnimatedText */}
                    <AnimatedText
                      effect="fadeLines"
                      threshold={0.2}
                      elementType="h3"
                    >
                      Digitale Lösungen, die deine Marke voranbringen.
                    </AnimatedText>
                </div>
            </div>
          </div>
          
          {/* Modified third paragraph - bullet points with AnimatedText */}
          <div className={`${styles.paragraph} ${styles.paragraph3} ${styles.left}`}>
            <div className={styles.paragraphContainer}>
                <div className={styles.bulletPointsContainer}>
                    {/* Replace each bullet point with AnimatedText */}
                    <AnimatedText
                      effect="fadeLines"
                      threshold={0.1}
                      elementType="p"
                      className={styles.bulletPoint}
                    >
                      Webdesign
                    </AnimatedText>
                    
                    <AnimatedText
                      effect="fadeLines"
                      threshold={0.1}
                      elementType="p"
                      className={styles.bulletPoint}
                    >
                      Webentwicklung
                    </AnimatedText>
                    
                    <AnimatedText
                      effect="fadeLines"
                      threshold={0.1}
                      elementType="p"
                      className={styles.bulletPoint}
                    >
                      3D-Animationen
                    </AnimatedText>
                    
                    <AnimatedText
                      effect="fadeLines"
                      threshold={0.1}
                      elementType="p"
                      className={styles.bulletPoint}
                    >
                      E-Commerce
                    </AnimatedText>
                    
                    <AnimatedText
                      effect="fadeLines"
                      threshold={0.1}
                      elementType="p"
                      className={styles.bulletPoint}
                    >
                      Shopify
                    </AnimatedText>
                    
                    <AnimatedText
                      effect="fadeLines"
                      threshold={0.1}
                      elementType="p"
                      className={styles.bulletPoint}
                    >
                      Content Management
                    </AnimatedText>
                    
                    <AnimatedText
                      effect="fadeLines"
                      threshold={0.1}
                      elementType="p"
                      className={styles.bulletPoint}
                    >
                      SEO & Performance
                    </AnimatedText>
                    
                    <AnimatedText
                      effect="fadeLines"
                      threshold={0.1}
                      elementType="p"
                      className={styles.bulletPoint}
                    >
                      Branding
                    </AnimatedText>
                    
                    <AnimatedText
                      effect="fadeLines"
                      threshold={0.1}
                      elementType="p"
                      className={styles.bulletPoint}
                    >
                      Hosting & Wartung
                    </AnimatedText>
                </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}