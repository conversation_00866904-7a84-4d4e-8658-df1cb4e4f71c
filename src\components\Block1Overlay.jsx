import React, { useState } from 'react';
import styles from './Block1Overlay.module.css';

function ExpandingFlexCards() {
  const cards = [
    { svg: '/flexcard/FlexCard1.svg', icon: '/icons/FlexCard_Icon1.svg' },
    { svg: '/flexcard/FlexCard2.svg', icon: '/icons/FlexCard_Icon2.svg' },
    { svg: '/flexcard/FlexCard3.svg', icon: '/icons/FlexCard_Icon3.svg' },
    { svg: '/flexcard/FlexCard4.svg', icon: '/icons/FlexCard_Icon4.svg' },
    { svg: '/flexcard/FlexCard5.svg', icon: '/icons/FlexCard_Icon5.svg' },
  ];
  const [active, setActive] = useState(0);
  return (
    <div className={styles.flexCardsRow}>
      {cards.map((card, idx) => (
        <div
          key={idx}
          className={
            styles.flexCard + ' ' + (active === idx ? styles.active : styles.inactive)
          }
          onClick={() => setActive(idx)}
        >
          {active === idx ? (
            <img
              src={card.svg}
              alt={`Flex Card ${idx + 1}`}
              className={styles.flexCardSVG}
              draggable={false}
            />
          ) : (
            <div className={styles.flexCardIconWrapper}>
              <img
                src={card.icon}
                alt={`Flex Card Icon ${idx + 1}`}
                width={40}
                height={40}
                className={styles.flexCardIcon}
                draggable={false}
              />
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

export default function Block1Overlay({ onClose }) {
  return (
    <>
      <button onClick={onClose} className={styles.closeButton} aria-label="Close overlay">CLOSE</button>
      <div className={styles.arrowRight}>
        <img src="/arrow_long.svg" alt="Arrow Right" width={40} height={40} />
      </div>
      <div className={styles.arrowLeft}>
        <img src="/arrow_long.svg" alt="Arrow Left" width={40} height={40} className={styles.arrowLeftImg} />
      </div>
      <div className={styles.overlay}>
        <div className={styles.contentWrapper}>
          <h1 className={styles.title}>Was ist KI Automation?</h1>
          <p className={styles.description}>
            KI Automation steht für die Verbindung von Künstlicher Intelligenz und Automatisierungstechnologien, um wiederkehrende Aufgaben und Prozesse automatisch und intelligent auszuführen. Während klassische Automatisierung vor allem auf festen Regeln basiert (z. B. „Wenn A, dann B“), geht KI Automation einen Schritt weiter:
            Sie nutzt lernende Systeme, die mitdenken, dazulernen und sich an neue Situationen anpassen können.
          </p>

          {/* Real World Example Section */}
          <h2 className={styles.infoHeadingExample}>KI-Automatisierung in Aktion: Praxisbeispiel im Gästeservice</h2>
          <p className={styles.description}>
            Um KI-Automatisierung greifbar zu machen, betrachten wir einen typischen Anwendungsfall: die Bearbeitung von Gästeanfragen. Dieses reale Beispiel zeigt in fünf Schritten, wie aus einer manuellen, zeitaufwendigen Aufgabe ein nahtloser, intelligenter Prozess wird. Vom Erkennen der Anfrage über die Analyse in Echtzeit bis hin zur automatischen, personalisierten Antwort und dem kontinuierlichen Lernen des Systems.
          </p>

          {/* Expanding Flex Cards Section */}
          <ExpandingFlexCards />

          <h2 className={styles.infoHeading}>Typische Technologien, die dabei zum Einsatz kommen, sind:</h2>
          <div className={styles.infoRow}>
            <div className={styles.infoBlock}>
              <div className={styles.infoLabel}>Robotic Process Automation (RPA)</div>
              <div className={styles.infoValue}>Für regelbasierte Aufgaben wie das Kopieren <br/>von Daten oder das Versenden von E-Mails.</div>
            </div>
            <div className={styles.infoBlock}>
              <div className={styles.infoLabel}>Machine Learning (ML)</div>
              <div className={styles.infoValue}>
                Damit Systeme aus Daten lernen und z. B. Vorhersagen treffen können.
              </div>
            </div>
            <div className={styles.infoBlock}>
              <div className={styles.infoLabel}>Natural Language Processing (NLP)</div>
              <div className={styles.infoValue}>Damit Maschinen menschliche Sprache verstehen und verarbeiten – z. B. Chats.</div>
            </div>
          </div>

          <div className={styles.statsGrid}>
            {/* Card 1 */}
            <div className={styles.statCard}>
              <div className={styles.statLabelColumn}>
                <div className={styles.statLabel} style={{width: '32px'}}>#001</div>
                <div className={styles.statLabel} style={{width: '48px'}}>V01</div>
                <div className={styles.statLabel} style={{width: '70px'}}>UPDATE<br/>20 JULY 25</div>
                <div className={styles.statLabel} style={{width: '80px'}}>AI. RESEARCH LAB 001</div>
              </div>
              <div className={styles.statNumber}>61%</div>
              <div className={styles.statDesc}>der Unternehmen nutzen bereits KI zur Automatisierung repetitiver Aufgaben</div>
            </div>
            {/* Card 2 */}
            <div className={styles.statCard}>
              {/*
              <div className={styles.statLabelColumn}>
                <div className={styles.statLabel} style={{width: '32px'}}>#001</div>
                <div className={styles.statLabel} style={{width: '48px'}}>V01</div>
                <div className={styles.statLabel} style={{width: '70px'}}>UPDATE<br/>20 JULY 25</div>
                <div className={styles.statLabel} style={{width: '80px'}}>AI. RESEARCH LAB 001</div>
              </div>
              */}
              {/* Empty card for visual spacing */}
            </div>
            {/* Card 3 */}
            <div className={styles.statCard}>
              <div className={styles.statLabelColumn}>
                <div className={styles.statLabel} style={{width: '32px'}}>#001</div>
                <div className={styles.statLabel} style={{width: '48px'}}>V01</div>
                <div className={styles.statLabel} style={{width: '70px'}}>UPDATE<br/>20 JULY 25</div>
                <div className={styles.statLabel} style={{width: '80px'}}>AI. RESEARCH LAB 001</div>
              </div>
              <div className={styles.statNumber}>80%</div>
              <div className={styles.statDesc}>der Geschäftsprozesse lassen sich bereits durch KI automatisieren</div>
            </div>
            {/* Card 4 */}
            <div className={styles.statCard}>
              <div className={styles.statLabelColumn}>
                <div className={styles.statLabel} style={{width: '32px'}}>#001</div>
                <div className={styles.statLabel} style={{width: '48px'}}>V01</div>
                <div className={styles.statLabel} style={{width: '70px'}}>UPDATE<br/>20 JULY 25</div>
                <div className={styles.statLabel} style={{width: '80px'}}>AI. RESEARCH LAB 001</div>
              </div>
              <div className={styles.statNumber}>2030<span className={styles.statUnit}></span></div>
              <div className={styles.statDesc}>wird KI voraussichtlich 15,7 Billionen USD zur Weltwirtschaft beitragen</div>
            </div>
            {/* Card 5 */}
            <div className={styles.statCard}>
              <div className={styles.statLabelColumn}>
                <div className={styles.statLabel} style={{width: '32px'}}>#001</div>
                <div className={styles.statLabel} style={{width: '48px'}}>V01</div>
                <div className={styles.statLabel} style={{width: '70px'}}>UPDATE<br/>20 JULY 25</div>
                <div className={styles.statLabel} style={{width: '80px'}}>AI. RESEARCH LAB 001</div>
              </div>
              <div className={styles.statNumber}>50%</div>
              <div className={styles.statDesc}>KI-gestützte Systeme reduzieren manuelle Fehler um bis zu 50%</div>
            </div>
            {/* Card 6 */}
            <div className={styles.statCard}>
              {/*
              <div className={styles.statLabelColumn}>
                <div className={styles.statLabel} style={{width: '32px'}}>#001</div>
                <div className={styles.statLabel} style={{width: '48px'}}>V01</div>
                <div className={styles.statLabel} style={{width: '70px'}}>UPDATE<br/>20 JULY 25</div>
                <div className={styles.statLabel} style={{width: '80px'}}>AI. RESEARCH LAB 001</div>
              </div>
              */}
              {/* Empty card for visual spacing */}
            </div>
            {/* Card 7 */}
            <div className={styles.statCard}>
              {/*
              <div className={styles.statLabelColumn}>
                <div className={styles.statLabel} style={{width: '32px'}}>#001</div>
                <div className={styles.statLabel} style={{width: '48px'}}>V01</div>
                <div className={styles.statLabel} style={{width: '70px'}}>UPDATE<br/>20 JULY 25</div>
                <div className={styles.statLabel} style={{width: '80px'}}>AI. RESEARCH LAB 001</div>
              </div>
              */}
              {/* Empty card for visual spacing */}
            </div>
            {/* Card 8 */}
            <div className={styles.statCard}>
              <div className={styles.statLabelColumn}>
                <div className={styles.statLabel} style={{width: '32px'}}>#001</div>
                <div className={styles.statLabel} style={{width: '48px'}}>V01</div>
                <div className={styles.statLabel} style={{width: '70px'}}>UPDATE<br/>20 JULY 25</div>
                <div className={styles.statLabel} style={{width: '80px'}}>AI. RESEARCH LAB 001</div>
              </div>
              <div className={styles.statNumber}>80%</div>
              <div className={styles.statDesc}>der Führungskräfte betrachten KI als entscheidend für den langfristigen Erfolg</div>
            </div>
            {/* Card 9 */}
            <div className={styles.statCard}>
              <div className={styles.statLabelColumn}>
                <div className={styles.statLabel} style={{width: '32px'}}>#001</div>
                <div className={styles.statLabel} style={{width: '48px'}}>V01</div>
                <div className={styles.statLabel} style={{width: '70px'}}>UPDATE<br/>20 JULY 25</div>
                <div className={styles.statLabel} style={{width: '80px'}}>AI. RESEARCH LAB 001</div>
              </div>
              <div className={styles.statNumber}>40%</div>
              <div className={styles.statDesc}>Unternehmen steigern ihre Produktivität im Schnitt um 40 % durch KI Automation</div>
            </div>
          </div>
          <div className={styles.divider}></div>
          <h2 className={styles.bigTitle}>Warum ist KI Automation so relevant für Unternehmen?</h2>
          <p className={styles.bigDescription}>
            In einer datengetriebenen und dynamischen Geschäftswelt sind Schnelligkeit, Effizienz und Präzision entscheidend. KI Automation hilft Unternehmen dabei, Prozesse nicht nur schneller und zuverlässiger abzuwickeln, sondern auch flexibel zu skalieren – unabhängig von Branche oder Abteilung. Ob in der Verwaltung, im Kundenservice, im Marketing oder in der Produktion: Automatisierte Systeme übernehmen wiederkehrende Aufgaben, arbeiten fehlerfrei und passen sich kontinuierlich an neue Anforderungen an. So entstehen nicht nur Zeit- und Kostenvorteile – auch Mitarbeitende werden entlastet und können sich auf die wirklich wertschöpfenden Aufgaben konzentrieren.
          </p>
          <div className={styles.newsGrid}>
            <div className={styles.newsCard}>
              <div className={styles.newsTop}>
                <div className={styles.newsTag}>ARTIKEL</div>
                <div className={styles.newsTitle}>KI im Einsatz: So schaffen Unternehmen echten Mehrwert mit Automatisierung</div>
              </div>
              <div className={styles.newsDate}>November 6, 2024</div>
              <a className={styles.newsButton} href="https://live.handelsblatt.com/ki-im-einsatz-so-schaffen-unternehmen-echten-mehrwert-mit-automatisierung/" target="_blank" rel="noopener noreferrer">Zum Artikel</a>
            </div>
            <div className={styles.newsCard}>
              {/* Empty card for Migration from SWIFT standards */}
            </div>
            <div className={styles.newsCard}>
              <div className={styles.newsTop}>
                <div className={styles.newsTag}>ARTIKEL</div>
                <div className={styles.newsTitle}>Hyperautomation durch Künstliche Intelligenz: Chancen und Risiken</div>
              </div>
              <div className={styles.newsDate}>April 29, 2025</div>
              <a className={styles.newsButton} href="https://www.informatik-aktuell.de/betrieb/kuenstliche-intelligenz/hyperautomation-durch-ki-chancen-und-risiken.html" target="_blank" rel="noopener noreferrer">Zum Artikel</a>
            </div>
            <div className={styles.newsCard}>
              <div className={styles.newsTop}>
                <div className={styles.newsTag}>ARTIKEL</div>
                <div className={styles.newsTitle}>Wie BPA und KI die Automatisierung von Geschäftsprozessen verändern</div>
              </div>
              <div className={styles.newsDate}>August 7, 2024</div>
              <a className={styles.newsButton} href="https://www.it-p.de/blog/ki-bpa-prozesse/" target="_blank" rel="noopener noreferrer">Zum Artikel</a>
            </div>
            <div className={styles.newsCard}>
              <div className={styles.newsTop}>
                <div className={styles.newsTag}>ARTIKEL</div>
                <div className={styles.newsTitle}>Künstliche Intelligenz in der Industrie: Einsatz, Beispiele und Vorteile</div>
              </div>
              <div className={styles.newsDate}>April 1, 2025</div>
              <a className={styles.newsButton} href="https://industriemagazin.at/fertigen/einsatz-der-ki-in-der-industrie/" target="_blank" rel="noopener noreferrer">Zum Artikel</a>
            </div>
            <div className={styles.newsCard}>
              {/* Empty card for All the latest news */}
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 