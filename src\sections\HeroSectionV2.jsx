import React, { Suspense, useRef, useState, useEffect, useMemo, useCallback } from 'react';
import { Canvas, useThree, useLoader, useFrame } from '@react-three/fiber';
import * as THREE from 'three';
import styles from './HeroSectionV2.module.css';
import WaterMatcapBackground from '../components/WaterMatcap/WaterMatcapBackground';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { OrbitControls } from '@react-three/drei';
import { easing } from 'maath';
import { useControls } from 'leva';
import { Cameras, ScrollCameraController, orbitCamera } from '../components/WaterMatcap/scene_copy/cameras2';

// Helper Grid Component
function HelperGrid({ size = 10, divisions = 10, position = [0, 0, 0], visible = true }) {
  const gridRef = useRef();

  useEffect(() => {
    if (!gridRef.current || !visible) return;

    // Clear previous helpers
    gridRef.current.clear();

    // Create main grid helper (XZ plane)
    const gridHelper = new THREE.GridHelper(size, divisions, 0x888888, 0x444444);
    gridHelper.position.set(...position);
    gridRef.current.add(gridHelper);

    // Create vertical grid (XY plane)
    const verticalGridGeometry = new THREE.PlaneGeometry(size, size, divisions, divisions);
    const verticalGridMaterial = new THREE.MeshBasicMaterial({
      color: 0x333333,
      wireframe: true,
      transparent: true,
      opacity: 0.3
    });
    const verticalGrid = new THREE.Mesh(verticalGridGeometry, verticalGridMaterial);
    verticalGrid.rotation.x = Math.PI / 2;
    verticalGrid.position.set(position[0], position[1], position[2]);
    gridRef.current.add(verticalGrid);

    // Create side grid (YZ plane)
    const sideGridGeometry = new THREE.PlaneGeometry(size, size, divisions, divisions);
    const sideGridMaterial = new THREE.MeshBasicMaterial({
      color: 0x333333,
      wireframe: true,
      transparent: true,
      opacity: 0.2
    });
    const sideGrid = new THREE.Mesh(sideGridGeometry, sideGridMaterial);
    sideGrid.rotation.y = Math.PI / 2;
    sideGrid.position.set(position[0], position[1], position[2]);
    gridRef.current.add(sideGrid);

    // Create axes helper
    const axesHelper = new THREE.AxesHelper(size / 2);
    axesHelper.position.set(...position);
    gridRef.current.add(axesHelper);

    // Add coordinate labels at grid intersections
    const labelStep = Math.max(1, Math.floor(size / 10));
    for (let x = -size/2; x <= size/2; x += labelStep) {
      for (let z = -size/2; z <= size/2; z += labelStep) {
        if (x === 0 && z === 0) continue; // Skip origin

        // Create text sprite for coordinates
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 128;
        canvas.height = 32;

        context.fillStyle = '#ffffff';
        context.font = '12px Arial';
        context.textAlign = 'center';
        context.fillText(`${x},${z}`, 64, 20);

        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({
          map: texture,
          transparent: true,
          opacity: 0.6
        });
        const sprite = new THREE.Sprite(material);

        sprite.position.set(
          position[0] + x,
          position[1] + 0.1,
          position[2] + z
        );
        sprite.scale.set(0.5, 0.125, 1);

        gridRef.current.add(sprite);
      }
    }

    return () => {
      if (gridRef.current) {
        gridRef.current.clear();
      }
    };
  }, [size, divisions, position, visible]);

  if (!visible) return null;

  return <group ref={gridRef} />;
}

// Coordinate Display Component
function CoordinateDisplay({ position = [0, 0, 0], visible = true, label = "Object" }) {
  const textRef = useRef();

  useEffect(() => {
    if (!textRef.current || !visible) return;

    // Clear previous sprites
    textRef.current.clear();

    // Create text geometry for coordinates
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 400;
    canvas.height = 80;

    // Background
    context.fillStyle = 'rgba(0, 0, 0, 0.7)';
    context.fillRect(0, 0, 400, 80);

    // Border
    context.strokeStyle = '#ffffff';
    context.lineWidth = 2;
    context.strokeRect(2, 2, 396, 76);

    // Text
    context.fillStyle = '#ffffff';
    context.font = 'bold 14px Arial';
    context.textAlign = 'center';
    context.fillText(`${label}:`, 200, 25);
    context.font = '12px monospace';
    context.fillText(`X: ${position[0].toFixed(2)}  Y: ${position[1].toFixed(2)}  Z: ${position[2].toFixed(2)}`, 200, 50);

    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.SpriteMaterial({
      map: texture,
      transparent: true,
      alphaTest: 0.1
    });
    const sprite = new THREE.Sprite(material);

    sprite.position.set(position[0], position[1] + 1.0, position[2]);
    sprite.scale.set(2, 0.4, 1);

    textRef.current.add(sprite);

    // Add a small marker at the object position
    const markerGeometry = new THREE.SphereGeometry(0.05, 8, 8);
    const markerMaterial = new THREE.MeshBasicMaterial({
      color: 0xff0000,
      transparent: true,
      opacity: 0.8
    });
    const marker = new THREE.Mesh(markerGeometry, markerMaterial);
    marker.position.set(position[0], position[1], position[2]);
    textRef.current.add(marker);

    return () => {
      if (textRef.current) {
        textRef.current.clear();
      }
    };
  }, [position, visible, label]);

  if (!visible) return null;

  return <group ref={textRef} />;
}

// Simple Axes Helper Component
function AxesHelper({ size = 5, position = [0, 0, 0], visible = true }) {
  const axesRef = useRef();

  useEffect(() => {
    if (!axesRef.current || !visible) return;

    axesRef.current.clear();

    const axesHelper = new THREE.AxesHelper(size);
    axesHelper.position.set(...position);
    axesRef.current.add(axesHelper);

    return () => {
      if (axesRef.current) {
        axesRef.current.clear();
      }
    };
  }, [size, position, visible]);

  if (!visible) return null;

  return <group ref={axesRef} />;
}

function BlackBackground() {
  const { scene } = useThree();
  React.useEffect(() => {
    scene.background = new THREE.Color('#000000');
    return () => {
      scene.background = null;
    };
  }, [scene]);
  return null;
}

// Original Block1Mesh component (commented out for potential future use)
/*
const Block1Mesh = React.forwardRef(function Block1Mesh(props, ref) {
  const gl = useThree((state) => state.gl);
  const geometry = useLoader(
    DRACOLoader,
    '/models/block2.drc',
    loader => {
      loader.setDecoderPath('https://www.gstatic.com/draco/v1/decoders/');
    }
  );
  const normalMap = useLoader(KTX2Loader, '/textures/block2_normal.ktx2', loader => {
    loader.setTranscoderPath('https://unpkg.com/three@0.152.2/examples/jsm/libs/basis/');
    loader.detectSupport(gl);
  });
  const roughnessMap = useLoader(KTX2Loader, '/textures/block2_roughness.ktx2', loader => {
    loader.setTranscoderPath('https://unpkg.com/three@0.152.2/examples/jsm/libs/basis/');
    loader.detectSupport(gl);
  });
  return (
    <mesh ref={ref} geometry={geometry} {...props}>
      <MeshTransmissionMaterial
        normalMap={normalMap}
        roughnessMap={roughnessMap}
        color={'#fff'}
        backside
        samples={4}
        thickness={3}
        chromaticAberration={0.01}
        anisotropy={0.1}
        distortion={0}
        distortionScale={0}
        temporalDistortion={0}
        iridescence={1}
        iridescenceIOR={1}
        iridescenceThicknessRange={[0, 1400]}
        side={THREE.DoubleSide}
      />
    </mesh>
  );
});
*/

// New Block2CellMesh component using GLB loader with DRACO compression support and explosion effect
const Block1Mesh = React.forwardRef(function Block1Mesh({ displacement = 0.5, intensity = 1, explosionProgress = 0, ...props }, ref) {
  const gltf = useLoader(
    GLTFLoader,
    '/models/sphere.glb',
    loader => {
      const dracoLoader = new DRACOLoader();
      dracoLoader.setDecoderPath('https://www.gstatic.com/draco/v1/decoders/');
      loader.setDRACOLoader(dracoLoader);
    }
  );

  // Load the normal map texture
  const normalMap = useLoader(THREE.TextureLoader, '/textures/sphere.normal.jpg');

  const groupRef = useRef();
  const debrisGroupRef = useRef();
  const vec = useMemo(() => new THREE.Vector3(), []);
  const [isHovering, setIsHovering] = useState(false);
  const [hoverPoint, setHoverPoint] = useState(new THREE.Vector3());
  const [mouseVelocity, setMouseVelocity] = useState(new THREE.Vector3());
  const previousMousePos = useRef(new THREE.Vector3());
  const [debrisMeshes, setDebrisMeshes] = useState([]);

  // Apply performance-friendly glass material with normal map
  useEffect(() => {
    if (gltf.scene && normalMap) {
      gltf.scene.traverse((child) => {
        if (child.isMesh && child.material) {
          // Create a performance-friendly glass material using MeshPhysicalMaterial
          const glassMaterial = new THREE.MeshPhysicalMaterial({
            color: '#000000ff',
            normalMap: normalMap,
            metalness: 0,
            roughness: 0.1,
            transmission: 0.9,  // Glass-like transparency
            transparent: true,
            opacity: 0.90,
            clearcoat: 1.0,  // Adds glass-like reflection
            clearcoatRoughness: 0.1,
            ior: 1.5,  // Index of refraction for glass
            thickness: 0.5,  // Much thinner than MeshTransmissionMaterial
            envMapIntensity: 1.0,
            side: THREE.FrontSide,  // Single-sided for better performance
          });

          // Apply the glass material
          child.material = glassMaterial;
          child.material.needsUpdate = true;
        }
      });
    }
  }, [gltf.scene, normalMap]);

  // Create debris instances from specific cells
  useEffect(() => {
    if (!gltf.scene) return;

    const debrisObjectNames = ["Sphere_cell108_cell006", "Sphere_cell122_cell006"];
    const debrisCount = 400; // Reduced for better performance
    const debrisPerType = Math.round(debrisCount / debrisObjectNames.length);
    const newDebrisMeshes = [];

    debrisObjectNames.forEach(objectName => {
      // Find the specific cell in the GLB
      let debrisGeometry = null;
      gltf.scene.traverse((child) => {
        if (child.isMesh && child.name === objectName) {
          debrisGeometry = child.geometry.clone();
        }
      });

      if (debrisGeometry) {
        // Create instanced mesh for this debris type
        const debrisMaterial = new THREE.MeshPhysicalMaterial({
          color: '#000000ff',
          normalMap: normalMap,
          metalness: 0,
          roughness: 0.1,
          transmission: 0.9,
          transparent: true,
          opacity: 0.0, // Start invisible
          clearcoat: 1.0,
          clearcoatRoughness: 0.1,
          ior: 1.5,
          thickness: 0.5,
          envMapIntensity: 1.0,
          side: THREE.FrontSide,
        });

        const instancedMesh = new THREE.InstancedMesh(debrisGeometry, debrisMaterial, debrisPerType);

        // Set random positions for debris instances
        const matrix = new THREE.Matrix4();
        const position = new THREE.Vector3();
        const rotation = new THREE.Euler();
        const scale = new THREE.Vector3();

        for (let i = 0; i < debrisPerType; i++) {
          // Random position INSIDE the sphere - 50% wider distribution
          const radius = Math.random() * 1.2; // Increased from 0.8 to 1.2 (50% wider)
          const theta = Math.random() * Math.PI * 2; // Azimuth angle
          const phi = Math.acos(2 * Math.random() - 1); // Polar angle for uniform distribution

          position.set(
            radius * Math.sin(phi) * Math.cos(theta),
            radius * Math.sin(phi) * Math.sin(theta),
            radius * Math.cos(phi)
          );

          // Random rotation
          rotation.set(
            Math.random() * Math.PI * 2,
            Math.random() * Math.PI * 2,
            Math.random() * Math.PI * 2
          );

          // Random sizes - much wider range
          const scaleValue = 0.05 + Math.random() * 1.15; // 0.05 to 1.20 (wide range of sizes)
          scale.set(scaleValue, scaleValue, scaleValue);

          matrix.compose(position, new THREE.Quaternion().setFromEuler(rotation), scale);
          instancedMesh.setMatrixAt(i, matrix);
        }

        instancedMesh.instanceMatrix.needsUpdate = true;
        newDebrisMeshes.push({
          mesh: instancedMesh,
          material: debrisMaterial, // Store material reference for opacity control
          originalPositions: Array.from({length: debrisPerType}, (_, i) => {
            const matrix = new THREE.Matrix4();
            instancedMesh.getMatrixAt(i, matrix);
            const pos = new THREE.Vector3();
            matrix.decompose(pos, new THREE.Quaternion(), new THREE.Vector3());
            return pos.clone();
          }),
          rotationSpeeds: Array.from({length: debrisPerType}, () => ({
            x: (Math.random() - 0.5) * 0.02, // Random rotation speeds
            y: (Math.random() - 0.5) * 0.02,
            z: (Math.random() - 0.5) * 0.02
          })),
          currentRotations: Array.from({length: debrisPerType}, () => ({
            x: 0,
            y: 0,
            z: 0
          }))
        });
      }
    });

    setDebrisMeshes(newDebrisMeshes);
  }, [gltf.scene, normalMap]);

  // Store original positions of all mesh children
  const originalPositions = useMemo(() => {
    if (!gltf.scene) return [];
    const positions = [];
    gltf.scene.traverse((child) => {
      if (child.isMesh) {
        positions.push({
          mesh: child,
          originalPosition: child.position.clone(),
          originalColor: child.material ? (child.material.color ? child.material.color.clone() : new THREE.Color(1, 1, 1)) : new THREE.Color(1, 1, 1)
        });
      }
    });
    return positions;
  }, [gltf.scene]);

  // Handle pointer events on the block
  const handlePointerMove = useCallback((event) => {
    if (event.point) {
      // Calculate mouse velocity for directional effect
      const currentMousePos = event.point.clone();
      const velocity = currentMousePos.clone().sub(previousMousePos.current);

      setHoverPoint(currentMousePos);
      setMouseVelocity(velocity);
      setIsHovering(true);

      // Update previous position for next frame
      previousMousePos.current.copy(currentMousePos);
    }
  }, []);

  const handlePointerLeave = useCallback(() => {
    setIsHovering(false);
  }, []);

  useFrame(({ clock }, delta) => {
    if (!groupRef.current || originalPositions.length === 0) return;

    // Apply ambient animation to all pieces (always running)
    originalPositions.forEach(({ mesh, originalPosition, originalColor }, index) => {
      // Calculate direction from center (0,0,0) to this piece
      const distanceFromCenter = originalPosition.length();
      let directionFromCenter;

      // Special handling for pieces too close to center (avoid division by zero and weird behavior)
      if (distanceFromCenter < 0.1) {
        // For center pieces, use a default forward direction (towards camera)
        directionFromCenter = new THREE.Vector3(0, 0, 1);
      } else {
        directionFromCenter = originalPosition.clone().normalize();
      }

      // Create overlapping animation zones for continuous, random-like movement
      const animationGroup = index % 8; // 8 different animation patterns

      // Calculate spherical coordinates for wave propagation
      const phi = Math.atan2(originalPosition.z, originalPosition.x); // Azimuth angle
      const theta = Math.acos(originalPosition.y / distanceFromCenter); // Polar angle

      let baseAnimatedPosition;

      // Create multiple overlapping wave zones that move independently and continuously
      // Each zone has different timing and phase offsets to create constant movement

      // Zone 1: Top hemisphere waves
      const topZoneWave = Math.sin(clock.elapsedTime * 1.8 + phi * 2.5 + theta * 1.2 + index * 0.1);

      // Zone 2: Bottom hemisphere waves
      const bottomZoneWave = Math.sin(clock.elapsedTime * 2.2 + phi * 1.8 + theta * 2.0 + index * 0.15);

      // Zone 3: Left side waves
      const leftZoneWave = Math.sin(clock.elapsedTime * 1.6 + phi * 3.0 + theta * 0.8 + index * 0.08);

      // Zone 4: Right side waves
      const rightZoneWave = Math.sin(clock.elapsedTime * 2.4 + phi * 1.5 + theta * 1.8 + index * 0.12);

      // Zone 5: Front waves
      const frontZoneWave = Math.sin(clock.elapsedTime * 2.0 + phi * 2.2 + theta * 1.5 + index * 0.09);

      // Zone 6: Back waves
      const backZoneWave = Math.sin(clock.elapsedTime * 1.4 + phi * 1.2 + theta * 2.5 + index * 0.11);

      // Determine which zones this piece belongs to based on its position
      const isTop = originalPosition.y > 0;
      const isLeft = originalPosition.x < 0;
      const isFront = originalPosition.z > 0;

      // Combine waves based on position (pieces can belong to multiple zones)
      let combinedWave = 0;
      let waveCount = 0;

      if (isTop) {
        combinedWave += topZoneWave;
        waveCount++;
      } else {
        combinedWave += bottomZoneWave;
        waveCount++;
      }

      if (isLeft) {
        combinedWave += leftZoneWave;
        waveCount++;
      } else {
        combinedWave += rightZoneWave;
        waveCount++;
      }

      if (isFront) {
        combinedWave += frontZoneWave;
        waveCount++;
      } else {
        combinedWave += backZoneWave;
        waveCount++;
      }

      // Average the waves to prevent excessive movement
      combinedWave = combinedWave / waveCount;

      // Add individual randomness for more organic feel
      const individualOffset = Math.sin(clock.elapsedTime * 2.6 + index * 0.2 + phi * 0.5 + theta * 0.3) * 0.3;
      combinedWave += individualOffset;

      // Amplitude based on animation group and distance from center
      let amplitude;
      const distanceFactor = Math.min(1.0, distanceFromCenter / 2.0); // Reduce amplitude for inner pieces

      if (animationGroup === 0) {
        amplitude = 0.04 * distanceFactor; // Minimal movement
      } else if (animationGroup === 1) {
        amplitude = 0.08 * distanceFactor; // Light movement
      } else if (animationGroup === 2) {
        amplitude = 0.12 * distanceFactor; // Medium movement
      } else if (animationGroup === 3) {
        amplitude = 0.10 * distanceFactor; // Medium movement
      } else if (animationGroup === 4) {
        amplitude = 0.14 * distanceFactor; // Strong movement
      } else if (animationGroup === 5) {
        amplitude = 0.09 * distanceFactor; // Medium-light movement
      } else if (animationGroup === 6) {
        amplitude = 0.11 * distanceFactor; // Medium-strong movement
      } else {
        amplitude = 0.07 * distanceFactor; // Light-medium movement
      }

      // Apply continuous radial movement (asymmetric for outward bias)
      const radialMovement = combinedWave > 0 ? combinedWave * amplitude : combinedWave * (amplitude * 0.4);
      const radialOffset = directionFromCenter.clone().multiplyScalar(radialMovement);

      // Add explosion effect based on explosionProgress
      const explosionOffset = directionFromCenter.clone().multiplyScalar(explosionProgress * 3.0); // Explosion strength

      baseAnimatedPosition = vec.copy(originalPosition).add(radialOffset).add(explosionOffset);

      // Add hover effect on top of ambient animation
      let finalPosition = baseAnimatedPosition.clone();
      let finalColor = originalColor.clone();

      if (isHovering && hoverPoint) {
        // Get world position of the mesh
        const worldPosition = new THREE.Vector3();
        mesh.getWorldPosition(worldPosition);

        // Calculate distance from hover point to mesh in world space
        const dist = worldPosition.distanceTo(hoverPoint);

        // Apply directional destroying effect within displacement radius
        if (dist < displacement) {
          // Calculate displacement strength
          const distanceRatio = 1 - (dist / displacement);
          const intensity = distanceRatio * distanceRatio; // Quadratic falloff

          // Directional effect based on mouse velocity
          const velocityMagnitude = mouseVelocity.length();
          const normalizedVelocity = velocityMagnitude > 0.001 ? mouseVelocity.clone().normalize() : new THREE.Vector3(0, 0, 1);

          // Convert world velocity to local space
          const worldToLocal = new THREE.Matrix4().copy(groupRef.current.matrixWorld).invert();
          const localVelocity = normalizedVelocity.clone().transformDirection(worldToLocal);

          // Apply directional displacement (pieces move in mouse direction)
          const directionalForce = localVelocity.multiplyScalar(intensity * velocityMagnitude * 8.0); // Strong directional effect

          // Always apply a minimum radial outward force, even when mouse is stationary
          const baseRadialForce = directionFromCenter.clone().multiplyScalar(intensity * 1.2); // Stronger base outward expansion

          // Additional radial force when mouse is moving
          const velocityRadialForce = directionFromCenter.clone().multiplyScalar(intensity * velocityMagnitude * 2.0);

          // Combine all forces: directional + base radial + velocity-based radial
          const totalDisplacement = directionalForce.add(baseRadialForce).add(velocityRadialForce);
          finalPosition.add(totalDisplacement);

          // Keep original color during hover (no color change)
          finalColor = originalColor.clone();
        }
      }

      // Apply the final position and color with much faster damping for smooth movement
      easing.damp3(mesh.position, finalPosition, 0.8, delta); // Much faster damping for smooth movement

      if (mesh.material && mesh.material.color) {
        easing.dampC(mesh.material.color, finalColor, 0.6, delta); // Faster color transitions
      }

      // Apply rotation to more moving pieces for enhanced living effect
      if (animationGroup >= 2 && animationGroup <= 6) {
        const rotationSpeed = animationGroup === 3 || animationGroup === 5 ? 0.8 : 0.6; // Varied rotation speeds
        const rotationOffset = Math.sin(clock.elapsedTime * rotationSpeed + index * 0.2) * 0.04;
        mesh.rotation.y = originalPosition.y * 0.1 + rotationOffset;

        // Add subtle X and Z rotation for more organic movement
        if (animationGroup === 3 || animationGroup === 4) {
          mesh.rotation.x = Math.sin(clock.elapsedTime * 0.7 + index * 0.15) * 0.03;
          mesh.rotation.z = Math.cos(clock.elapsedTime * 0.5 + index * 0.1) * 0.02;
        }
      }
    });

    // Add slower, more subtle rotation to the whole sphere
    if (groupRef.current) {
      groupRef.current.rotation.y = clock.elapsedTime * 0.05; // Much slower rotation around Y-axis
      groupRef.current.rotation.x = Math.sin(clock.elapsedTime * 0.2) * 0.03; // Slower, subtler wobble on X-axis
    }

    // Animate debris instances (no hover effects, smooth rotation)
    debrisMeshes.forEach(({ mesh, material, originalPositions, rotationSpeeds, currentRotations }) => {
      if (!mesh || !material || !rotationSpeeds || !currentRotations) return;

      // Control debris visibility based on explosion progress
      // Debris becomes visible starting at 40% scroll (explosionProgress = 0.4)
      if (explosionProgress < 0.4) {
        mesh.visible = false; // Completely hide the mesh before 40% scroll
      } else {
        mesh.visible = true; // Make mesh visible
        // Fade in from 40% to 50% scroll, then stay at full opacity
        const fadeProgress = Math.min(1.0, (explosionProgress - 0.4) / 0.1); // 0.4 to 0.5 range
        material.opacity = fadeProgress * 0.90; // Fade to full opacity (0.90)
      }

      const matrix = new THREE.Matrix4();
      const position = new THREE.Vector3();
      const rotation = new THREE.Euler();
      const scale = new THREE.Vector3();

      for (let i = 0; i < originalPositions.length; i++) {
        const originalPos = originalPositions[i];

        // Calculate direction from center for explosion
        const directionFromCenter = originalPos.clone().normalize();

        // Add ambient movement (smaller than main pieces)
        const ambientOffset = Math.sin(clock.elapsedTime * 1.5 + i * 0.1) * 0.01;
        const ambientMovement = directionFromCenter.clone().multiplyScalar(ambientOffset);

        // Add explosion effect - debris moves outward but stays more centered
        const explosionOffset = directionFromCenter.clone().multiplyScalar(explosionProgress * 1.0); // Small movement to stay inside/center

        // Calculate final position
        const finalPosition = originalPos.clone().add(ambientMovement).add(explosionOffset);

        // Get current matrix and update position
        mesh.getMatrixAt(i, matrix);
        matrix.decompose(position, new THREE.Quaternion().setFromEuler(rotation), scale);

        // Update position
        position.copy(finalPosition);

        // Smooth rotation using stored speeds (no jumping)
        currentRotations[i].x += rotationSpeeds[i].x;
        currentRotations[i].y += rotationSpeeds[i].y;
        currentRotations[i].z += rotationSpeeds[i].z;

        rotation.set(currentRotations[i].x, currentRotations[i].y, currentRotations[i].z);

        matrix.compose(position, new THREE.Quaternion().setFromEuler(rotation), scale);
        mesh.setMatrixAt(i, matrix);
      }

      mesh.instanceMatrix.needsUpdate = true;
    });
  });

  return (
    <group
      ref={groupRef}
      {...props}
      onPointerMove={handlePointerMove}
      onPointerLeave={handlePointerLeave}
    >
      <primitive
        ref={ref}
        object={gltf.scene}
      />
      {/* Render debris instances */}
      <group ref={debrisGroupRef}>
        {debrisMeshes.map((debrisData, index) => (
          <primitive key={index} object={debrisData.mesh} />
        ))}
      </group>
    </group>
  );
});







// Camera position tracker component (inside Canvas)
function CameraPositionTracker({ onPositionChange }) {
  const { camera } = useThree();

  useFrame(() => {
    // Always show the currently active camera position
    onPositionChange({
      position: [
        Math.round(camera.position.x * 100) / 100,
        Math.round(camera.position.y * 100) / 100,
        Math.round(camera.position.z * 100) / 100
      ],
      rotation: [
        Math.round(camera.rotation.x * 100) / 100,
        Math.round(camera.rotation.y * 100) / 100,
        Math.round(camera.rotation.z * 100) / 100
      ],
      cameraType: camera === orbitCamera ? 'Orbit' : 'Main'
    });
  });

  return null;
}

export default function HeroSection() {
  // Camera position state
  const [cameraInfo, setCameraInfo] = useState({
    position: [0, 0, 0],
    rotation: [0, 0, 0],
    cameraType: 'Main'
  });

  const {
    blockPositionA,
    blockRotationA,
    blockPositionB,
    blockRotationB,
    showGrid,
    gridSize,
    gridDivisions,
    gridPosition,
    showCoordinates,
    showAxes,
    enableOrbitControls
  } = useControls({
    // Block Controls
    blockPositionA: {
      value: [0, 0.4, 0.70],
      label: 'Block Position A (Start)',
      step: 0.01,
      min: -10,
      max: 10,
    },
    blockRotationA: {
      value: [0, 0, 0],
      label: 'Block Rotation A (Start)',
      step: 0.01,
      min: -Math.PI,
      max: Math.PI,
    },
    blockPositionB: {
      value: [0, 0.40, 1.25],
      label: 'Block Position B (Final)',
      step: 0.01,
      min: -10,
      max: 10,
    },
    blockRotationB: {
      value: [0, 0, 0],
      label: 'Block Rotation B (Final)',
      step: 0.01,
      min: -Math.PI,
      max: Math.PI,
    },
    // Helper Grid Controls
    showGrid: {
      value: true,
      label: 'Show Helper Grid'
    },
    gridSize: {
      value: 10,
      label: 'Grid Size',
      min: 1,
      max: 50,
      step: 1
    },
    gridDivisions: {
      value: 20,
      label: 'Grid Divisions',
      min: 5,
      max: 100,
      step: 5
    },
    gridPosition: {
      value: [0, 0, 0],
      label: 'Grid Position',
      step: 0.1,
      min: -10,
      max: 10
    },
    showCoordinates: {
      value: true,
      label: 'Show Object Coordinates'
    },
    showAxes: {
      value: true,
      label: 'Show Axes Helper'
    },
    // Camera Controls
    enableOrbitControls: {
      value: false,
      label: 'Enable Orbit Controls'
    }
  });

  // Simple scroll-based navigation state
  const [scrollProgress, setScrollProgress] = useState(0);
  const [cameraProgress, setCameraProgress] = useState(0);
  const containerRef = useRef(null);

  // Handle simple hybrid scroll navigation
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return;
      const scrollTop = window.scrollY;
      const windowHeight = window.innerHeight;
      const totalHeight = windowHeight * 6; // 6 sections total for more scroll range

      // Calculate overall scroll progress (0 to 1)
      const overallProgress = Math.max(0, Math.min(1, scrollTop / totalHeight));

      if (overallProgress <= 0.5) {
        // Phase 1: Block movement A → B (0% to 50% of total scroll)
        const blockProgress = overallProgress * 2; // 0 to 1
        setScrollProgress(blockProgress);
        setCameraProgress(0); // Camera stays at original position
      } else {
        // Phase 2: Camera movement (50% to 100% of total scroll)
        setScrollProgress(1); // Blocks stay at position B
        const cameraProgressValue = (overallProgress - 0.5) * 2; // 0 to 1
        setCameraProgress(cameraProgressValue);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);



  // Calculate explosion progress based on scroll (starts from 0%)
  const explosionProgress = scrollProgress; // 0 to 1 range for explosion, starts immediately

  // Simple block position calculation: A → B
  const currentPosition = blockPositionA.map((v, i) => v + (blockPositionB[i] - v) * scrollProgress);
  const currentRotation = blockRotationA.map((v, i) => v + (blockRotationB[i] - v) * scrollProgress);

  return (
    <div ref={containerRef} style={{ position: 'relative', width: '100vw', height: '600vh', overflow: 'auto' }}>
      {/* Fixed 3D background */}
      <Canvas
        camera={{ position: [0, 3, 0], fov: 45 }}
        gl={{ alpha: true, antialias: true, powerPreference: 'high-performance', clearColor: [0, 0, 0, 1] }}
        className={styles.appAICanvas}
        style={{ position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', zIndex: 0 }}
      >
        <Suspense fallback={null}>
          <BlackBackground />
          {/* Camera system */}
          <Cameras />
          <ScrollCameraController
            cameraProgress={cameraProgress}
          />
          {/* Orbit Controls */}
          {enableOrbitControls && (
            <OrbitControls
              enablePan={true}
              enableZoom={true}
              enableRotate={true}
              enableDamping={true}
              dampingFactor={0.05}
              minDistance={1}
              maxDistance={50}
            />
          )}

          {/* Helper Grid */}
          {showGrid && (
            <HelperGrid
              size={gridSize}
              divisions={gridDivisions}
              position={gridPosition}
              visible={showGrid}
            />
          )}

          {/* Separate Axes Helper */}
          {showAxes && (
            <AxesHelper
              size={gridSize / 2}
              position={[0, 0, 0]}
              visible={showAxes}
            />
          )}

          <ambientLight intensity={0.5} />
          <pointLight position={[5, 5, 5]} intensity={1} />
          <Block1Mesh
            position={currentPosition}
            rotation={currentRotation}
            displacement={0.6}
            intensity={0.15}
            explosionProgress={explosionProgress}
            scale={[0.40, 0.40, 0.40]}
          />

          {/* Coordinate Display for Block */}
          {showCoordinates && (
            <CoordinateDisplay
              position={currentPosition}
              visible={showCoordinates}
              label="Sphere Block"
            />
          )}

          <WaterMatcapBackground position={[0, 0, 0]} />
          <CameraPositionTracker onPositionChange={setCameraInfo} />
        </Suspense>
      </Canvas>

      {/* Camera Position Display */}
      <div style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        background: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '10px',
        borderRadius: '5px',
        fontFamily: 'monospace',
        fontSize: '12px',
        zIndex: 1000
      }}>
        <div>{cameraInfo.cameraType} Camera Position:</div>
        <div>x: {cameraInfo.position[0]}, y: {cameraInfo.position[1]}, z: {cameraInfo.position[2]}</div>
        <div>{cameraInfo.cameraType} Camera Rotation:</div>
        <div>x: {cameraInfo.rotation[0]}, y: {cameraInfo.rotation[1]}, z: {cameraInfo.rotation[2]}</div>
        <div style={{ marginTop: '10px', borderTop: '1px solid #333', paddingTop: '10px' }}>
          <div>Hybrid Navigation:</div>
          <div>Block Progress: {Math.round(scrollProgress * 100)}%</div>
          <div>Camera Progress: {Math.round(cameraProgress * 100)}%</div>
          <div>Phase: {cameraProgress > 0 ? 'Camera Movement' : 'Block Movement'}</div>
          <div style={{ marginTop: '5px', fontSize: '10px', opacity: 0.7 }}>
            <div>Scroll Y: {Math.round(window.scrollY)}</div>
            <div>Total Height: {Math.round(window.innerHeight * 6)}</div>
            <div>Overall Progress: {Math.round((window.scrollY / (window.innerHeight * 6)) * 100)}%</div>
          </div>
        </div>
      </div>


    </div>
  );
}