import React, { useEffect, useRef } from 'react';
import gsap from 'gsap';
import { CustomEase } from 'gsap/CustomEase';
import { SplitText } from 'gsap/SplitText';
import styles from './Preloader.module.css';

const Preloader = ({ onComplete }) => {
  const preloaderRef = useRef(null);
  const splitOverlayRef = useRef(null);
  const containerRef = useRef(null);
  const introTitleRef = useRef(null);
  const outroTitleRef = useRef(null);
  const heroImgRef = useRef(null);
  const scrollBlockerRef = useRef(null);

  useEffect(() => {
    // Register GSAP plugins
    gsap.registerPlugin(CustomEase, SplitText);

    // Create custom ease
    CustomEase.create("hop", ".8, 0, .3, 1");
    
    // CRITICAL: Create a direct style element to override ANY scrolling
    const styleElement = document.createElement('style');
    styleElement.innerHTML = `
      html, body {
        overflow: hidden !important;
        height: 100% !important; 
        position: fixed !important;
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        touch-action: none !important;
      }
      .lenis, .lenis.lenis-smooth {
        overflow: hidden !important;
      }
    `;
    document.head.appendChild(styleElement);
    
    // Handle direct scroll event blocking for ALL elements
    const handleScroll = (e) => {
      e.preventDefault();
      e.stopPropagation();
      return false;
    };
    
    // Add capturing phase event listeners to ALL scroll events
    document.addEventListener('wheel', handleScroll, { capture: true, passive: false });
    document.addEventListener('touchmove', handleScroll, { capture: true, passive: false });
    document.addEventListener('scroll', handleScroll, { capture: true, passive: false });
    document.addEventListener('keydown', (e) => {
      if ([32, 33, 34, 35, 36, 37, 38, 39, 40].indexOf(e.keyCode) > -1) {
        e.preventDefault();
        return false;
      }
    }, { capture: true, passive: false });

    // Function to enable scrolling
    const enableScroll = () => {
      // Remove style element
      if (styleElement && styleElement.parentNode) {
        document.head.removeChild(styleElement);
      }
      
      // Remove event listeners
      document.removeEventListener('wheel', handleScroll, { capture: true });
      document.removeEventListener('touchmove', handleScroll, { capture: true });
      document.removeEventListener('scroll', handleScroll, { capture: true });
    };

    // Ensure DOM is fully ready before starting animations
    const initializeAnimation = () => {
      // Split text elements function
      const splitTextElements = (
        selector,
        type = "words,chars",
        addSpecialChars = false,
        firstCharIndex = 0,
        specialCharIndex = 8 // 8th char (0-indexed)
      ) => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element) => {
          const splitText = new SplitText(element, {
            type,
            wordsClass: "word",
            charsClass: "char",
          });

          if (type.includes("chars")) {
            splitText.chars.forEach((char, index) => {
              const originalText = char.textContent;
              char.innerHTML = `<span>${originalText}</span>`;

              if (addSpecialChars) {
                if (index === firstCharIndex) {
                  char.classList.add("first-char");
                }
                if (index === specialCharIndex) {
                  char.classList.add("special-char");
                }
              }
            });
          }
        });
      };

      // Reset any previous animations
      gsap.set(`.${styles.introTitle} .char span, .${styles.outroTitle} .char span`, {
        clearProps: "all"
      });
      gsap.set(`.${styles.preloaderTag} p .word`, { clearProps: "all" });
      gsap.set(`.${styles.container}`, { clipPath: "polygon(0 48%, 0 48%, 0 52%, 0 52%)" });
      gsap.set(`.${styles.preloader}`, { y: "0%" });
      gsap.set(`.${styles.splitOverlay}`, { y: "0%" });
      gsap.set(`.${styles.heroImg}`, { opacity: 1 }); // Initialize hero image opacity
      
      // Initialize split text elements with the first and special characters
      splitTextElements(`.${styles.introTitle} h1`, "words, chars", true);
      splitTextElements(`.${styles.outroTitle} h1`);
      splitTextElements(`.${styles.preloaderTag} p`, "words");
      
      // Check if mobile
      const isMobile = window.innerWidth < 1000;

      // Ensure elements are visible before animation
      gsap.set(
        [
          `.${styles.introTitle} .char span`,
          `.${styles.outroTitle} .char span`,
        ],
        { 
          y: "-100%",
          opacity: 1,
          visibility: "visible"
        }
      );

      // Setup initial positions for split overlay
      gsap.set(
        [
          `.${styles.splitOverlay} .${styles.introTitle} .first-char span`,
          `.${styles.splitOverlay} .${styles.introTitle} .special-char span`,
          `.${styles.splitOverlay} .${styles.outroTitle} .char span`,
        ],
        { y: "0%" }
      );

      // Set initial position for first char - no scale, only horizontal movement
      gsap.set(`.${styles.splitOverlay} .${styles.introTitle} .first-char span`, {
        x: isMobile ? "7.5rem" : "14rem",
        fontWeight: "900",
      });

      // Set initial position for special char - no scale, only horizontal movement
      gsap.set(`.${styles.splitOverlay} .${styles.introTitle} .special-char span`, {
        x: isMobile ? "-23.5rem" : "-16.75rem",
        fontWeight: "900",
      });

      // Get each character from outro-title and set different positions
      const outroChars = document.querySelectorAll(`.${styles.splitOverlay} .${styles.outroTitle} .char`);
      
      // Set custom positions for each BLK character
      if (outroChars.length === 3) {
        // B character
        gsap.set(outroChars[0], {
          x: isMobile ? "-5rem" : "-12rem",
        });
        
        // L character - moved 1rem more to the left
        gsap.set(outroChars[1], {
          x: isMobile ? "-6rem" : "-12.25rem", // Changed from -3rem/-8rem to -4rem/-9rem
        });
        
        // K character
        gsap.set(outroChars[2], {
          x: isMobile ? "-1rem" : "-6.75rem",
        });
      }

      // Create animation timeline with a slight delay to ensure everything is ready
      const tl = gsap.timeline({ 
        defaults: { ease: "hop" },
        delay: 0.1 
      });
      
      const tags = gsap.utils.toArray(`.${styles.preloaderTag}`);

      // Animate tags
      tags.forEach((tag, index) => {
        tl.to(
          tag.querySelectorAll("p .word"),
          {
            y: "0%",
            duration: 0.75,
          },
          0.5 + index * 0.1
        );
      });

      // Main animation sequence
      tl.to(
        `.${styles.preloader} .${styles.introTitle} .char span`,
        {
          y: "0%",
          duration: 0.75,
          stagger: 0.05,
        },
        0.5
      )
      .to(
        `.${styles.preloader} .${styles.introTitle} .char:not(.first-char):not(.special-char) span`, 
        {
          y: "100%",
          duration: 0.75,
          stagger: 0.05,
        },
        2
      );

      // Animate each BLK character separately
      const outroPreloaderChars = document.querySelectorAll(`.${styles.preloader} .${styles.outroTitle} .char span`);
      if (outroPreloaderChars.length === 3) {
        tl.to(
          outroPreloaderChars[0], // B
          {
            y: "0%",
            duration: 0.75,
          }, 
          2.5
        )
        .to(
          outroPreloaderChars[1], // L
          {
            y: "0%",
            duration: 0.75,
          }, 
          2.6
        )
        .to(
          outroPreloaderChars[2], // K
          {
            y: "0%",
            duration: 0.75,
          }, 
          2.7
        );
      } else {
        // Fallback if character count is different
        tl.to(
          `.${styles.preloader} .${styles.outroTitle} .char span`, 
          {
            y: "0%",
            duration: 0.75,
            stagger: 0.075,
          }, 
          2.5
        );
      }
      
      // Animate first char - horizontal movement only
      tl.to(
        `.${styles.preloader} .${styles.introTitle} .first-char`,
        {
          x: isMobile ? "9rem" : "14rem",
          duration: 1,
        },
        3.5
      )
      // Animate special char - horizontal movement only
      .to(
        `.${styles.preloader} .${styles.introTitle} .special-char`,
        {
          x: isMobile ? "7rem" : "-16.75rem",
          duration: 1,
        },
        3.5
      );

      // Animate each BLK character individually
      const outroAnimationChars = document.querySelectorAll(`.${styles.preloader} .${styles.outroTitle} .char`);
      if (outroAnimationChars.length === 3) {
        // B character
        tl.to(
          outroAnimationChars[0],
          {
            x: isMobile ? "-5rem" : "-12rem",
            duration: 1,
          },
          3.5
        );
        
        // L character - moved 1rem more to the left
        tl.to(
          outroAnimationChars[1],
          {
            x: isMobile ? "-5rem" : "-12.25rem", // Changed from -3rem/-8rem to -4rem/-9rem
            duration: 1,
          },
          3.6
        );
        
        // K character
        tl.to(
          outroAnimationChars[2],
          {
            x: isMobile ? "-1rem" : "-6.75rem",
            duration: 1,
          },
          3.7
        );
      } else {
        // Fallback
        tl.to(
          `.${styles.preloader} .${styles.outroTitle} .char`,
          {
            x: isMobile ? "-3rem" : "-8rem",
            duration: 1,
          },
          3.5
        );
      }
      
      // Final position for first char - horizontal movement only
      tl.to(
        `.${styles.preloader} .${styles.introTitle} .first-char`,
        {
          x: isMobile ? "5rem" : "14rem",
          fontWeight: "900",
          duration: 0.75
        },
        4.5
      )
      // Final position for special char - horizontal movement only
      .to(
        `.${styles.preloader} .${styles.introTitle} .special-char`,
        {
          x: isMobile ? "10rem" : "-16.75rem",
          fontWeight: "900",
          duration: 0.75
        },
        4.5
      );

      // Final position for each BLK character
      const finalOutroChars = document.querySelectorAll(`.${styles.preloader} .${styles.outroTitle} .char`);
      if (finalOutroChars.length === 3) {
        // B character
        tl.to(
          finalOutroChars[0],
          {
            x: isMobile ? "-5rem" : "-12rem",
            duration: 0.75,
            onComplete: () => {
              if (finalOutroChars[0] === finalOutroChars[finalOutroChars.length - 1]) {
                gsap.set(`.${styles.preloader}`, {
                  clipPath: "polygon(0 0, 100% 0, 100% 50%, 0 50%)",
                });
                gsap.set(`.${styles.splitOverlay}`, {
                  clipPath: "polygon(0 50%, 100% 50%, 100% 100%, 0 100%)",
                });
              }
            },
          },
          4.5
        );
        
        // L character - moved 1rem more to the left
        tl.to(
          finalOutroChars[1],
          {
            x: isMobile ? "-5rem" : "-12.25rem", // Changed from -3rem/-8rem to -4rem/-9rem
            duration: 0.75,
          },
          4.6
        );
        
        // K character
        tl.to(
          finalOutroChars[2],
          {
            x: isMobile ? "-1rem" : "-6.75rem",
            duration: 0.75,
            onComplete: () => {
              gsap.set(`.${styles.preloader}`, {
                clipPath: "polygon(0 0, 100% 0, 100% 50%, 0 50%)",
              });
              gsap.set(`.${styles.splitOverlay}`, {
                clipPath: "polygon(0 50%, 100% 50%, 100% 100%, 0 100%)",
              });
            },
          },
          4.7
        );
      } else {
        // Fallback
        tl.to(
          `.${styles.preloader} .${styles.outroTitle} .char`,
          {
            x: isMobile ? "-3rem" : "-8rem",
            duration: 0.75,
            onComplete: () => {
              gsap.set(`.${styles.preloader}`, {
                clipPath: "polygon(0 0, 100% 0, 100% 50%, 0 50%)",
              });
              gsap.set(`.${styles.splitOverlay}`, {
                clipPath: "polygon(0 50%, 100% 50%, 100% 100%, 0 100%)",
              });
            },
          },
          4.5
        );
      }
      
      // This is the key animation that reveals the app underneath
      // Now without any fade effect on the hero image
      tl.to(
        `.${styles.container}`, // Important: using the original selector here
        {
          clipPath: "polygon(0% 48%, 100% 48%, 100% 52%, 0% 52%)",
          duration: 1,
          // Removed the onUpdate callback that was fading the hero image
        },
        5
      );

      // Add a separate animation just for fading out the hero image AFTER container animation
      // This will start at position 6 (after the container animation completes)
      tl.to(
        `.${styles.heroImg}`,
        {
          opacity: 0,
          duration: 3, // Slow fade-out over 3 seconds
          ease: "power2.out" // Smooth ease-out for natural fading
        },
        6 // Start at position 6, after the container animation is complete
      );

      // Animate tags again
      tags.forEach((tag, index) => {
        tl.to(
          tag.querySelectorAll("p .word"),
          {
            y: "100%",
            duration: 0.75,
          },
          5.5 + index * 0.1
        );
      });

      // Final animation sequence
      tl.to(
        [`.${styles.preloader}`, `.${styles.splitOverlay}`],
        {
          y: (i) => (i === 0 ? "-50%" : "50%"),
          duration: 1,
        },
        6
      ).to(
        `.${styles.container}`, // Important: using the original selector here
        {
          clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
          duration: 1,
          onComplete: () => {
            // Fade out the entire preloader wrapper to reveal the app underneath
            gsap.to(`.${styles.preloaderWrapper}`, {
              autoAlpha: 0,
              duration: 0.5,
              onComplete: () => {
                // Re-enable scrolling before completing the preloader
                enableScroll();
                
                // Signal that the preloader animation is complete
                if (onComplete) {
                  onComplete();
                }
              }
            });
          }
        },
        6
      );
    };

    // Wait for fonts to load and DOM to be fully ready
    const cleanup = () => {
      // Kill any active GSAP animations when component unmounts
      gsap.killTweensOf(`.${styles.preloader}, .${styles.splitOverlay}, .${styles.container}, .${styles.introTitle}, .${styles.outroTitle}, .${styles.preloaderTag}, .${styles.heroImg}`);
      
      // Make sure scrolling is re-enabled when component unmounts
      enableScroll();
    };

    // Use requestIdleCallback or setTimeout to ensure DOM is fully ready
    const timeoutId = setTimeout(initializeAnimation, 100);

    // Add event listener for page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        // Reinitialize animation when page becomes visible after being hidden
        cleanup();
        setTimeout(initializeAnimation, 100);
      }
    });

    return () => {
      clearTimeout(timeoutId);
      cleanup();
      document.removeEventListener('visibilitychange', cleanup);
    };
  }, [onComplete]);

  return (
    <div className={styles.preloaderWrapper}>
      <div className={styles.preloader} ref={preloaderRef}>
        <div className={styles.introTitle} ref={introTitleRef}>
          <h1>Creative Studio</h1>
        </div>
        <div className={styles.outroTitle} ref={outroTitleRef}>
          <h1>BLK</h1>
        </div>
      </div>
      <div className={styles.splitOverlay} ref={splitOverlayRef}>
        <div className={styles.introTitle}>
          <h1>Creative Studio</h1>
        </div>
        <div className={styles.outroTitle}>
          <h1>BLK</h1>
        </div>
      </div>
      <div className={styles.tagsOverlay}>
        <div className={`${styles.preloaderTag} ${styles.tag1}`}><p>CREATIVE</p></div>
        <div className={`${styles.preloaderTag} ${styles.tag2}`}><p>AI</p></div>
        <div className={`${styles.preloaderTag} ${styles.tag3}`}><p>STUDIO</p></div>
      </div>

      {/* This is the element that creates the split effect and shows the app underneath */}
      <div className={styles.container} ref={containerRef}>
        <div className={styles.heroImg} ref={heroImgRef}>
          <img src="/homebackground.jpg" alt="" />
        </div>
      </div>

      {/* Adding a scroll blocker overlay that will catch all mouse/touch events */}
      <div 
        ref={scrollBlockerRef}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 99999,
          background: 'transparent',
          pointerEvents: 'all'
        }} 
        onClick={(e) => e.preventDefault()}
        onScroll={(e) => e.preventDefault()}
        onWheel={(e) => e.preventDefault()}
        onTouchMove={(e) => e.preventDefault()}
      />
    </div>
  );
};

export default Preloader;