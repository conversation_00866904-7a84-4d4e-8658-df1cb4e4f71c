// Enhanced smoke shader effect for project page transitions
(function() {
  function initSmokeShader() {
    const smokeCanvases = document.querySelectorAll('.smoke-canvas');
    
    if (!smokeCanvases.length) return;
    
    smokeCanvases.forEach(canvas => {
      const gl = canvas.getContext('webgl');
      if (!gl) {
        console.error('WebGL not supported');
        return;
      }
      
      // Set canvas dimensions
      function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        gl.viewport(0, 0, canvas.width, canvas.height);
      }
      
      resizeCanvas();
      window.addEventListener('resize', resizeCanvas);
      
      // Create shader program
      const vertexShaderSource = `
        attribute vec2 a_position;
        void main() {
          gl_Position = vec4(a_position, 0.0, 1.0);
        }
      `;
      
      const fragmentShaderSource = `
        precision highp float;
        uniform vec2 u_resolution;
        uniform float u_time;
        uniform float u_progress;
        
        float random(in vec2 _st) {
          return fract(sin(dot(_st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
        }
        
        float noise(in vec2 _st) {
          vec2 i = floor(_st);
          vec2 f = fract(_st);
          
          // Four corners in 2D of a tile
          float a = random(i);
          float b = random(i + vec2(1.0, 0.0));
          float c = random(i + vec2(0.0, 1.0));
          float d = random(i + vec2(1.0, 1.0));
          
          vec2 u = f * f * (3.0 - 2.0 * f);
          
          return mix(a, b, u.x) +
                  (c - a) * u.y * (1.0 - u.x) +
                  (d - b) * u.x * u.y;
        }
        
        #define NUM_OCTAVES 5
        
        float fbm(in vec2 _st) {
          float v = 0.0;
          float a = 0.5;
          vec2 shift = vec2(100.0);
          // Rotate to reduce axial bias
          mat2 rot = mat2(cos(0.5), sin(0.5),
                         -sin(0.5), cos(0.5));
          for (int i = 0; i < NUM_OCTAVES; ++i) {
            v += a * noise(_st);
            _st = rot * _st * 2.0 + shift;
            a *= 0.5;
          }
          return v;
        }
        
        void main() {
          vec2 st = gl_FragCoord.xy / u_resolution.xy * 3.0;
          st.x *= u_resolution.x / u_resolution.y;
          
          // Apply motion based on progress for right to left effect
          float moveAmount = 5.0; // Increase this for more horizontal movement
          float direction = 1.0; // 1.0 for right to left, -1.0 for left to right
          
          // Apply right to left movement
          st.x += direction * moveAmount * (1.0 - u_progress);
          
          vec3 color = vec3(0.0);
          
          // Create more complex movement
          vec2 q = vec2(0.0);
          q.x = fbm(st + 0.00 * u_time);
          q.y = fbm(st + vec2(1.0));
          
          vec2 r = vec2(0.0);
          r.x = fbm(st + 1.0 * q + vec2(1.7, 9.2) + 0.15 * u_time);
          r.y = fbm(st + 1.0 * q + vec2(8.3, 2.8) + 0.126 * u_time);
          
          float f = fbm(st + r);
          
          // Custom smoke color gradient - darker for project transitions
          color = mix(vec3(0.101961, 0.101961, 0.101961), 
                     vec3(0.266667, 0.266667, 0.298039), 
                     clamp((f * f) * 4.0, 0.0, 1.0));
          
          color = mix(color, 
                     vec3(0.0, 0.0, 0.164706), 
                     clamp(length(q), 0.0, 1.0));
          
          color = mix(color,
                     vec3(0.166667, 0.1, 0.2),
                     clamp(length(r.x), 0.0, 1.0));
          
          // Create a transition edge effect
          float edgeWidth = 0.2; // Width of the edge transition
          float edge = smoothstep(u_progress - edgeWidth, u_progress, st.x / 3.0);
          
          // Apply right to left wipe with smoke effect
          float alpha = edge * smoothstep(0.0, 0.5, u_progress);
          
          gl_FragColor = vec4((f * f * f + 0.6 * f * f + 0.5 * f) * color, alpha);
        }
      `;
      
      // Compile shaders
      function createShader(gl, type, source) {
        const shader = gl.createShader(type);
        gl.shaderSource(shader, source);
        gl.compileShader(shader);
        
        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
          console.error('Shader compilation error:', gl.getShaderInfoLog(shader));
          gl.deleteShader(shader);
          return null;
        }
        
        return shader;
      }
      
      const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
      const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
      
      // Create program
      const program = gl.createProgram();
      gl.attachShader(program, vertexShader);
      gl.attachShader(program, fragmentShader);
      gl.linkProgram(program);
      
      if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        console.error('Program linking error:', gl.getProgramInfoLog(program));
        return;
      }
      
      // Set up buffers
      const positionBuffer = gl.createBuffer();
      gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
      
      // Create a square that covers the entire canvas
      const positions = [
        -1.0, -1.0,
         1.0, -1.0,
        -1.0,  1.0,
         1.0,  1.0
      ];
      
      gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(positions), gl.STATIC_DRAW);
      
      // Get attribute and uniform locations
      const positionAttributeLocation = gl.getAttribLocation(program, 'a_position');
      const resolutionUniformLocation = gl.getUniformLocation(program, 'u_resolution');
      const timeUniformLocation = gl.getUniformLocation(program, 'u_time');
      const progressUniformLocation = gl.getUniformLocation(program, 'u_progress');
      
      gl.enableVertexAttribArray(positionAttributeLocation);
      gl.vertexAttribPointer(positionAttributeLocation, 2, gl.FLOAT, false, 0, 0);
      
      // Enable blending
      gl.enable(gl.BLEND);
      gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
      
      // Animation variables
      let startTime = performance.now();
      let lastProgress = 0;
      
      function render() {
        // Get current progress from data attribute
        const progress = parseFloat(canvas.getAttribute('data-progress') || '0');
        
        // Only update when progress changes
        if (progress !== lastProgress) {
          // Clear
          gl.clearColor(0, 0, 0, 0);
          gl.clear(gl.COLOR_BUFFER_BIT);
          
          // Use program
          gl.useProgram(program);
          
          // Set uniforms
          gl.uniform2f(resolutionUniformLocation, canvas.width, canvas.height);
          gl.uniform1f(timeUniformLocation, (performance.now() - startTime) / 1000);
          gl.uniform1f(progressUniformLocation, progress);
          
          // Draw
          gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
          
          lastProgress = progress;
        }
        
        requestAnimationFrame(render);
      }
      
      render();
    });
  }
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initSmokeShader);
  } else {
    initSmokeShader();
  }
})();