// Performance optimization configuration for the BLCKS web application

export const PERFORMANCE_CONFIG = {
  // Three.js renderer settings
  renderer: {
    antialias: false, // Disabled for better performance
    powerPreference: 'high-performance',
    stencil: false,
    depth: true,
    logarithmicDepthBuffer: false,
    maxPixelRatio: 2, // Limit pixel ratio
  },

  // Material optimization settings
  materials: {
    transmissionSamples: 2, // Reduced from 4 for better performance
    enableChromaticAberration: false, // Disable on lower-end devices
    enableIridescence: true,
    maxMaterialInstances: 4,
  },

  // Animation settings
  animation: {
    mouseThrottleMs: 16, // ~60fps
    lerpFactor: 0.035,
    enableFloatingAnimation: true,
    enableOrbitEffect: true,
  },

  // Rendering optimizations
  rendering: {
    enableFrustumCulling: true,
    enableOcclusion: false, // Disable for simpler scenes
    shadowMapSize: 1024, // Reduced shadow map size
    enableShadows: false, // Disable shadows for better performance
  },

  // Background effects
  background: {
    enableWaterEffect: true,
    disableWhenOverlayOpen: true,
    flowSimSize: 512, // Reduced from default
    raymarchSteps: 32, // Reduced raymarching steps
  },

  // Performance monitoring
  monitoring: {
    enableFPSCounter: false, // Set to true for debugging
    targetFPS: 60,
    minFPS: 30,
    enableAutoQualityAdjustment: true,
  },

  // Device-specific optimizations
  deviceOptimizations: {
    mobile: {
      transmissionSamples: 1,
      disableWaterEffect: true,
      reducedAnimations: true,
      maxPixelRatio: 1,
    },
    lowEnd: {
      transmissionSamples: 1,
      disableOrbitEffect: true,
      disableWaterEffect: true,
      enableStaticBackground: true,
    },
  },
};

// Device detection utilities
export const detectDeviceCapabilities = () => {
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
  
  if (!gl) {
    return 'unsupported';
  }

  const renderer = gl.getParameter(gl.RENDERER);
  const vendor = gl.getParameter(gl.VENDOR);
  
  // Simple device capability detection
  const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  const isLowEnd = isMobile || renderer.includes('Intel') || renderer.includes('Mali');
  
  return {
    isMobile,
    isLowEnd,
    supportsWebGL2: !!document.createElement('canvas').getContext('webgl2'),
    maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
    renderer,
    vendor,
  };
};

// Performance optimization helper
export const getOptimizedConfig = () => {
  const capabilities = detectDeviceCapabilities();
  
  if (capabilities === 'unsupported') {
    return { ...PERFORMANCE_CONFIG, fallbackMode: true };
  }

  let config = { ...PERFORMANCE_CONFIG };

  if (capabilities.isMobile) {
    config = {
      ...config,
      ...config.deviceOptimizations.mobile,
    };
  }

  if (capabilities.isLowEnd) {
    config = {
      ...config,
      ...config.deviceOptimizations.lowEnd,
    };
  }

  return config;
};

// FPS monitoring utility
export class PerformanceMonitor {
  constructor(targetFPS = 60) {
    this.targetFPS = targetFPS;
    this.frameCount = 0;
    this.lastTime = performance.now();
    this.fps = 0;
    this.isMonitoring = false;
  }

  start() {
    this.isMonitoring = true;
    this.monitor();
  }

  stop() {
    this.isMonitoring = false;
  }

  monitor() {
    if (!this.isMonitoring) return;

    const now = performance.now();
    this.frameCount++;

    if (now - this.lastTime >= 1000) {
      this.fps = Math.round((this.frameCount * 1000) / (now - this.lastTime));
      this.frameCount = 0;
      this.lastTime = now;
      
      // Trigger performance adjustment if needed
      if (this.fps < this.targetFPS * 0.8) {
        this.onPerformanceDrop?.(this.fps);
      }
    }

    requestAnimationFrame(() => this.monitor());
  }

  getFPS() {
    return this.fps;
  }

  onPerformanceDrop(callback) {
    this.onPerformanceDrop = callback;
  }
}
