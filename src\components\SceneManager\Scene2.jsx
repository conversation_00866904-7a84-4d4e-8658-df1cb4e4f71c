import React, { useRef, useMemo, useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useThree, useLoader } from '@react-three/fiber';
import * as THREE from 'three';
import { useControls } from 'leva';

import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader';
import { EffectComposer, Bloom, DepthOfField, ToneMapping } from '@react-three/postprocessing';

// Optimized RandomParticles Component
function RandomParticles({ count = 50, color = '#363636', minSize = 0.05, maxSize = 0.2, bounds = 15 }) {
  const particleRefs = useRef([]);

  // Generate random particles with different sizes and constrain to screen area
  const particles = useMemo(() => {
    const temp = [];
    // Screen-constrained bounds (approximate viewport dimensions)
    const screenWidth = 8;  // Adjust based on your camera FOV and distance
    const screenHeight = 6; // Adjust based on your camera FOV and distance
    const screenDepth = 2;  // Small depth range to keep particles in screen area

    for (let i = 0; i < count; i++) {
      const size = Math.random() * (maxSize - minSize) + minSize;
      const position = [
        (Math.random() - 0.5) * screenWidth,  // x constrained to screen width
        (Math.random() - 0.5) * screenHeight, // y constrained to screen height
        (Math.random() - 0.5) * screenDepth   // z constrained to small depth
      ];
      const hoverOffset = Math.random() * 0.10 + 0.05; // Random hover amplitude
      const hoverSpeed = Math.random() * 0.10 + 0.05;  // Random hover speed
      temp.push({ position, size, hoverOffset, hoverSpeed, originalY: position[1] });
    }
    return temp;
  }, [count, minSize, maxSize, bounds]);

  // Hovering animation for particles
  useFrame(({ clock }) => {
    particles.forEach((particle, i) => {
      if (particleRefs.current[i]) {
        // Hovering effect - gentle up and down movement
        const hoverY = particle.originalY + Math.sin(clock.elapsedTime * particle.hoverSpeed + i * 0.1) * particle.hoverOffset;
        particleRefs.current[i].position.y = hoverY;

        // Optional: Very subtle horizontal drift
        const driftX = particle.position[0] + Math.sin(clock.elapsedTime * 0.2 + i * 0.05) * 0.1;
        particleRefs.current[i].position.x = driftX;
      }
    });
  });

  return (
    <group>
      {particles.map((particle, i) => (
        <mesh
          key={i}
          ref={el => particleRefs.current[i] = el}
          position={particle.position}
        >
          <sphereGeometry args={[particle.size, 8, 8]} />
          <meshBasicMaterial color={color} transparent opacity={0.7} />
        </mesh>
      ))}
    </group>
  );
}

// BlackBackground component (from HeroSectionV2)
function BlackBackground() {
  const { scene } = useThree();
  React.useEffect(() => {
    scene.background = new THREE.Color('#202020');
    return () => {
      scene.background = null;
    };
  }, [scene]);
  return null;
}

// Underwater Shader Component
function UnderwaterShader() {
  const meshRef = useRef();
  const { size, camera } = useThree();

  // Calculate responsive plane size based on camera and viewport
  const planeSize = useMemo(() => {
    const distance = 5; // Distance from camera (z: -5)
    const fov = camera.fov * Math.PI / 180; // Convert to radians
    const height = 2 * Math.tan(fov / 2) * distance;
    const width = height * (size.width / size.height);

    // Ensure minimum width of 25 to avoid stretching
    const finalWidth = Math.max(width, 25);
    const finalHeight = height;

    return { width: finalWidth, height: finalHeight };
  }, [size, camera]);

  // Shader material with converted Shadertoy code and cleanup
  const shaderMaterial = useMemo(() => {
    const material = new THREE.ShaderMaterial({
      uniforms: {
        iTime: { value: 0 },
        iResolution: { value: new THREE.Vector3(size.width, size.height, 1) }
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float iTime;
        uniform vec3 iResolution;
        varying vec2 vUv;

        float hash(vec2 p) {
          return 0.5*(
            sin(dot(p, vec2(271.319, 413.975)) + 1217.13*p.x*p.y)
          ) + 0.5;
        }

        float noise(vec2 p) {
          vec2 w = fract(p);
          w = w * w * (3.0 - 2.0*w);
          p = floor(p);
          return mix(
            mix(hash(p+vec2(0,0)), hash(p+vec2(1,0)), w.x),
            mix(hash(p+vec2(0,1)), hash(p+vec2(1,1)), w.x), w.y);
        }

        float map_octave(vec2 uv) {
          uv = (uv + noise(uv)) / 2.5;
          uv = vec2(uv.x*0.6-uv.y*0.8, uv.x*0.8+uv.y*0.6);
          vec2 uvsin = 1.0 - abs(sin(uv));
          vec2 uvcos = abs(cos(uv));
          uv = mix(uvsin, uvcos, uvsin);
          float val = 1.0 - pow(uv.x * uv.y, 0.65);
          return val;
        }

        float map(vec3 p) {
          // Much slower wave movement
          vec2 uv = p.xz + iTime/8.;
          float amp = 0.6, freq = 2.0, val = 0.0;
          for(int i = 0; i < 3; ++i) {
            val += map_octave(uv) * amp;
            amp *= 0.3;
            uv *= freq;
          }
          uv = p.xz - 1000. - iTime/8.;
          amp = 0.6, freq = 2.0;
          for(int i = 0; i < 3; ++i) {
            val += map_octave(uv) * amp;
            amp *= 0.3;
            uv *= freq;
          }
          return val + 3.0 - p.y;
        }

        vec3 getNormal(vec3 p) {
          float eps = 1./iResolution.x;
          vec3 px = p + vec3(eps, 0, 0);
          vec3 pz = p + vec3(0, 0, eps);
          return normalize(vec3(map(px),eps,map(pz)));
        }

        float raymarch(vec3 ro, vec3 rd, out vec3 outP, out float outT) {
          float l = 0., r = 26.;
          int i = 0, steps = 16;
          float dist = 1000000.;
          for(i = 0; i < steps; ++i) {
            float mid = (r+l)/2.;
            float mapmid = map(ro + rd*mid);
            dist = min(dist, abs(mapmid));
            if(mapmid > 0.) {
              l = mid;
            }
            else {
              r = mid;
            }
            if(r - l < 1./iResolution.x) break;
          }
          outP = ro + rd*l;
          outT = l;
          return dist;
        }

        // Optimized FBM with fewer iterations for better performance
        float fbm(vec2 n) {
          float total = 0.0, amplitude = 1.0;
          for (int i = 0; i < 5; i++) { // Reduced from 5 to 3 iterations
            total += noise(n) * amplitude;
            n += n;
            amplitude *= 0.4; // Slightly adjusted for visual compensation
          }
          return total;
        }

        // Optimized light shafts with reduced calculations
        float lightShafts(vec2 st) {
          float angle = -0.2;
          vec2 _st = st;
          float t = iTime / 20.; // Slower movement for better performance

          // Pre-calculate rotation values
          float cosA = cos(angle);
          float sinA = sin(angle);
          st = vec2(st.x * cosA - st.y * sinA, st.x * sinA + st.y * cosA);

          // Single FBM call instead of two for better performance
          float val = fbm(vec2(st.x*1.5 + 200. + t, st.y/4.)) * 0.7;

          // Simplified mask calculation
          float mask = clamp(1.0 - abs(_st.y-0.15), 0., 1.) * 0.49 + 0.5;
          mask *= clamp(1.0 - abs(_st.x+0.2), 0., 1.) * 0.49 + 0.5;
          return val * mask * mask; // Simplified power calculation
        }

        // Original bubble function with proper noise and visual effects
        vec2 bubble(vec2 uv, float scale) {
          if(uv.y > 0.2) return vec2(0.);
          float t = iTime/6.; // Original speed for natural movement
          vec2 st = uv * scale;
          vec2 _st = floor(st);

          // Original random bubble generation with proper noise
          float cellRandom = hash(_st);
          if(cellRandom < 0.6) return vec2(0.); // 60% chance of bubble (more bubbles)

          // Original complex bias calculation for natural movement
          vec2 bias = vec2(0., 4. * sin(_st.x*128. + t) * cos(_st.y*64. + t*0.7));
          float mask = smoothstep(0.05, 0.15, -cos(_st.x*128. + t)) *
                      smoothstep(0.1, 0.4, sin(_st.y*64. + t*0.5));
          st += bias;
          st = fract(st);

          // Variable bubble size with noise for realism
          float sizeNoise = hash(_st + vec2(123.45, 678.90));
          float size = 0.005 + 0.02 * sizeNoise; // Variable size 0.01 to 0.04

          // Complex bubble position with multiple sine waves
          vec2 pos = vec2(
            0.5 + 0.4 * sin(t + _st.y*64. + _st.x*32.),
            0.3 + 0.2 * cos(t*0.8 + _st.x*48.)
          );

          if(length(st.xy - pos) < size) {
            // Original stronger effect with proper distortion
            return (st + pos) * vec2(.12, .15) * mask * (1.0 + sizeNoise);
          }
          return vec2(0.);
        }

        void main() {
          vec3 ro = vec3(0.,0.,2.);
          vec3 lightPos = vec3(8, 3, -3);
          vec3 lightDir = normalize(lightPos - ro);

          // Convert vUv to fragCoord-like coordinates
          vec2 fragCoord = vUv * iResolution.xy;

          // adjust uv
          vec2 uv = fragCoord;
          uv = (-iResolution.xy + 2.0*uv) / iResolution.y;
          uv.y *= 0.5;
          uv.x *= 0.45;
          uv += bubble(uv, 6.) + bubble(uv, 12.) + bubble(uv, 24.); // Original multiple bubble layers

          vec3 rd = normalize(vec3(uv, -1.));
          vec3 hitPos;
          float hitT;
          vec3 seaColor = vec3(0,0,0);
          vec3 color;

          // waves - COMMENTED OUT
          // float dist = raymarch(ro, rd, hitPos, hitT);
          // float diffuse = dot(getNormal(hitPos), rd) * 0.5 + 0.5;
          // color = mix(seaColor, vec3(0,0,0), diffuse);
          // color += pow(diffuse, 12.0) * 0.9;

          // refraction - COMMENTED OUT
          // vec3 ref = normalize(refract(hitPos-lightPos, getNormal(hitPos), 0.05));
          // float refraction = clamp(dot(ref, rd), 0., 1.0);
          // color += vec3(0.5,0.5,0.5) * 0.6 * pow(refraction, 1.5);

          // Set base color without waves
          color = seaColor;

          vec3 col = vec3(0.);
          // col = mix(color, seaColor, pow(clamp(0., 1., dist), 0.2)); // glow edge - COMMENTED OUT
          col = color; // Use base color without wave effects
          col += vec3(1,1,1) * lightShafts(uv); // light shafts

          // tone map
          col = (col*col + sin(col))/vec3(2, 2, 2);

          // vignette
          vec2 q = fragCoord / iResolution.xy;
          col *= 0.7+0.3*pow(16.0*q.x*q.y*(1.0-q.x)*(1.0-q.y),0.2);

          gl_FragColor = vec4(col,1.0);
        }
      `
    });
    return material;
  }, [size]);

  // Cleanup shader material on unmount
  useEffect(() => {
    return () => {
      if (shaderMaterial && shaderMaterial.dispose) {
        shaderMaterial.dispose();
      }
    };
  }, [shaderMaterial]);

  // Update time uniform
  useFrame(({ clock }) => {
    if (meshRef.current) {
      meshRef.current.material.uniforms.iTime.value = clock.elapsedTime;
    }
  });

  // Update resolution when size changes
  React.useEffect(() => {
    if (meshRef.current) {
      meshRef.current.material.uniforms.iResolution.value.set(size.width, size.height, 1);
    }
  }, [size]);

  return (
    <mesh ref={meshRef} position={[0, 2.5, -5]} rotation={[-0.12, 0, 0]} renderOrder={-1}>
      <planeGeometry args={[30, 30/16*9]} />
      <primitive object={shaderMaterial} attach="material" />
    </mesh>
  );
}

// Enhanced Post-Processing Component with Bloom, DepthOfField, and ToneMapping
function EnhancedPostProcessing() {
  return (
    <EffectComposer disableNormalPass>
      <Bloom
        luminanceThreshold={0}
        mipmapBlur
        luminanceSmoothing={0.0}
        intensity={5}
      />
      <DepthOfField
        target={[0, 0, 13]}
        focalLength={0.3}
        bokehScale={15}
        height={700}
      />
    </EffectComposer>
  );
}

// Individual Cube Component
function ScrollCube({ index, scrollProgress, verticalOffset, cubeData, isActive }) {
  const meshRef = useRef();
  const groupRef = useRef();
  const { gl } = useThree();

  // Load DRACO model based on index
  const geometry = useLoader(
    DRACOLoader,
    cubeData.modelPath,
    loader => {
      loader.setDecoderPath('https://www.gstatic.com/draco/v1/decoders/');
    }
  );

  // Load KTX2 textures for this specific block (same setup as whatSection.jsx)
  const normalMap = useLoader(KTX2Loader, cubeData.normalMapPath, loader => {
    loader.setTranscoderPath('https://unpkg.com/three@0.152.2/examples/jsm/libs/basis/');
    loader.detectSupport(gl);
  });

  const roughnessMap = useLoader(KTX2Loader, cubeData.roughnessMapPath, loader => {
    loader.setTranscoderPath('https://unpkg.com/three@0.152.2/examples/jsm/libs/basis/');
    loader.detectSupport(gl);
  });

  // Calculate this cube's scroll position and centered progress
  const scrollPosition = (1 + index) * verticalOffset;
  const centeredProgress = scrollPosition / ((4 + 1) * verticalOffset); // 4 cubes total

  useFrame(({ clock }) => {
    if (!groupRef.current || !meshRef.current) return;

    // COMPLETELY DISABLE all animation when scene is not active
    if (!isActive) {
      // Freeze cubes in default position when scene is not active
      groupRef.current.position.y = scrollPosition;
      groupRef.current.rotation.x = 0;
      groupRef.current.rotation.y = 0;
      groupRef.current.rotation.z = 0;
      return;
    }

    // Calculate relative position to current scroll
    const relativeProgress = centeredProgress - scrollProgress;
    const scrollDistance = scrollPosition - (scrollProgress * (4 + 1) * verticalOffset);

    // Position the group based on scroll
    groupRef.current.position.y = scrollDistance;

    // Rotation logic based on original code
    const randomSeed = (index + 0.5) * 242.45353 % 1;
    const rotationDirection = randomSeed < 0.5 ? -1 : 1;

    const rotationMultiplierX = rotationDirection;
    const rotationMultiplierY = -rotationDirection;
    const rotationMultiplierZ = rotationDirection;

    // Random factors for each cube (based on index)
    const randX = ((index * 12.3423) % 1);
    const randY = ((index * 123.5343) % 1);
    const randZ = ((index * 54.654) % 1);

    const dampingX = randX * 0.1 + 0.1; // 0.1 to 0.2
    const dampingY = randY * 0.2 + 0.1; // 0.1 to 0.3
    const dampingZ = randZ * 0.15 + 0.1; // 0.1 to 0.25

    // Main rotation based on scroll position
    const rotationX = 11 * rotationMultiplierX * (1 - dampingX);
    const rotationY = 14 * rotationMultiplierY * (1 - dampingY);
    const rotationZ = 6 * rotationMultiplierZ * (1 - dampingZ);

    // Additional subtle animation
    const animationSpeed = 0.3;
    const animationAmount = 0.1;
    const animRandX = Math.sin(clock.elapsedTime * animationSpeed + index * 12.423) * animationAmount * Math.sign(index - 2);
    const animRandY = Math.sin(clock.elapsedTime * animationSpeed + index * 42.987) * animationAmount * Math.sign(index - 2);
    const animRandZ = Math.sin(clock.elapsedTime * animationSpeed + index * 2.53) * animationAmount * Math.sign(index - 2);

    // Apply final rotations
    groupRef.current.rotation.y = rotationY * relativeProgress + animRandY;
    groupRef.current.rotation.x = rotationX * relativeProgress + animRandX;
    groupRef.current.rotation.z = rotationZ * relativeProgress + animRandZ;
  });

  return (
    <group ref={groupRef}>
      <mesh ref={meshRef} position={[0, 0, 0]} geometry={geometry} scale={[0.5, 0.5, 0.5]} renderOrder={100}>
        <meshPhysicalMaterial
          color="#242424"
          roughnessMap={roughnessMap}
          normalMap={normalMap}
          normalScale={[1, 1]}
          metalness={0}
          roughness={1}
          transmission={0.9}
          transparent={true}
          opacity={0.75}
          clearcoat={1}
          clearcoatRoughness={0.1}
          ior={1.5}
          thickness={0.5}
          envMapIntensity={0.91}
          side={THREE.FrontSide}
          depthTest={true}     // Ensure depth testing
          depthWrite={true}    // Ensure depth writing
        />
      </mesh>
    </group>
  );
}


// Scene2 Component - 4 Scrolling Cubes System
export default function Scene2({ isActive = false, scrollProgress = 0, globalScrollProgress = 0 }) {
  // Camera position is handled by the transition system in ScrollTransitionController
  // No need to manually reset camera position here as it causes a flash

  // Controls for debugging
  const { showDebugInfo } = useControls('Scene2 Controls', {
    showDebugInfo: false
  });

  // Use the scroll progress directly (0 to 1) - already calculated in AppAI

  // Debug info display
  useEffect(() => {
    if (showDebugInfo) {
      console.log('Scene2 Debug:', {
        isActive,
        scrollProgress: scrollProgress.toFixed(3)
      });
    }
  }, [isActive, scrollProgress, showDebugInfo]);

  // Cube configuration (4 cubes with DRACO models and KTX2 textures)
  const cubes = useMemo(() => [
    {
      name: 'Cube 1',
      modelPath: '/models/block1.drc',
      normalMapPath: '/textures/block1_normal.ktx2',
      roughnessMapPath: '/textures/block1_roughness.ktx2'
    },
    {
      name: 'Cube 2',
      modelPath: '/models/block2.drc',
      normalMapPath: '/textures/block2_normal.ktx2',
      roughnessMapPath: '/textures/block2_roughness.ktx2'
    },
    {
      name: 'Cube 3',
      modelPath: '/models/block3.drc',
      normalMapPath: '/textures/block3_normal.ktx2',
      roughnessMapPath: '/textures/block3_roughness.ktx2'
    },
    {
      name: 'Cube 4',
      modelPath: '/models/block1.drc', // 4th cube uses block1.drc again
      normalMapPath: '/textures/block1_normal.ktx2', // Uses block1 textures
      roughnessMapPath: '/textures/block1_roughness.ktx2'
    }
  ], []);

  // Vertical offset between cubes (from original code)
  const verticalOffset = -5.75;

  return (
    <>
      {/* No background color - transparent so you can see debug helpers */}
      <BlackBackground />

      {/* Underwater Shader Background */}
      <UnderwaterShader />

      <group>
        {/* Enhanced Lighting */}
        <hemisphereLight intensity={0.15} groundColor="black" />
        <spotLight
          decay={0}
          position={[-5, 5, -2]}
          angle={0.12}
          penumbra={1}
          intensity={100}
          castShadow
          shadow-mapSize={1024}
          color={'white'}
        />
        <ambientLight intensity={1} />
        {/*<pointLight position={[5, 5, 5]} intensity={100} />*/}

        {/* Random Particles Background (behind cubes) */}
        {/*<group position={[0, 0, 0]}>
          <RandomParticles
            count={200}
            color="#818181"
            minSize={0.001}
            maxSize={0.01}
            bounds={20}
          />
        </group>*/}

        {/* 4 Scrolling Cubes (front layer - default renderOrder 0) */}
        {cubes.map((cubeData, index) => (
          <ScrollCube
            key={index}
            index={index}
            scrollProgress={scrollProgress}
            verticalOffset={verticalOffset}
            cubeData={cubeData}
            isActive={isActive}
          />
        ))}
      </group>

      {/* Enhanced Post-Processing Effects - DISABLED for SceneManager compatibility */}
      {/* <EnhancedPostProcessing /> */}
    </>
  );
}
