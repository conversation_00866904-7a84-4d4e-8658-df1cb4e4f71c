import React from 'react';
import { useInView } from 'react-intersection-observer';
import './SimpleFadeSection.css';

const SimpleFadeSection = ({ 
  children,
  className = ''
}) => {
  // Set up the intersection observer with threshold for gradual fade
  const { ref, inView } = useInView({
    threshold: 1, // Start fading when 20% out of view
    triggerOnce: false // Re-trigger when scrolling back
  });
  
  return (
    <div 
      ref={ref}
      className={`simple-fade-section ${className} ${inView ? 'visible' : 'hidden'}`}
    >
      {children}
    </div>
  );
};

export default SimpleFadeSection;