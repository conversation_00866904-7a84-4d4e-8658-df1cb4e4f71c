/* Import Satoshi font */
@import url('https://api.fontshare.com/v2/css?f[]=satoshi@900,700,500,301,300,400&display=swap');

/* Import local Telegraf fonts */
@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Archivo';
  src: url('/fonts/archivo-black-regular.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  font-family: 'Telegraf', sans-serif;
  color: black;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: transparent;
}

/* PROJECT PAGE STYLES */
.project-page-container-wrapper {
  width: 100%;
  height: 100vh; 
  position: relative;
  background-color: #e8e8e8;
  overflow-x: hidden;
}

.project-fluid-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
  pointer-events: none;
}

.project-fluid-canvas canvas {
  pointer-events: auto;
}

.project-content-container {
  position: relative;
  z-index: 5;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  
  /* Hide scrollbar but keep functionality */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.project-content-container::-webkit-scrollbar {
  display: none;
}

/* Project specific styles */
/* 1. Heading centered in viewport */
.project-hero-section {
  height: 75vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 30px;
  margin-bottom: 0px;
}

.project-title {
  font-family: 'Telegraf', sans-serif;
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1.3;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  color: black;
}

/* Make sure the hero image container properly handles both images and videos */
.project-hero-image {
  position: relative;
  width: 100%;
  max-width: 1600px;
  margin: 2rem auto; /* Center horizontally with auto margins */
  padding: 0 2rem; /* Add some padding on smaller screens */
  box-sizing: border-box;
}

/* Create a container with fixed aspect ratio for the media */
.project-hero-image::after {
  content: "";
  display: block;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.project-hero-image img,
.project-hero-image video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

/* 3. Grid of 2: left heading, right two paragraphs */
.project-info-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 400px;
  padding: 0 30px;
  max-width: 1200px;
  margin: 240px auto 60px;
  height: 75vh;
}

.project-info-heading {
  padding-right: 0px;
}

.project-info-heading h2 {
  font-family: 'Telegraf', sans-serif;
  font-size: 2.5rem;
  font-weight: 500;
  color: black;
  margin-top: 10px;
}

.project-info-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
  margin-top: 40px;
  margin-left: 0;
  margin-right: 0;
  max-width: 100%;
}

.project-info-content p {
  font-family: 'Telegraf', sans-serif;
  font-size: 1.25rem;
  line-height: 1.6;
  color: black;
  margin: 0;
}

/* Special style for QUBE project */
.qube-info-grid {
  grid-template-columns: 1fr 1fr !important; /* Override with equal columns */
  gap: 200px !important; /* Adjust gap as needed */
}

@media (max-width: 992px) {
  .qube-info-grid {
    grid-template-columns: 1fr !important; /* Stack on smaller screens */
    gap: 60px !important;
  }
}

@media (max-width: 768px) {
  .qube-info-grid {
    grid-template-columns: 1fr !important; /* Stack on smaller screens */
    gap: 40px !important;
  }
}

/* 4. Four big images below each other */
.project-gallery-images {
  padding: 0 30px;
  max-width: 1400px;
  margin: 0 auto 120px;
  display: flex;
  flex-direction: column;
  gap: 400px;
}

.project-gallery-image {
  width: 100%;
  position: relative;
}

.project-gallery-image img {
  width: 100%;
  aspect-ratio: 16 / 9;
  object-fit: cover;
  border-radius: 8px;
}

.gallery-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* If you want to maintain the same styling as your images */
.project-gallery-image video {
  width: 100%;
  display: block;
  margin-bottom: 2rem;
  border-radius: 8px;
}

/* Project Page Back Button */
.project-page-back-button {
  position: fixed;
  top: 20px;
  left: 30px;
  z-index: 1000;
  background: transparent;
  border: 0px solid #e8e8e8;
  color: black;
  font-family: 'Telegraf', sans-serif;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.75rem 1.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
}

.project-page-back-button:hover {
  background: transparent;
  border-color: transparent;
}

/* Next Project Section Styles - Updated with text-only design */
.next-project-section {
  position: relative;
  z-index: 4;
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 30px;
  margin-bottom: 0;
}

.next-project-button {
  background: transparent;
  border: none;  /* No borders */
  color: #000000;
  font-family: 'Telegraf', sans-serif;
  font-size: 2rem;  /* Increased font size for better visibility */
  font-weight: 500;
  padding: 8px 0;  /* Reduced padding */
  cursor: pointer;
  transition: color 0.3s ease;
  position: relative;
  overflow: visible;
}

/* Ensure no background color on hover */
.next-project-button:hover {
  background: transparent;
  color: #ffffff; /* Keep the text color unchanged */
}

/* Underline styling */
.next-project-button::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #ffffff;
  transition: transform 0.4s ease;
  transform-origin: left;
}

/* Underline disappears on hover */
.next-project-button:hover::after {
  transform: scaleX(0);
  transform-origin: right;
}

/* Project Footer Styles - UPDATED FOR CONTACT SECTION */
.project-footer-section {
  min-height: 100vh;
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
  background-color: #000 !important;
  margin-top: 0;
  z-index: 5;
}

/* Updated Footer/Contact Styles to Match Contact.module.css */
.project-footer-content {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 2rem;
  position: relative;
  z-index: 2;
}

/* Contact Email Container */
.contact-email-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.contact-email-heading {
  font-size: 4rem;
  font-weight: 500;
  color: #ffffff;
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.contact-email-link {
  text-decoration: none;
  color: inherit;
}

.contact-email-link:hover {
  text-decoration: underline;
  cursor: pointer;
}

.contact-email-heading:hover {
  transform: scale(1.05);
  opacity: 0.7;
}

.contact-email-heading::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #ffffff;
  transition: transform 0.4s ease;
  transform-origin: left;
  transform: scaleX(0);
}

.contact-email-heading:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Contact Bottom - updated to match Contact.module.css */
.contact-bottom {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-bottom: 0;
  position: relative;
}

/* Copyright and Built By positioning */
.contact-copyright {
  position: absolute;
  bottom: 0px;
  left: 0%;
  font-size: 0.7rem;
  color: white;
  opacity: 0.75;
  letter-spacing: 1px;
  font-family: 'Telegraf', sans-serif;
}

.contact-built-by {
  position: absolute;
  bottom: 0px;
  right: 0%;
  font-size: 0.7rem;
  color: white;
  opacity: 0.75;
  letter-spacing: 1px;
  font-family: 'Telegraf', sans-serif;
}

/* Footer section layout - Using flex column for mobile instead of grid */
.footer-section {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  width: 100%;
  max-width: 800px;
  position: absolute;
  bottom: 120px;
  margin: 0 auto;
  left: 0;
  right: 0;
}

/* Footer links container styling */
.footer-links-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  top: -20px;
}

/* Footer link text styling */
.footer-links {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.footer-link {
  color: #ffffff;
  text-decoration: none;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 1px;
  transition: opacity 0.3s ease;
}

.footer-link:hover {
  opacity: 0.7;
}

/* Social icons styling - positioned in the center grid */
.social-icons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}

.social-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: transparent;
  transition: opacity 0.3s ease;
}

.social-icon:hover {
  opacity: 0.7;
}

.social-icon img {
  width: 24px;
  height: 24px;
}

/* QR Code container styling */
.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  grid-column: 3;
}

.qr-code-image {
  width: 100px;
  height: 100px;
  margin-bottom: 0px;
}

.qr-code-heading {
  color: #ffffff;
  opacity: 0.7;
  font-size: 10px;
  font-weight: 400;
  text-align: center;
  margin-top: 5px;
}

/* Media queries for responsive design - Updated for mobile layouts */
@media (max-width: 768px) {
  .contact-email-heading {
    font-size: 2rem !important; 
    margin-top: -80px;
  }
  
  .footer-section {
    display: flex;
    flex-direction: column;
    gap: 25px;
    width: 90%;
    bottom: 180px;
    align-items: center; /* Center all footer items */
  }
  
  .footer-links-container {
    order: 1; /* Make this appear first */
    margin-bottom: 20px; /* Add space between links and social icons */
    width: 100%;
  }
  
  .social-icons {
    order: 2; /* Make this appear second */
    width: 100%;
    justify-content: center;
  }
  
  .qr-code-container {
    order: 3; /* Make this appear last */
    grid-column: 1;
    margin-top: 10px; /* Add space above QR code */
  }
  
  .contact-copyright,
  .contact-built-by {
    position: static;
    text-align: center;
    margin-top: 30px;
    margin-bottom: 10px;
  }
  
  .contact-bottom {
    margin-top: auto;
    padding-top: 10px;
    padding-bottom: 60px;
  }
  
  .footer-links {
    flex-direction: column;
    gap: 15px;
    justify-content: center;
    align-items: center;
    width: 100%; /* Full width */
    text-align: center; /* Center text */
  }
  
  .qr-code-image {
    width: 80px;
    height: 80px;
  }
}

/* Smaller mobile devices */
@media (max-width: 480px) {
  .contact-email-heading {
    font-size: 2rem !important;
    text-align: center;
    margin-top: -200px;
  }
  
  .footer-section {
    width: 95%;
    gap: 30px; /* Increased gap for more separation */
    bottom: 250px;
  }
  
  .contact-copyright,
  .contact-built-by {
    font-size: 0.6rem;
    margin-bottom: 50px;
    margin-top: 0px;
  }
  
  .contact-copyright + .contact-built-by {
    margin-top: 0px;
  }
  
  .footer-links {
    flex-direction: column;
    align-items: center;
    gap: 12px;
    width: 100%;
    text-align: center;
  }
  
  .qr-code-image {
    width: 70px;
    height: 70px;
  }
  
  .contact-bottom {
    padding-bottom: 80px;
  }
}

/* Seamless color transition styles */
.project-page-container-wrapper {
  transition: background-color 0.7s ease;
}

.project-title,
.project-info-grid,
.next-project-button,
.back-button,
.project-page-back-button {
  transition: color 0.7s ease;
}

/* Color transition overlay */
.color-transition-overlay {
  position: absolute;
  left: 0;
  right: 0;
  height: 100vh; /* Full viewport height */
  pointer-events: none;
  z-index: 0;
  opacity: 0;
  display: none;
}

/* When transitioning, activate the overlay */
.transitioning .color-transition-overlay {
  display: block;
  opacity: var(--scroll-progress);
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  background: linear-gradient(
    to bottom,
    rgba(232, 232, 232, 0) 0%,
    rgba(0, 0, 0, calc(var(--scroll-progress) * 0.8)) 20%,
    rgba(0, 0, 0, var(--scroll-progress)) 100%
  );
  pointer-events: none;
  z-index: 1;
}

/* Make sure project content stays above the overlay */
.project-content-container {
  position: relative;
  z-index: 2;
}

/* Next project section - no background color of its own */
.next-project-section {
  position: relative;
  z-index: 3;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Footer section styles with fluid effect */
.project-footer-section {
  position: relative;
  z-index: 3;
  height: 100vh;
  width: 100%;
  margin-top: -1px; /* Avoid any gap between sections */
}

/* Make all fluid effect elements interactive with the canvas */
.fluid-effect-element {
  position: relative;
  z-index: 2;
}

/* Media Queries for Responsive Layout */
@media (max-width: 1200px) {
  
  .project-info-grid {
    gap: 40px;
  }

  .project-hero-image {
    aspect-ratio: 16 / 9;
  }
  
  .contact-email-heading {
    font-size: 3.5rem;
  }
}

@media (max-width: 992px) {
  
  .project-hero-image,
  .project-info-grid,
  .project-gallery-images {
    margin-bottom: 80px;
  }

  .project-hero-image {
    aspect-ratio: 16 / 9;
  }
  
  .project-gallery-images {
    gap: 60px;
  }
  
  .project-info-heading h2 {
    font-size: 2rem;
  }
  
  .contact-email-heading {
    font-size: 3rem;
  }
  
  .next-project-button {
    font-size: 1.3rem;
  }
}

@media (max-width: 768px) {
  
  .project-info-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .project-hero-image {
    aspect-ratio: 16 / 9;
  }
  
  .project-hero-image,
  .project-info-grid,
  .project-gallery-images {
    margin-bottom: 60px;
  }
  
  .project-gallery-images {
    gap: 40px;
  }
  
  .project-info-heading h2 {
    font-size: 1.75rem;
  }
  
  .project-info-content p {
    font-size: 1.1rem;
  }
  
  .project-title {
    font-size: 0.75rem;
  }

  .project-hero-section {
    height: 50vh;
  }
}

@media (max-width: 480px) {
  
  .project-hero-section,
  .project-hero-image,
  .project-info-grid,
  .project-gallery-images {
    padding: 0 20px;
  }

  .project-hero-image {
    aspect-ratio: 16 / 9;
  }
  
  .project-hero-image,
  .project-info-grid,
  .project-gallery-images {
    margin-bottom: 40px;
  }
  
  .project-gallery-images {
    gap: 30px;
  }
  
  .project-info-heading h2 {
    font-size: 1.5rem;
  }
  
  .project-info-content p {
    font-size: 1rem;
  }
  
  .project-page-back-button {
    top: 20px;
    left: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* Hero video styling */
.hero-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 8px;
}

/* Ensure video controls remain visible when needed */
.hero-video[controls] {
  object-fit: contain;
  background-color: black;
  border-radius: 8px;
}

/* Responsive adjustments */
@media (max-width: 1700px) {
  .project-hero-image {
    max-width: 90%;
  }
}

@media (max-width: 768px) {
  .project-hero-image {
    padding: 0 1.5rem;
  }
}

/* Black overlay that fades in */
.black-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  opacity: 0;
  transition: opacity 0.6s ease;
  z-index: 2; /* Between fluid canvas and content */
  pointer-events: none;
}

/* New combined footer section style */
.combined-footer-section {
  width: 100%;
  height: 200vh; /* Combined height for next project + footer */
  background-color: #000;
  position: relative;
  z-index: 4; /* Above black overlay */
}

/* Add this to your ProjectPage.css file */
.project-content-wrapper {
  position: relative;
  width: 100%;
  /* The height will be set dynamically with JavaScript */
}