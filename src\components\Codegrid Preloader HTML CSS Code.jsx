<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>P10-Inspired Landing Page Reveal Animation</title>
    <link rel="stylesheet" href="/styles.css" />
  </head>
  <body>
    <div class="preloader">
        <div class="intro-title">
            <h1>Nullspace Studio</h1>
        </div>
        <div class="outro-title">
            <h1>10</h1>
        </div>
    </div>
    <div class="split-overlay">
        <div class="intro-title">
            <h1>Nullspace Studio</h1>
        </div>
        <div class="outro-title">
            <h1>10</h1>
        </div>
    </div>
    <div className="tags-overlay">
        <div class="tag tag-1"><p>Negative Space</p></div>
        <div class="tag tag-2"><p>Form & Void</p></div>
        <div class="tag tag-3"><p>Light Studies</p></div>
    </div>

    <div class="container">
        <nav>
            <p id="logo">N10</p>
            <p>Menu</p>
        </nav>

        <div className="hero-img"><img src="/hero-img.jpg" alt="" /></div>

        <div class="card">
            <h1>Nullspace</h1>
        </div>

        <footer>
            <p>Scroll Down</p>
            <p>Made by Codegrid</p>
        </footer>
    </div>
    <script type="module" src="/script.js"></script>
  </body>
</html>





* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}


body {
    font-family: "DM Sans", sans-serif;
}

img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

h1 {
    text-transform:uppercase;
    font-size: 6rem;
    font-weight: 600;
    line-height: 1;
}

p {
    text-transform: uppercase;
    font-size: 13px;
    font-weight: 500;
}

.preloader, 
.split-overalay, 
.tags-overlay {
    position: fixed;
    width: 100vw;
    height: 100ShadowMapViewer;
}

.preloader,
.split-overlay {
    background-color: #0a0a0a;
    color: #fff
}

.preloader,
.tags-overlay {
    z-index: 2;
}

.split-overlay {
    z-index: 1;
}

.intro-title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;
}

.outro-title {
    position: absolute;
    top: 50%;
    left: calc(50% + 10rem);
    transform: translate(-50%, -50%)
}

.tag {
    position: absolute;
    width: max-content;
    color: #5a5a5a;
    overflow: hidden;
}

.tag-1 {
    top: 15%;
    left: 15%;
}

.tag-2 {
    bottom: 15%;
    left: 25%;
}

.tag-3 {
    bottom: 30%;
    right: 15%;
}

.container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 100svh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    clip-path: polygon( 0 48%, 0 48%, 0 52%, 0 52%);
    z-index: 2;
}

.container -hero-img {
    position: absolute;
    width: 100%;
    height: 100%;
}

nav, 
footer {
    position: relative;
    width: 100vw;
    padding: 2em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;
    z-index: 2;
}

nav p#logo {
    font-weight: 600;
    font-size: 20px;
}

.card {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30%;
    height: 70%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    clip-path: polygon(0% 50%, 100% 50%, 100% 50%, 0% 50%,);
}

.card h1 {
    text-align: center;
    width: 100%;
    font-size: 3rem;
}

.card .char {
    position: relative;
    display: inline-block;
    transform: translateY(100%);
    will-change: transform;
}

.intro-title .char,
.outro-title .char,
.card .char {
    position: relative;
    display: inline-block;
    overflow: hidden;
}

.intro-title .char, 
.outro-title .char {
    margin-top: 0.75rem;
}

.intro-title .char span,
.outro-title .char span,
.tag .word {
    position: relative;
    display: inline-block;
    transform: translateY(-100%);
    will-change: transform;
}

.intro-title .first-char {
    transform-origin: top left;
}




