import { useEffect, useRef } from 'react'
import { useThree } from '@react-three/fiber'
import * as THREE from 'three'

// Import Reflector and ReflectorMaterial from alien.js
import { 
  Reflector,
  ReflectorMaterial
} from '@alienkitty/alien.js/three'

export default function ReflectiveFloor({ 
  position = [0, -1.6, 0], 
  rotation = [-Math.PI / 2, 0, 0],
  size = [100, 100],
  reflectivity = 0.3,
  mirror = 1,
  mixStrength = 4,
  textureBasePath = '/textures/',
  baseColorTexture = 'concrete_wet_floor_basecolor.webp',
  normalMapTexture = 'concrete_wet_floor_normal.webp',
  normalScale = 5
}) {
  const { scene, gl } = useThree()
  const reflectorRef = useRef()
  
  useEffect(() => {
    // Create reflector from alien.js
    const reflector = new Reflector()
    const width = Math.round(gl.domElement.width / 2)
    const height = 1024
    reflector.setSize(width, height)
    
    // Load textures with fallback
    const textureLoader = new THREE.TextureLoader()
    textureLoader.setPath(textureBasePath)
    
    // Try to load textures, but provide fallbacks if they fail
    const baseColorPromise = textureLoader.loadAsync(baseColorTexture)
      .catch(() => {
        console.warn('Base texture not found, using default color')
        return null
      })
    
    const normalMapPromise = textureLoader.loadAsync(normalMapTexture)
      .catch(() => {
        console.warn('Normal map not found, using default')
        return null
      })
    
    Promise.all([baseColorPromise, normalMapPromise]).then(([map, normalMap]) => {
      // Apply texture settings if loaded successfully
      if (map) {
        map.wrapS = THREE.RepeatWrapping
        map.wrapT = THREE.RepeatWrapping
      }
      
      if (normalMap) {
        normalMap.wrapS = THREE.RepeatWrapping
        normalMap.wrapT = THREE.RepeatWrapping
      }
      
      // Create material with reflector
      const material = new ReflectorMaterial({
        map: map,
        normalMap: normalMap,
        normalScale: normalMap ? new THREE.Vector2(normalScale, normalScale) : undefined,
        reflectivity: reflectivity,
        mirror: mirror,
        mixStrength: mixStrength,
        fog: scene.fog,
        dithering: true
      })
      
      // Connect reflector with material
      material.uniforms.tReflect = reflector.renderTargetUniform
      material.uniforms.uMatrix = reflector.textureMatrixUniform
      
      // Create mesh
      const geometry = new THREE.PlaneGeometry(size[0], size[1])
      const mesh = new THREE.Mesh(geometry, material)
      mesh.position.set(position[0], position[1], position[2])
      mesh.rotation.set(rotation[0], rotation[1], rotation[2])
      mesh.add(reflector)
      
      // Setup reflection rendering
      mesh.onBeforeRender = (renderer, scene, camera) => {
        mesh.visible = false
        reflector.update(renderer, scene, camera)
        mesh.visible = true
      }
      
      scene.add(mesh)
      reflectorRef.current = mesh
    })
    
    // Cleanup
    return () => {
      if (reflectorRef.current) {
        scene.remove(reflectorRef.current)
      }
    }
  }, [
    scene, 
    gl, 
    position, 
    rotation, 
    size, 
    reflectivity, 
    mirror, 
    mixStrength, 
    textureBasePath, 
    baseColorTexture, 
    normalMapTexture, 
    normalScale
  ])
  
  return null
}