import React, { Suspense, useRef, useState, useEffect } from 'react';
import { Can<PERSON>, useFrame, useThree } from '@react-three/fiber';
import { useGLTF, /* OrbitControls, */ MeshTransmissionMaterial } from '@react-three/drei';
import * as THREE from 'three';
import WaterMatcapBackground from '../components/WaterMatcap/WaterMatcapBackground';
import ExplainOrbitCamera from '../components/WaterMatcap/scene_copy/ExplainOrbitCamera';
import { useControls } from 'leva';

function CenterModel(props) {
  const { nodes, materials } = useGLTF('/models/cube_high_comp.glb');
  const icon = useGLTF('/models/icon_ai_comp.glb');
  return (
    <group {...props} dispose={null}>
      {/* Render all meshes in the GLB, or adjust as needed */}
      {Object.values(nodes).map((node, i) =>
        node.type === 'Mesh' ? (
          <mesh
            key={i}
            geometry={node.geometry}
            castShadow
            receiveShadow
          >
            <MeshTransmissionMaterial
              backside
              samples={4}
              thickness={3}
              chromaticAberration={0}
              anisotropy={0.1}
              distortion={0}
              distortionScale={0}
              temporalDistortion={0}
              iridescence={1}
              iridescenceIOR={1}
              iridescenceThicknessRange={[0, 1400]}
            />
          </mesh>
        ) : null
      )}
      {/* Place the icon_ai_comp.glb model inside the CenterModel */}
      {icon.scene && (
        <primitive
          object={icon.scene}
          position={[0, 0, 0]}
          scale={[0.75, 0.75, 0.75]}
        />
      )}
    </group>
  );
}

useGLTF.preload('/models/extruded-try-soft-cube-round-whole.glb');

const CAMERA_POSITIONS = [
  { name: 'Top', position: [0, 1.5, 0], lookAt: [0, 0, 0] },
  { name: 'Front', position: [1.27, 0.81, -0.18], lookAt: [0, 0.50, 0] },
];

function ExplainCameraController({ cameraIndex }) {
  // Define the two positions
  const positions = [
    [0, 3, 0], // Top
    [3, 1, 0], // Side
  ];
  return null; // No camera logic here anymore
}

function BlackBackground() {
  const { scene } = useThree();
  React.useEffect(() => {
    scene.background = new THREE.Color('#000000');
    return () => {
      scene.background = null;
    };
  }, [scene]);
  return null;
}

// AnimatedCubeGroup for smooth vertical movement
function AnimatedCubeGroup({ targetIndex, CUBE_DISTANCE, centerY, children }) {
  const [animatedIndex, setAnimatedIndex] = React.useState(targetIndex);
  useFrame(() => {
    const lerpSpeed = 0.15;
    setAnimatedIndex((prev) => prev + (targetIndex - prev) * lerpSpeed);
  });
  return (
    <group position={[0, centerY - animatedIndex * CUBE_DISTANCE, 0]}>
      {React.Children.map(children, (child, i) => {
        if (Math.abs(i - animatedIndex) > 1) return null;
        return child;
      })}
    </group>
  );
}

export default function ExplainSection() {
  const [cameraIndex, setCameraIndex] = useState(0);

  const NUM_CUBES = 4;
  const CUBE_DISTANCE = 5; // vertical distance between cubes for 100vh spacing
  const [targetIndex, setTargetIndex] = useState(0);

  // Leva controls for interactive camera tuning
  const [{ camPos, camLookAt }, set] = useControls(() => ({
    camPos: { value: CAMERA_POSITIONS[0].position, label: 'Camera Position' },
    camLookAt: { value: CAMERA_POSITIONS[0].lookAt, label: 'Look At' },
  }), []);

  // OrbitControls ref
  // const controlsRef = useRef();

  // Reset OrbitControls when switching to Top
  /*
  useEffect(() => {
    if (cameraIndex === 0 && controlsRef.current) {
      controlsRef.current.target.set(0, 0, 0);
      controlsRef.current.update();
      if (controlsRef.current.setAzimuthalAngle) controlsRef.current.setAzimuthalAngle(0);
      if (controlsRef.current.setPolarAngle) controlsRef.current.setPolarAngle(0);
    }
  }, [cameraIndex]);
  */

  // Handler to update both cameraIndex and Leva controls
  const handleCameraButton = (idx) => {
    setCameraIndex(idx);
    set({
      camPos: CAMERA_POSITIONS[idx].position,
      camLookAt: CAMERA_POSITIONS[idx].lookAt,
    });
    // Reset cubeIndex when switching views (optional)
    // if (idx !== 1) setCubeIndex(0);
  };

  // Scroll to move between cubes in Front view
  useEffect(() => {
    if (cameraIndex !== 1) return;
    const handleWheel = (e) => {
      setTargetIndex((prev) => {
        let next = prev + e.deltaY * 0.01; // Lower = less sensitive
        next = Math.max(0, Math.min(NUM_CUBES - 1, next));
        return next;
      });
    };
    window.addEventListener('wheel', handleWheel, { passive: false });
    return () => window.removeEventListener('wheel', handleWheel);
  }, [cameraIndex]);

  return (
    <div style={{ position: 'relative', width: '100vw', height: '100vh', overflow: 'hidden' }}>
      <Canvas
        camera={{ position: camPos, fov: 45 }}
        gl={{ alpha: true, antialias: true, powerPreference: 'high-performance', clearColor: [0, 0, 0, 1] }}
        style={{ position: 'absolute', inset: 0, width: '100vw', height: '100vh', display: 'block', zIndex: 0 }}
      >
        <Suspense fallback={null}>
          <BlackBackground />
          <WaterMatcapBackground position={[0, 0, 0]} />
          {cameraIndex === 1 && (
            <AnimatedCubeGroup targetIndex={targetIndex} CUBE_DISTANCE={CUBE_DISTANCE} centerY={0.60}>
              {[...Array(NUM_CUBES)].map((_, i) => (
                <CenterModel
                  key={i}
                  position={[0, i * CUBE_DISTANCE, 0]}
                  scale={[0.20, 0.30, 0.20]}
                  rotation={[0, 2, 0]}
                />
              ))}
            </AnimatedCubeGroup>
          )}
          <ExplainOrbitCamera position={camPos} lookAt={camLookAt} />
        </Suspense>
      </Canvas>
      {/* Camera position buttons */}
      <div style={{ position: 'absolute', top: 20, left: 20, zIndex: 2, display: 'flex', gap: 8 }}>
        {CAMERA_POSITIONS.map((pos, idx) => (
          <button
            key={pos.name}
            onClick={() => handleCameraButton(idx)}
            style={{
              padding: '8px 16px',
              background: cameraIndex === idx ? '#222' : '#fff',
              color: cameraIndex === idx ? '#fff' : '#222',
              border: '1px solid #222',
              borderRadius: 4,
              cursor: 'pointer',
              fontWeight: 'bold',
            }}
          >
            {pos.name}
          </button>
        ))}
      </div>
      {/* Content for ExplainSection will go here */}
      <div style={{ position: 'relative', zIndex: 1 }}>
        {/* TODO: Add ExplainSection content here */}
      </div>
    </div>
  );
} 