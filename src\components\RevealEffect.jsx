import React, { useEffect, useRef } from 'react';

const RevealEffect = ({ targetRef }) => {
  const canvasRef = useRef(null);
  
  useEffect(() => {
    // Get the canvas and its context
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    // Get the target element (3D canvas)
    const target = targetRef.current;
    
    // Variables for the effect
    let width, height;
    let mouseX = 0, mouseY = 0;
    let particles = [];
    let isMouseMoving = false;
    let lastMouseX = 0, lastMouseY = 0;
    let mouseTimer;
    let fadeOutStarted = false;
    let fadeOutInterval;
    let fadeStartDelay;
    
    // Settings - simplified from original but keeping core functionality
    const settings = {
      particleCount: 5,
      particleBaseSize: 30,
      particleMaxSpeed: 3,
      fadeSpeed: 0.01,
      recoverySpeed: 0.03,
      brushSize: 60,
      smokeOpacity: 0.5,
      tailFadeDelay: 100,
      tailFadeSpeed: 0.03,
      fadeStartDelay: 300,
      canvasColor: "#000000", // Black to completely hide the model initially
      
      reset: function() {
        // Clear canvas
        ctx.fillStyle = settings.canvasColor;
        ctx.globalAlpha = 1.0;
        ctx.fillRect(0, 0, width, height);
        particles = [];
        fadeOutStarted = false;
        clearInterval(fadeOutInterval);
        clearTimeout(fadeStartDelay);
      }
    };
    
    // Resize function
    function resize() {
      // Match canvas to window size
      width = window.innerWidth;
      height = window.innerHeight;
      canvas.width = width;
      canvas.height = height;

      // Create initial mask - this will hide the 3D model completely
      ctx.fillStyle = settings.canvasColor;
      ctx.fillRect(0, 0, width, height);
    }
    
    // Particle class for the reveal effect
    class Particle {
      constructor(x, y) {
        this.x = x;
        this.y = y;
        this.size = settings.particleBaseSize + Math.random() * settings.particleBaseSize * 0.5;
        this.speedX = (Math.random() - 0.5) * settings.particleMaxSpeed;
        this.speedY = (Math.random() - 0.5) * settings.particleMaxSpeed;
        this.life = 400 + Math.random() * 200;
        this.opacity = settings.smokeOpacity;
        this.isFadingOut = false;
        this.creationTime = Date.now();
        this.fadeMultiplier = 1.0;
      }

      update() {
        this.x += this.speedX;
        this.y += this.speedY;
        this.life -= 1;

        // Normal fade or accelerated fade for tail-to-head effect
        if (this.isFadingOut) {
          // Gradually increase the fade multiplier for a smoother transition
          this.fadeMultiplier = Math.min(this.fadeMultiplier + 0.05, 3.0);
          this.opacity -= settings.tailFadeSpeed * this.fadeMultiplier;
        } else {
          this.opacity -= settings.fadeSpeed;
        }

        if (this.opacity < 0) this.opacity = 0;
      }

      draw() {
        ctx.globalCompositeOperation = "destination-out";
        const gradient = ctx.createRadialGradient(
          this.x,
          this.y,
          0,
          this.x,
          this.y,
          this.size
        );

        gradient.addColorStop(0, `rgba(0, 0, 0, ${this.opacity})`);
        gradient.addColorStop(1, "rgba(0, 0, 0, 0)");

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
      }
    }
    
    // Create particles at mouse position
    function createParticlesAtMouse() {
      for (let i = 0; i < settings.particleCount; i++) {
        const offsetX = (Math.random() - 0.5) * settings.brushSize;
        const offsetY = (Math.random() - 0.5) * settings.brushSize;
        particles.push(new Particle(mouseX + offsetX, mouseY + offsetY));
      }
    }

    // Start the tail-to-head fade out effect
    function startTailToHeadFadeOut() {
      if (particles.length === 0 || fadeOutStarted) return;

      fadeOutStarted = true;
      let fadeOutIndex = 0;

      // Sort particles by creation time (oldest first)
      particles.sort((a, b) => a.creationTime - b.creationTime);

      // Mark particles for accelerated fade out, starting from the oldest (tail)
      fadeOutInterval = setInterval(() => {
        if (fadeOutIndex < particles.length) {
          particles[fadeOutIndex].isFadingOut = true;
          fadeOutIndex++;
        } else {
          clearInterval(fadeOutInterval);
        }
      }, settings.tailFadeDelay);
    }
    
    // Helper function to convert hex to rgb
    function hexToRgb(hex) {
      // Remove # if present
      hex = hex.replace("#", "");

      // Parse the hex values
      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);

      return `${r}, ${g}, ${b}`;
    }

    // Animation loop
    function animate() {
      // Slowly fade back to canvas color (recover the mask)
      ctx.globalCompositeOperation = "source-over";
      ctx.fillStyle = `rgba(${hexToRgb(settings.canvasColor)}, ${
        settings.recoverySpeed
      })`;
      ctx.fillRect(0, 0, width, height);

      // Update and draw particles
      for (let i = 0; i < particles.length; i++) {
        particles[i].update();
        particles[i].draw();

        // Remove dead particles
        if (particles[i].life <= 0 || particles[i].opacity <= 0) {
          particles.splice(i, 1);
          i--;
        }
      }

      // Create a hole at the current mouse position if moving
      if (isMouseMoving) {
        ctx.globalCompositeOperation = "destination-out";
        const gradient = ctx.createRadialGradient(
          mouseX,
          mouseY,
          0,
          mouseX,
          mouseY,
          settings.brushSize
        );

        gradient.addColorStop(0, "rgba(0, 0, 0, 1)");
        gradient.addColorStop(1, "rgba(0, 0, 0, 0)");

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(mouseX, mouseY, settings.brushSize, 0, Math.PI * 2);
        ctx.fill();
      }

      requestAnimationFrame(animate);
    }

    // Initialize
    function init() {
      resize();
      window.addEventListener("resize", resize);

      // Mouse events - using window to capture all mouse movements
      window.addEventListener("mousemove", function (e) {
        mouseX = e.clientX;
        mouseY = e.clientY;

        // Check if mouse is actually moving
        if (
          Math.abs(mouseX - lastMouseX) > 3 ||
          Math.abs(mouseY - lastMouseY) > 3
        ) {
          // If we were in fade-out mode, cancel it
          if (fadeOutStarted) {
            fadeOutStarted = false;
            clearInterval(fadeOutInterval);
            clearTimeout(fadeStartDelay);
          }

          isMouseMoving = true;

          // Create particles on mouse move
          createParticlesAtMouse();

          lastMouseX = mouseX;
          lastMouseY = mouseY;

          // Reset timer
          clearTimeout(mouseTimer);
          mouseTimer = setTimeout(() => {
            isMouseMoving = false;

            // Start the tail-to-head fade out with a delay
            clearTimeout(fadeStartDelay);
            fadeStartDelay = setTimeout(() => {
              startTailToHeadFadeOut();
            }, settings.fadeStartDelay);
          }, 100);
        }
      });

      // Start animation
      animate();
    }

    // Start the effect
    init();

    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', resize);
      window.removeEventListener('mousemove', function (e) {});
      cancelAnimationFrame(animate);
      clearTimeout(mouseTimer);
      clearTimeout(fadeStartDelay);
      clearInterval(fadeOutInterval);
    };
  }, [targetRef]);

  return (
    <canvas 
      ref={canvasRef}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 2  // Position between the 3D canvas and the content
      }}
    />
  );
};

export default RevealEffect;