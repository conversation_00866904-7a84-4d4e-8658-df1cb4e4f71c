/* Import Satoshi font */
@import url('https://api.fontshare.com/v2/css?f[]=satoshi@900,700,500,301,300,400&display=swap');

/* Import local Telegraf fonts */
@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Archivo';
  src: url('/fonts/archivo-black-regular.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  font-family: 'Telegraf', sans-serif;
  color: black;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: transparent;
}

/* App structure */
.app-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: lightgrey;
}

.canvas-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
  pointer-events: none;
}

.canvas-container canvas {
  pointer-events: auto;
}

.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/blcks_simple_bg_light.jpg');
  background-size: cover;
  background-position: center;
  z-index: -1;
  opacity: 1;
}

/* Smooth transitions between views */
.main-view,
.project-view {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.5s ease;
}

.fadein {
  opacity: 1;
  pointer-events: auto;
}

.fadeout {
  opacity: 0;
  pointer-events: none;
}

.content-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 2;
  pointer-events: auto;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.content-container::-webkit-scrollbar {
  display: none;
}

/* Global section properties */
section {
  height: 100vh;
  width: 100%;
  position: relative;
  background-color: rgba(255, 255, 255, 0.0);
}

/* HOME SECTION */
#home {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 75vh;
}

#home .section-content {
  max-width: 1400px;
  width: 100%;
  padding: 0 2rem;
  margin-top: 10%;
  color: black;
}

#home .home-row-container {
  display: flex;
  align-items: center;
  width: 100%;
}

#home .home-logo {
  max-width: 450px;
  width: 100%;
  height: auto;
  object-fit: contain;
  margin-right: 80px;
  margin-left: 140px;
}

#home .home-heading {
  font-size: 1rem;
  font-weight: 500;
  color: #222;
  font-family: 'Telegraf', sans-serif;
  line-height: 1.3;
  margin-right: 200px;
  flex: 1;
}

#home .scroll-arrow {
  width: 24px;
  height: auto;
  margin-right: 20px;
}

#home .scroll-text {
  font-family: 'Telegraf', sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 1px;
  color: #222;
  white-space: nowrap;
}

/* SLOGAN SECTION */
#slogan {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100vh;
}

#slogan .section-content {
  max-width: 1300px;
  width: 100%;
  padding: 0 2rem;
  margin: 0 auto;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: left;
  color: black;
}

#slogan .slogan-text-container {
  max-width: 800px;
  width: 100%;
}

#slogan h2 {
  font-size: 2.5rem;
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  margin-bottom: 0;
  line-height: 1.2;
  text-align: left;
}

/* SHOWREEL SECTION */
#showreel {
  display: flex;
  align-items: center;
  justify-content: center;
}

#showreel .section-content {
  max-width: 1600px;
  width: 100%;
  padding: 0 2rem;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #222;
}

#showreel .showreel-text-container {
  width: 100%;
  max-width: 1400px;
}

#showreel .showreel-video {
  width: 100%;
  height: auto;
  aspect-ratio: 16/9;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* MISSION SECTION */
#mission {
  display: flex;
  align-items: center;
  justify-content: center;
}

#mission .section-content {
  max-width: 1400px;
  width: 100%;
  padding: 0 2rem;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  color: black;
}

#mission .mission-text-container {
  max-width: 1000px;
  width: 100%;
  text-align: center;
}

#mission h2 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 3.5rem;
  line-height: 1.3;
  text-align: center;
  color: #222;
  letter-spacing: 2px;
}

#mission .mission-description {
  font-size: 2.5rem;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  margin-bottom: 4rem;
}

/* SERVICES SECTION */
#services {
  display: flex;
  align-items: center;
  justify-content: center;
}

#services .section-content {
  max-width: 1400px;
  width: 100%;
  padding: 0 2rem;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #222;
}

#services .section-title {
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 2px;
  text-transform: uppercase;
  margin-bottom: 2rem;
}

#services .services-big-heading {
  font-size: 4rem;
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: 0.5px;
  margin-bottom: 2rem;
}

/* WORK SECTION */
#work {
  min-height: 600vh;
  padding: 10rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

#work .project-gallery {
  display: flex;
  flex-direction: column;
  gap: 40rem;
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 10rem;
}

#work .gallery-item {
  width: 100%;
  display: flex;
  position: relative;
}

#work .gallery-item.right-aligned {
  justify-content: flex-end;
  padding-left: 10%;
}

#work .gallery-item.center-left {
  justify-content: center;
  padding-right: 10%;
}

#work .project-image {
  width: 85%;
  padding-bottom: 47.8125%; 
  position: relative;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.05);
  z-index: 5;
  pointer-events: auto;
  border-radius: 8px;
  cursor: pointer;
}

/* Container for project heading elements */
#work .project-heading {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  pointer-events: none;
  opacity: 0; /* Hide by default */
  transition: opacity 0.3s ease;
  padding: 0 20px 20px 20px; /* Left/right/bottom padding of 20px */
  display: flex;
  justify-content: space-between; /* Space between project name and VIEW PROJECT */
  align-items: flex-end; /* Align items to bottom */
  width: 100%;
}

/* Left section container for project title and category */
#work .project-info-left {
  display: flex;
  flex-direction: row; /* Horizontal layout */
  align-items: baseline; /* Align text baselines */
  gap: 30px; /* Space between title and category */
}

/* Project title - bottom left */
#work .project-heading h2 {
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  letter-spacing: 2px;
  color: white;
  line-height: 1.2;
  margin: 0;
  transform: translateY(10px);
  transition: transform 0.3s ease, opacity 0.3s ease;
  opacity: 0;
}

/* Category field - right of project title */
#work .project-heading .project-category {
  font-family: 'Telegraf', sans-serif;
  font-weight: 400;
  font-size: 0.7rem;
  letter-spacing: 2px;
  color: rgb(170, 170, 170);
  opacity: 0;
  transform: translateY(10px);
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* VIEW PROJECT text - bottom right */
.project-heading .view-project {
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  font-size: 0.75rem;
  color: white;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
  letter-spacing: 2px;
  margin-left: auto; /* Push to right */
  /* No additional padding needed - container handles positioning */
}

#work .project-image:hover .project-heading {
  opacity: 1;
}

#work .project-image:hover .project-heading h2,
#work .project-image:hover .project-heading .project-category,
#work .project-image:hover .project-heading .view-project {
  opacity: 1;
  transform: translateY(0);
}

/* Add a subtle gradient overlay to ensure text is readable on any image */
#work .project-image::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50%; /* Gradient height - covers bottom half */
  background: linear-gradient(to top, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

#work .project-image:hover::after {
  opacity: 1;
}

#work .project-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  pointer-events: auto;
  cursor: pointer;
  transition: transform 0.3s ease;
}

/* Transition Overlay */
.transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  /* background-color is set by inline style */
  /* opacity is set by inline style */
  /* pointer-events is set by inline style */
  /* z-index is set by inline style */
  transition: opacity 0.5s ease-in-out; /* FADE_DURATION should match this in JS */
}

/* Adjust fadein/fadeout for main/project views to depend on overlay state */
.main-content-wrapper,
.project-view-wrapper {
  position: absolute; /* Ensure they can stack if needed, though one is usually hidden */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.5s ease-in-out; /* Standard fade for views */
}

.main-content-wrapper.fadein, 
.project-view-wrapper.fadein {
  opacity: 1;
  pointer-events: auto;
}

.main-content-wrapper.fadeout, 
.project-view-wrapper.fadeout {
  opacity: 0;
  pointer-events: none;
}

/* Grid effect container */
.grid-effect-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.grid-effect-container canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

#itemsWrapper {
  position: relative;
  z-index: 5;
}

.canvas-preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #e8e8e8;
  z-index: 0;
}

/* General back button style (shared between both pages) */
.back-button {
  background: transparent;
  border: 0px solid #e8e8e8;
  color: black;
  font-family: 'Telegraf', sans-serif;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.75rem 1.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Contact section background transition styles */
.contact-section {
  position: relative;
  min-height: 100vh;
  width: 100%;
  z-index: 5;
  background-color: transparent;
  transition: background-color 0.8s ease-in-out;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: black;
  opacity: 0;
  transition: opacity 0.8s ease-in-out;
  z-index: 0;
}

.contact-section.fade-to-black::before {
  opacity: 1;
}

#contact {
  min-height: 100vh;
  width: 100%;
  position: relative;
  z-index: 5;
  background-color: transparent; /* Light background instead of black */
}

.contact-content {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 2rem;
  position: relative;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.6s ease-in-out;
  transition-delay: 0.8s; /* Delay showing content until after background fade */
}

.contact-section.fade-to-black .contact-content {
  opacity: 1;
}

/* Transition all text elements to white when on black background */
.contact-section.fade-to-black .contact-email-heading,
.contact-section.fade-to-black .contact-link-button,
.contact-section.fade-to-black .contact-copyright,
.contact-section.fade-to-black .contact-built-by {
  color: white;
  transition: color 0.8s ease-in-out;
}

.contact-section.fade-to-black .contact-email-heading::after,
.contact-section.fade-to-black .contact-link-button::after {
  background-color: white;
  transition: background-color 0.8s ease-in-out;
}

.contact-email-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.contact-email-heading {
  font-size: 4rem;
  font-weight: 500;
  color: #ffffff; /* Dark color for light background */
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.contact-email-heading:hover {
  transform: scale(0);
  opacity: 0.7;
}

.contact-email-heading::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #ffffff; /* Dark color for light background */
  transition: transform 0.4s ease;
  transform-origin: left;
  transform: scaleX(0); /* Start with no line */
  color: #ffffff;
}

.contact-email-heading:hover::after {
  transform: scaleX(1);
  transform-origin: left;
  color: #ffffff;
}

.contact-email-link {
  text-decoration: none;
  color: inherit; /* Preserves the original text color */
}

.contact-email-link:hover {
  text-decoration: underline;
  cursor: pointer;
}


/* Contact Bottom */
.contact-bottom {
  display: flex;
  justify-content: space-between;
  position: relative;
  align-items: flex-end;
  width: 100%;
  padding-bottom: 0rem;
}

.contact-copyright,
.contact-built-by {
  font-size: 0.7rem;
  color: black; /* Dark color for light background */
  opacity: 0.75;
  letter-spacing: 1px;
  font-family: 'Telegraf', sans-serif;
}


/* Footer section layout */
.footer-section {
  display: flex;
  justify-content: space-between;
  width: 40%;
  margin: 0 auto;
  position: absolute;
  bottom: 150px;
  left: 10%;
  right: 10%;
}

/* Footer links container styling */
.footer-links-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}

/* Social icons styling - now positioned in the middle */
.social-icons {
  display: flex;
  gap: 10px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 50%;
}

.social-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: transparent;
  transition: opacity 0.3s ease;
}

.social-icon:hover {
  opacity: 0.7;
}

.social-icon img {
  width: 24px;
  height: 24px;
}

/* Footer link text styling */
.footer-links {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 10px;
}

.footer-link {
  color: #ffffff;
  text-decoration: none;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 1px;
  transition: opacity 0.3s ease;
}

.footer-link:hover {
  opacity: 0.7;
}

/* QR Code container styling */
.qr-code-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.qr-code-image {
  width: 100px;
  height: 100px;
  margin-bottom: 0;
}

.qr-code-heading {
  color: #ffffff;
  opacity: 0.7;
  font-size: 10px;
  font-weight: 400;
}

/* Navigation Button Styles */
.nav-button {
  position: fixed;
  top: 20px;
  right: 10px;
  background: transparent;
  border: none;
  color: black;
  font-family: 'Telegraf', sans-serif;
  font-size: 1rem;
  font-weight: 500;
  padding: 10px 20px;
  cursor: pointer;
  z-index: 1500;
  transition: all 0.3s ease;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
}

.nav-button .arrow-icon {
  margin-left: 6px;
  width: 24px;
  height: auto;
  opacity: 0;
  rotate: -90deg;
  transform: translateX(-5px);
  transition: all 0.3s ease;
}

.nav-button:hover .arrow-icon {
  opacity: 1;
  transform: translateX(0);
}

.nav-button:hover {
  opacity: 0.8;
}

/* Explicitly set the button color to white when contact section is active */
body.contact-active .nav-button {
  color: white;
}

body.contact-active .nav-button .arrow-icon {
  filter: brightness(0) invert(1); /* Make arrow white */
}





/* MOBILE RESPONSIVENESS */

/* Mobile responsiveness for the home section */
@media screen and (max-width: 767px) {
  #home .home-row-container {
    flex-direction: column;
    align-items: flex-start;
  }

  #home .home-logo {
    max-width: 175px; /* Make logo smaller on mobile */
    margin-right: 0;
    margin-left: -10px;
    margin-top: 30%;
    margin-bottom: 20px;
  }

  #home .home-heading {
    font-size: 1rem; /* Make heading a bit bigger */
    margin-right: 0;
    margin-bottom: 30px;
  }
  
  #home .scroll-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  #home .scroll-text {
    font-size: 0.65rem; /* Make scroll text smaller */
    margin-right: 10px;
    order: 1;
  }

  #home .scroll-arrow {
    margin-right: 0;
    order: 2;
  }
  
  #home .section-content {
    padding: 0 1.5rem;
    margin-top: 15%;
  }
}


/* Mobile responsiveness for the slogan section */
@media screen and (max-width: 767px) {
  #slogan {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  /* Custom height for slogan section */
  #slogan {
    height: 60vh; /* Example: even shorter */
  }
  
  #slogan .section-content {
    padding: 0 1.5rem;
  }
  
  #slogan .slogan-text-container {
    width: 100%;
  }
  
  #slogan h2 {
    font-size: 1.3rem; /* Smaller heading size for mobile */
    line-height: 1.3;
  }
}

@media screen and (max-width: 767px) {
  /* Custom height for showreel section */
  #showreel {
    height: 40vh; /* Example: taller */
  }
}


/* Mobile responsiveness for the mission section */
@media screen and (max-width: 767px) {
  #mission {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Custom height for mission section */
  #mission {
    height: 70vh; /* Example: medium height */
  }
  
  #mission .section-content {
    padding: 0 1.5rem;
  }
  
  #mission .mission-text-container {
    width: 100%;
  }
  
  #mission h2 {
    font-size: 0.75rem; /* Smaller h2 size for mobile */
    margin-bottom: 2rem;
  }
  
  #mission .mission-description {
    font-size: 1.3rem; /* Smaller animated mission text for mobile */
    line-height: 1.4;
    margin-bottom: 0rem;
  }
}




/* Mobile responsiveness for the services section */
@media screen and (max-width: 767px) {
  #services {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Custom height for services section */
  #services {
    height: 75vh; /* Example: slightly taller */
  }
  
  #services .section-content {
    padding: 0 1.5rem;
  }
  
  #services .section-title {
    font-size: 0.75rem; /* Smaller section title size for mobile */
    margin-bottom: 1.5rem;
  }
  
  #services .services-big-heading {
    font-size: 2rem; /* Smaller big heading size for mobile */
    line-height: 1.25;
    margin-bottom: 0rem;
    text-align: left;
  }
}




/* Mobile responsiveness for the work section */
@media screen and (max-width: 767px) {
  #work {
    min-height: 200vh; /* Adjust total height for mobile */
    padding: 5rem 0;
  }
  
  #work .project-gallery {
    gap: 10rem; /* Reduce gap between projects on mobile */
    padding: 0 1.5rem; /* Consistent padding with other sections */
  }
  
  #work .gallery-item {
    justify-content: center; /* Center all gallery items on mobile */
    padding-left: 0;
    padding-right: 0;
  }
  
  #work .gallery-item.right-aligned,
  #work .gallery-item.center-left {
    justify-content: center; /* Override desktop positioning */
    padding-left: 0;
    padding-right: 0;
  }
  
  #work .project-image {
    width: 100%; /* Make images full width on mobile */
    padding-bottom: 56.25%; /* Maintain aspect ratio (16:9) */
  }
  
  /* Adjust the project heading text sizes */
  #work .project-heading h2 {
    font-size: 0.9rem;
  }
  
  #work .project-heading .project-category {
    font-size: 0.65rem;
  }
  
  #work .project-heading .view-project {
    font-size: 0.7rem;
  }
}





/* Mobile responsiveness for the footer/contact section */
@media screen and (max-width: 767px) {
  #contact {
    min-height: 100vh;
    position: relative;
  }
  
  .contact-content {
    padding: 3rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
  }
  
  .contact-email-heading {
    font-size: 2rem !important;
    text-align: center;
    margin-bottom: 20px;
  }
  
  /* Additional container centering */
  .contact-bottom {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding-bottom: 150px !important;
    text-align: center;
  }
  
  /* Specific centering fix */
  .footer-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin-bottom: 50px;
    order: 1;
    padding: 0;
    position: static; /* Override the absolute positioning from desktop */
    bottom: auto;
    left: auto;
    right: auto;
  }
  
/* Social icons - direct flex approach with explicit spacing */
  .social-icons {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin: 0 auto;
    order: 2;
    padding: 0;
    gap: 15px; /* Equal spacing between icons */
    position: static; /* Override absolute positioning from desktop */
    left: auto; /* Reset desktop value */
    transform: none; /* Reset desktop transform */
    bottom: auto; /* Reset desktop value */
  }
  
 .social-icon {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0; /* Remove any margin */
  }
  
  .social-icon img {
    width: 20px;
    height: 20px;
  }
  

  /* Footer links - top */
  .footer-links-container {
    width: 100%;
    margin-bottom: 20px;
    order: 1;
    display: flex;
    justify-content: center;
    position: static; /* Override any inherited positioning */
  }
  
  .footer-links {
    display: flex;
    flex-direction: column;
    gap: 15px;
    justify-content: center;
    align-items: center;
    width: auto; /* Changed from 100% to auto */
    text-align: center; /* Center text */
    margin: 0 auto; /* Center the container */
  }
  
  .footer-link {
    text-align: center;
    font-size: 0.8rem;
    width: auto; /* Changed from 100% to auto */
    display: inline-block; /* Changed from block to inline-block */
    margin: 0 auto; /* Center links */
  }
  
  /* Built by text - above copyright */
  .contact-built-by {
    width: 100%;
    text-align: center;
    font-size: 0.6rem;
    margin-bottom: 15px;
    order: 2;
  }
  
  /* Copyright text - bottom */
  .contact-copyright {
    width: 100%;
    text-align: center;
    font-size: 0.6rem;
    order: 3;
  }
  
  /* Hide the QR code on mobile */
  .qr-code-container {
    display: none !important;
  }

  /* Force center with transform method as backup */
  #contact .footer-section .social-icons {
    position: static;
    width: 100%;
  }

}




@media screen and (max-width: 767px) {
  /* Other mobile styles... */
  
  /* Adjust navigation "About" text size for mobile */
  .nav-button {
    font-size: 0.85rem;  /* Reduced font size for mobile */
    padding: 4px 0px;   /* Optional: slightly reduce padding too */
  }
}



/* Add to your App3.css file */
@media (max-width: 768px) {
  #contact.fade-to-black {
    background-color: #000 !important;
    color: #fff !important;
    transition: background-color 0.5s ease-in-out, color 0.5s ease-in-out;
  }
  
  body.contact-active {
    background-color: #000;
    transition: background-color 0.5s ease-in-out;
  }
}