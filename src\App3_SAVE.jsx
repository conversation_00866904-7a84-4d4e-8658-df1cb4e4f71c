import { Canvas } from '@react-three/fiber'
import { useTexture } from '@react-three/drei'
import { Suspense, useEffect, useRef, useState, useCallback } from 'react'
import './App3.css' // Your CSS file
import { useThree } from '@react-three/fiber'
import Lenis from 'lenis'
import NoiseFilter from './components/NoiseFilter.jsx';
import { Fluid } from '@whatisjery/react-fluid-distortion';
import { EffectComposer } from '@react-three/postprocessing';
import { useInView } from 'react-intersection-observer';

//Import Pages
import ProjectPage from './ProjectPage';
import About from './About';
import Services from './Services';
import Steps from './Steps';
import Contact from './Contact';

import AnimatedText from './components/AnimatedText';
//import SimpleFadeSection from './components/SimpleFadeSection';

const projectsData = {
    nordwood: { 
    id: "nordwood", 
    name: "NØRDWOOD", 
    title: "NØRDWOOD – ein modernes Chalet-Erlebnis inmitten des Waldes.", 
    description: "<PERSON><PERSON><PERSON>, eine exklusive Chalet-Anlage im Wald, entwickelten wir eine Website, die den natürlichen Lifestyle und die besondere Atmosphäre der Chalets digital transportiert. Durch stimmige Bildwelten, klare Strukturen und interaktive Elemente erleben Besucher das Zusammenspiel von Natur, Komfort und Aktivität. Die Seite ist so gestaltet, dass potenzielle Gäste mühelos Informationen finden, sich inspirieren lassen und ihren Aufenthalt unkompliziert buchen können. Ein emotionaler und funktionaler Auftritt, der NordWood als Wohlfühlort positioniert.", 
    aboutTitle: "Eine digitale Plattform, die das einzigartige Natur- und Wohlfühlkonzept von NØRDWOOD charmant und einladend erlebbar macht.",
    heroImage: "/nordwood_bw_home_comp.jpg", 
    heroImage2: "/nordwood_main.jpg",
    heroVideo: {
      src: "/nordwood_main.jpg",
      type: "video/mp4",
      autoplay: true,
      loop: true,
      muted: true,
    },
    category: "DESIGN · BOOKING", 
    client: "Concept", 
    year: "2025", 
    images: [
      {
        type: "video",
        src: "https://files.blcks.at/nordwood_video_4.mp4",
        videoType: "video/mp4",
        autoplay: true,
        loop: true,
        muted: true,
        controls: false
      },
      {
        type: "video",
        src: "https://files.blcks.at/nordwood_video_3.mp4",
        videoType: "video/mp4",
        autoplay: true,
        loop: true,
        muted: true,
        controls: false
      },
      {
        type: "video",
        src: "https://files.blcks.at/nordwood_video_5.mp4",
        videoType: "video/mp4",
        autoplay: true,
        loop: true,
        muted: true,
        controls: false
      },
      {
        type: "video",
        src: "https://files.blcks.at/nordwood_video_6.mp4",
        videoType: "video/mp4",
        autoplay: true,
        loop: true,
        muted: true,
        controls: false
      },
    ]
  },
  qube: { 
    id: "qube", 
    name: "QUBE", 
    title: "QUBE – eine interaktive 3D-Webreise für einen AI-Agenten der nächsten Generation", 
    description: "Für QUBE, einen High-End-AI-Agenten mit Fokus auf die Hotelbranche, entwickelten wir eine interaktive 3D-Website, die die Einsatzbereiche von Künstlicher Intelligenz auf visuelle und intuitive Weise präsentiert. Nutzer navigieren durch eine eigens gestaltete Welt, in der QUBE seine Stärken zeigt: von smarter Gästekommunikation bis hin zur Automatisierung interner Prozesse. Die Kombination aus narrativem Scrollverhalten, klarer Gestaltung und technologischem Anspruch macht QUBE nicht nur verständlich – sondern erlebbar.", 
    aboutTitle: "Eine digitale Welt, die die Möglichkeiten von QUBE als smarten AI-Agenten für Hotels erlebbar macht – visuell stark, funktional durchdacht.",
    aboutTitleStyle: {
      fontSize: '2.5rem',
      fontWeight: '500',
      color: 'black',
      fontFamily: 'Telegraf, sans-serif',
    },
    descriptionStyle: {
      fontSize: '1.25rem',
      lineHeight: '1.6',
      color: 'black',
      margin: 0
    },
    infoGridStyle: {
      gap: '400px',  // Customize the gap between title and description
    },
    heroImage: "/qubehotel_bw_home.jpg",
    heroVideo: {
      src: "https://files.blcks.at/qubehotel_video_1.mp4",
      type: "video/mp4",
      autoplay: true,
      loop: true,
      muted: true,
    },
    category: "WEB · DESIGN · 3D · AI", 
    client: "Concept", 
    year: "2025", 
    images: [
      {
        type: "image",
        src: "/qubehotel_image_2.jpg", 
        alt: "qubehotel world overview"
      },
      {
        type: "video",
        src: "https://files.blcks.at/qubehotel_video_2.mp4",
        videoType: "video/mp4",
        autoplay: true,
        loop: true,
        muted: true,
        controls: false
      },
      {
        type: "video",
        src: "https://files.blcks.at/qubehotel_video_3.mp4",
        videoType: "video/mp4",
        autoplay: true,
        loop: true,
        muted: true,
        controls: false
      },
      {
        type: "video",
        src: "https://files.blcks.at/qubehotel_video_4.mp4",
        videoType: "video/mp4",
        autoplay: true,
        loop: true,
        muted: true,
        controls: false
      },

    ]
  },
  forgeracer: { 
    id: "forgeracer", 
    name: "FORGERACER", 
    title: "Digitale Inszenierung der ForgeRacer-DNA – kraftvoll, detailverliebt, einzigartig.", 
    description: "Für ForgeRacer, eine Werkstatt für individuelle Café Racer, entwickelten wir eine hochwertige Webpräsenz mit 3D-Visualisierungen, interaktiven Animationen und einer klaren Designsprache. Ziel war es, das Handwerk, die Ästhetik und die Haltung der Marke digital erlebbar zu machen – kompromisslos, authentisch und mit viel Liebe zum Detail.", 
    aboutTitle: "Eine digitale Markenwelt, die die rohe Ästhetik und Individualität handgefertigter Café Racer erlebbar macht.",
    aboutTitleStyle: {
      fontSize: '2.5rem',
      fontWeight: '500',
      color: 'black',
      fontFamily: 'Telegraf, sans-serif',
    },
    descriptionStyle: {
      fontSize: '1.25rem',
      lineHeight: '1.6',
      color: 'black',
      margin: 0
    },
    infoGridStyle: {
      gap: '400px',  // Customize the gap between title and description
    },
    heroImage: "/forgeracer_bw_home.jpg",
    heroVideo: {
      src: "https://files.blcks.at/Project1_BGRVideo_Speed2x_webcomp%20-%20Kopie.mp4",
      type: "video/mp4",
      autoplay: true,
      loop: true,
      muted: true,
    },
    category: "WEB · DESIGN · 3D", 
    client: "Concept", 
    year: "2024",
    images: [
      {src: "/forgeracer_1.avif", alt: "FORGERACER 1"},
      {src: "/forgeracer_2.avif", alt: "FORGERACER 2"},
      {src: "/forgeracer_3.avif", alt: "FORGERACER 3"},
      {src: "/forgeracer_4.avif", alt: "FORGERACER 4"}
    ]
  },
  lunchbox: { 
    id: "lunchbox", 
    name: "LUNCHBOX", 
    title: "Streamlined Food Delivery App Focused on Curation and Experience", 
    description: "Lunchbox is a concept mobile application that transforms the food delivery experience by emphasizing curation over endless choice. We designed an intuitive interface that reduces decision fatigue while increasing user satisfaction through personalized recommendations and visually-driven content. The app features intelligent filtering based on user preferences, dietary requirements, and past behaviors, creating a seamless journey from food discovery to delivery tracking.", 
    aboutTitle: "LUXURY MEETS METAVERSE",
    heroImage: "/glenn-catteeuw.webp", 
    heroVideo: {
      src: "/lunchbox_hero.mp4",
      type: "video/mp4",
      autoplay: true,
      loop: true,
      muted: true,
    },
    category: "UI/UX DESIGN · MOBILE APP · INTERACTION DESIGN", 
    client: "Concept", 
    year: "2023", 
    images: [
      {src: "/lunchbox_image1.jpg", alt: "Lunchbox app user interface"},
      {src: "/lunchbox_image2.jpg", alt: "Lunchbox ordering flow"},
      {src: "/lunchbox_image3.jpg", alt: "Lunchbox personalization features"},
      {src: "/lunchbox_image4.jpg", alt: "Lunchbox visual design system"}
    ]
  },
  victorinox: { 
    id: "victorinox", 
    name: "HIER FEHLT NUR EINS: DEIN PROJEKT.", 
    title: "Heritage Collection Digital Experience for Swiss Icon", 
    description: "This concept project for Victorinox reimagines how the iconic Swiss brand could showcase its heritage collection online. We created an interactive digital experience that combines storytelling with detailed product visualization, allowing users to explore the craftsmanship behind each item. The design emphasizes Victorinox's legendary precision and attention to detail through high-resolution imagery, animation, and contextual information that connects products to their historical significance.", 
    aboutTitle: "LUXURY MEETS METAVERSE",
    heroImage: "/poppr_bw.png", 
    heroVideo: {
      src: "https://files.blcks.at/sphere.mp4",
      type: "video/mp4",
      autoplay: true,
      loop: true,
      muted: true,
    },
    category: "", 
    client: "Victorinox (Concept)", 
    year: "2024", 
    images: [
      {src: "/victorinox_image1.jpg", alt: "Victorinox heritage collection showcase"},
      {src: "/victorinox_image2.jpg", alt: "Victorinox product detail experience"},
      {src: "/victorinox_image3.jpg", alt: "Victorinox craftsmanship story"},
      {src: "/victorinox_image4.jpg", alt: "Victorinox responsive design"}
    ]
  },
};

function Background() {
  const texturePath = '/blcks_simple_bg_light.jpg'; 
  const texture = useTexture(texturePath);
  useEffect(() => {
    // console.log(`Loading background texture: ${texturePath}`);
  }, [texturePath]);
  return <primitive object={texture} attach="background" />;
}

function OverlayNoiseFilter() {
  const { camera } = useThree();
  useEffect(() => { camera.layers.enable(31); }, [camera]);
  return <NoiseFilter intensity={0.10} speed={0.8} />;
}

function AutomatedFluidEffect() {
  const canvasRef = useRef(null);
  const animationStateRef = useRef({ isAnimating: false, lastX: 0, lastY: 0 });
  
  useEffect(() => {
    const getCanvas = () => {
      // Ensure we are targeting the visible, interactive canvas
      canvasRef.current = document.querySelector('.canvas-container canvas');
      return canvasRef.current;
    };
    const createPointerMove = (x, y) => {
      const canvas = getCanvas();
      if (!canvas) return;
      const event = new PointerEvent('pointermove', { clientX: x, clientY: y, bubbles: true });
      canvas.dispatchEvent(event);
      animationStateRef.current.lastX = x;
      animationStateRef.current.lastY = y;
    };
    const generateSmoothPath = (startX, startY, endX, endY, numPoints) => {
      const points = [];
      for (let i = 0; i <= numPoints; i++) {
        const t = i / numPoints;
        const controlX = (startX + endX) / 2 + (Math.random() - 0.5) * 100;
        const controlY = (startY + endY) / 2 + (Math.random() - 0.5) * 100;
        const x = Math.pow(1-t, 2) * startX + 2 * (1-t) * t * controlX + Math.pow(t, 2) * endX;
        const y = Math.pow(1-t, 2) * startY + 2 * (1-t) * t * controlY + Math.pow(t, 2) * endY;
        points.push({ x, y });
      }
      return points;
    };
    const simulateMousePath = () => {
      const canvas = getCanvas();
      if (!canvas || animationStateRef.current.isAnimating) return;
      animationStateRef.current.isAnimating = true;
      const rect = canvas.getBoundingClientRect();
      const startX = animationStateRef.current.lastX || (Math.random() * rect.width + rect.left);
      const startY = animationStateRef.current.lastY || (Math.random() * rect.height + rect.top);
      const endX = Math.random() * rect.width + rect.left;
      const endY = Math.random() * rect.height + rect.top;
      const numPoints = Math.floor(Math.random() * 10) + 15;
      const points = generateSmoothPath(startX, startY, endX, endY, numPoints);
      const baseDelay = 40;
      points.forEach((point, index) => {
        const pointDelay = baseDelay + Math.random() * 15;
        setTimeout(() => {
          createPointerMove(point.x, point.y);
          if (index === points.length - 1) {
            setTimeout(() => { animationStateRef.current.isAnimating = false; }, 300);
          }
        }, index * pointDelay);
      });
    };
    const getRandomInterval = () => (Math.random() * 3000) + 3000;
    let intervalId;
    const timeoutId = setTimeout(() => {
      simulateMousePath();
      intervalId = setInterval(simulateMousePath, getRandomInterval());
    }, 1000);
    return () => { clearTimeout(timeoutId); clearInterval(intervalId); };
  }, []);
  return null;
}

const FADE_DURATION = 500; // ms


function App() {
  const [currentView, setCurrentView] = useState({ type: 'main', projectId: null, section: 'home' });
  const [activeSection, setActiveSection] = useState('home');
  const [overlayStyle, setOverlayStyle] = useState({
    opacity: 0, 
    backgroundColor: '#e8e8e8',
    pointerEvents: 'none', 
    zIndex: -1,
  });

  // Inside the App component
  const [contactRef, inViewContact] = useInView({
    threshold: 0.9,
    triggerOnce: false
  });

  // Effect to handle contact section visibility
  useEffect(() => {
    const contactSection = document.getElementById('contact');
    
    if (inViewContact && contactSection) {
      // Add class when contact section is in view
      contactSection.classList.add('fade-to-black');
    } else if (!inViewContact && contactSection) {
      // Remove class when scrolling away
      contactSection.classList.remove('fade-to-black');
    }
  }, [inViewContact]);

  // Add this to your App.js, replacing the existing contact section visibility effect
  useEffect(() => {
    const contactSection = document.getElementById('contact');
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Contact section is in view, add class to body
          document.body.classList.add('contact-active');
          contactSection.classList.add('fade-to-black');
        } else {
          // Contact section is out of view, remove class from body
          document.body.classList.remove('contact-active');
          contactSection.classList.remove('fade-to-black');
        }
      });
    }, { threshold: 0.4 }); // Trigger when 40% of the section is visible
    
    if (contactSection) {
      observer.observe(contactSection);
    }
    
    return () => {
      if (contactSection) {
        observer.unobserve(contactSection);
      }
    };
  }, []);



  // For transition effects - similar to ProductPage.js
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isDarkMode, setIsDarkMode] = useState(false);
  
  const scrollContainerRef = useRef(null);
  const mainLenisRef = useRef(null);
  const sectionsRef = useRef({
    home: null, mission: null, services: null, work: null, slogan: null, showreel: null, contact: null
  });
  const mainScrollStateRef = useRef({ scrollTop: 0 });
  // Store the work section path for back button navigation
  const workSectionPathRef = useRef({ path: "/", scrollTop: 0 });
  
  // Refs for videos
  const showreelVideoRef = useRef(null);
  const victorinoxVideoRef = useRef(null);
  
  // Throttle function to prevent excessive calculations
  const throttle = (func, limit) => {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  };

  useEffect(() => {
    if ('scrollRestoration' in window.history) {
      window.history.scrollRestoration = 'manual';
    }

    const initialPath = window.location.pathname;
    const initialPathSegments = initialPath.split('/').filter(Boolean);
    let determinedInitialSection = 'home';
    let determinedInitialScrollTop = 0;
    let determinedViewType = 'main';
    let determinedProjectId = null;

    if (initialPath === '/' || initialPath === '/home') {
      determinedInitialSection = 'home';
      determinedInitialScrollTop = 0;
      window.history.replaceState({ type: 'main', section: 'home', scrollTop: 0 }, '', '/');
    } else if (initialPathSegments.length === 2 && initialPathSegments[0] === 'work' && projectsData[initialPathSegments[1]]) {
      determinedViewType = 'project';
      determinedProjectId = initialPathSegments[1];
      determinedInitialSection = null; 
    } else if (initialPathSegments.length === 1) { 
      const potentialSection = initialPathSegments[0];
      if (Object.keys(sectionsRef.current).includes(potentialSection)) {
        determinedInitialSection = potentialSection;
      } else {
        determinedInitialSection = 'home'; 
      }
      determinedInitialScrollTop = 0; 
      const newPathForReplace = `/${determinedInitialSection === 'home' ? '' : determinedInitialSection}`;
      window.history.replaceState({ type: 'main', section: determinedInitialSection, scrollTop: 0 }, '', newPathForReplace);
    } else { 
      determinedInitialSection = 'home';
      determinedInitialScrollTop = 0;
      window.history.replaceState({ type: 'main', section: 'home', scrollTop: 0 }, '', '/');
    }

    mainScrollStateRef.current.scrollTop = determinedInitialScrollTop;
    setActiveSection(determinedViewType === 'main' ? determinedInitialSection : 'home'); 
    setCurrentView({ 
      type: determinedViewType, 
      projectId: determinedProjectId, 
      section: determinedViewType === 'main' ? determinedInitialSection : null 
    });

    const handlePopStateInternal = (event) => {
      setOverlayStyle({ 
        opacity: 1, 
        backgroundColor: '#e8e8e8',
        pointerEvents: 'auto', 
        zIndex: 1000 
      });
      setTimeout(() => {
        const state = event.state || {};
        const path = window.location.pathname;
        const pathSegmentsPop = path.split('/').filter(Boolean);
        let isNavigatingToProject = false;
        let newViewConfig = {};

        if (state.type === 'project' && state.projectId && projectsData[state.projectId]) {
          newViewConfig = { type: 'project', projectId: state.projectId };
          isNavigatingToProject = true;
        } else if (pathSegmentsPop.length === 2 && pathSegmentsPop[0] === 'work' && projectsData[pathSegmentsPop[1]]) {
          newViewConfig = { type: 'project', projectId: pathSegmentsPop[1] };
          isNavigatingToProject = true;
        } else { 
          const sectionFromState = state.section || (pathSegmentsPop.length === 1 && Object.keys(sectionsRef.current).includes(pathSegmentsPop[0]) ? pathSegmentsPop[0] : 'home');
          mainScrollStateRef.current.scrollTop = state.scrollTop || 0; 
          newViewConfig = { type: 'main', projectId: null, section: sectionFromState };
          setActiveSection(sectionFromState); 
        }
        setCurrentView(newViewConfig);
        if (!isNavigatingToProject) { 
          setTimeout(() => {
            setOverlayStyle(prev => ({ ...prev, opacity: 0 }));
            setTimeout(() => { 
              setOverlayStyle({ 
                opacity: 0, 
                backgroundColor: '#e8e8e8',
                pointerEvents: 'none', 
                zIndex: -1 
              });
            }, FADE_DURATION);
          }, 100); 
        }
      }, FADE_DURATION);
    };
    
    // Add event listener for project navigation (for "See next Project" feature)
    const handleProjectNavigation = (event) => {
      const { projectId, projectName } = event.detail;
      
      // Store current scroll position if in main view
      if (mainLenisRef.current) {
        mainScrollStateRef.current.scrollTop = mainLenisRef.current.scroll;
      }
      
      // Set the overlay to be visible
      setOverlayStyle({ 
        opacity: 1, 
        backgroundColor: '#e8e8e8',
        pointerEvents: 'auto', 
        zIndex: 1000 
      });
      
      // Navigate to the new project without going back to homepage
      setTimeout(() => {
        const newPath = `/work/${projectId}`;
        window.history.pushState({ type: 'project', projectId }, projectName, newPath);
        setCurrentView({ type: 'project', projectId });
        
        // Reset the scroll position of any project content container
        const projectContentContainer = document.querySelector('.project-content-container');
        if (projectContentContainer) {
          projectContentContainer.scrollTop = 0;
        }
      }, FADE_DURATION);
    };
    
    window.addEventListener('popstate', handlePopStateInternal);
    window.addEventListener('navigateToProject', handleProjectNavigation);
    
    return () => {
      window.removeEventListener('popstate', handlePopStateInternal);
      window.removeEventListener('navigateToProject', handleProjectNavigation);
    };
  }, []); 

  // Effect for showreel video visibility
  useEffect(() => {
    if (!showreelVideoRef.current) return;
    
    const options = {
      root: null, // use viewport
      rootMargin: '0px',
      threshold: 0.3 // video plays when 30% visible
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Try to play the video
          const playPromise = showreelVideoRef.current.play();
          
          // Handle potential play() rejection
          if (playPromise !== undefined) {
            playPromise.catch(error => {
              console.error("Video play failed:", error);
            });
          }
        } else {
          showreelVideoRef.current.pause();
        }
      });
    }, options);

    observer.observe(showreelVideoRef.current);
    
    return () => {
      if (showreelVideoRef.current) {
        observer.unobserve(showreelVideoRef.current);
      }
    };
  }, [showreelVideoRef.current]);

  // Effect for victorinox video visibility
  useEffect(() => {
    if (!victorinoxVideoRef.current) return;
    
    const options = {
      root: null, // use viewport
      rootMargin: '0px',
      threshold: 0.3 // video plays when 30% visible
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Try to play the video
          const playPromise = victorinoxVideoRef.current.play();
          
          // Handle potential play() rejection
          if (playPromise !== undefined) {
            playPromise.catch(error => {
              console.error("Victorinox video play failed:", error);
            });
          }
        } else {
          victorinoxVideoRef.current.pause();
        }
      });
    }, options);

    observer.observe(victorinoxVideoRef.current);
    
    return () => {
      if (victorinoxVideoRef.current) {
        observer.unobserve(victorinoxVideoRef.current);
      }
    };
  }, [victorinoxVideoRef.current]);

  useEffect(() => {
    let lenisInstance = null;
    let rafId = null;
    const container = scrollContainerRef.current;

    function raf(time) { 
      if (lenisInstance) {
        lenisInstance.raf(time);
        rafId = requestAnimationFrame(raf);
      }
    }

    if (currentView.type === 'main' && container) {
      lenisInstance = new Lenis({
        duration: 3.0, 
        easing: (t) => (t === 1 ? 1 : 1 - Math.pow(2, -10 * t)),
        orientation: 'vertical', 
        gestureOrientation: 'vertical', 
        smoothWheel: true,
        wheelMultiplier: 0.8, 
        smoothTouch: true, 
        touchMultiplier: 1.5, 
        infinite: false,
        wrapper: container, 
        content: container.querySelector('.all-sections-content'),
      });
      
      mainLenisRef.current = lenisInstance;
      
      // Update section refs once
      Object.keys(sectionsRef.current).forEach(sectionId => {
        sectionsRef.current[sectionId] = document.getElementById(sectionId);
      });
      
      // Throttled scroll handler for better performance
      const handleScrollThrottled = throttle((e) => {
        // Store current scroll position
        const scrollPosition = lenisInstance.scroll;
        mainScrollStateRef.current.scrollTop = scrollPosition;
        
        // Update active section based on scroll position
        const windowHeight = window.innerHeight;
        let currentActiveSection = activeSection;
        let minDistance = Infinity;
        
        Object.entries(sectionsRef.current).forEach(([id, element]) => {
          if (!element) return; // Skip null elements
          
          const elementRect = element.getBoundingClientRect();
          const elementCenter = elementRect.top + elementRect.height / 2;
          const distanceFromCenter = Math.abs(elementCenter - windowHeight / 2);
          
          if (distanceFromCenter < minDistance) {
            minDistance = distanceFromCenter;
            currentActiveSection = id;
          }
        });
        
        if (currentActiveSection !== activeSection) {
          setActiveSection(currentActiveSection);
        }
        
      }, 50); // Using a shorter throttle time for smoother transitions
      
      lenisInstance.on('scroll', handleScrollThrottled);
      
      // Initial section setting when component mounts or view changes
      if (currentView.section && sectionsRef.current[currentView.section]) {
        const targetSection = sectionsRef.current[currentView.section];
        
        // If we have a stored scroll position for this section, use it
        const historyState = window.history.state;
        if (historyState?.type === 'main' && historyState.section === currentView.section && typeof historyState.scrollTop === 'number') {
          // Use stored position
          setTimeout(() => {
            if (lenisInstance && !lenisInstance.isStopped) {
              lenisInstance.scrollTo(historyState.scrollTop, { immediate: true });
            }
          }, 10);
        } else if (currentView.section !== 'home') {
          // Scroll to section
          setTimeout(() => {
            if (lenisInstance && !lenisInstance.isStopped && targetSection) {
              lenisInstance.scrollTo(targetSection, { immediate: false });
            }
          }, 10);
        }
      }
      
      // Start the animation frame loop
      rafId = requestAnimationFrame(raf);
      
      return () => {
        if (rafId) {
          cancelAnimationFrame(rafId);
        }
        
        if (lenisInstance) {
          lenisInstance.off('scroll', handleScrollThrottled);
          lenisInstance.destroy();
        }
        
        mainLenisRef.current = null;
      };
    } 
  }, [currentView.type, currentView.section]); 

  const handleProjectClick = useCallback((projectId) => {
    const project = projectsData[projectId];
    if (project) {
      // Skip handling click for victorinox project
      if (projectId === 'victorinox') {
        return; // Don't do anything when victorinox is clicked
      }
      
      if (mainLenisRef.current) {
        const currentScrollPosition = mainLenisRef.current.scroll;
        mainScrollStateRef.current.scrollTop = currentScrollPosition;
        
        // Save the exact scroll position
        workSectionPathRef.current = {
          path: "/",
          scrollTop: currentScrollPosition
        };
      
        window.history.replaceState(
          { type: 'main', section: 'work', scrollTop: mainScrollStateRef.current.scrollTop },
          '', 
          window.location.pathname 
        );
    
        setOverlayStyle({ 
          opacity: 1, 
          backgroundColor: '#e8e8e8',
          pointerEvents: 'auto', 
          zIndex: 1000 
        });
        setTimeout(() => {
          const newPath = `/work/${projectId}`;
          window.history.pushState({ type: 'project', projectId }, project.name, newPath);
          setCurrentView({ type: 'project', projectId });
        }, FADE_DURATION);
      }
    }
  }, []); 



  // Modified handleBackToMainFromProject function
  const handleBackToMainFromProject = useCallback(() => {
    setOverlayStyle({ 
      opacity: 1, 
      backgroundColor: '#e8e8e8',
      pointerEvents: 'auto', 
      zIndex: 1000 
    });

    setTimeout(() => {
      // Set state to return to main view with the work section active
      setCurrentView({ type: 'main', projectId: null, section: 'work' });
      setActiveSection('work');
      
      // Update the history state and URL to the root path
      window.history.pushState(
        { type: 'main', section: 'work', scrollTop: workSectionPathRef.current.scrollTop },
        '',
        '/' // Always go to root URL
      );
      
      setTimeout(() => {
        setOverlayStyle(prev => ({ ...prev, opacity: 0 }));
        
        setTimeout(() => { 
          setOverlayStyle({ 
            opacity: 0, 
            backgroundColor: '#e8e8e8',
            pointerEvents: 'none', 
            zIndex: -1 
          });
          
          // After transition is complete, scroll to the saved position
          if (mainLenisRef.current) {
            // Use the stored scroll position directly instead of scrolling to the work section
            mainLenisRef.current.scrollTo(workSectionPathRef.current.scrollTop, { 
              immediate: true // Use immediate to avoid animation that might override our position
            });
          }
        }, FADE_DURATION);
      }, 100);
    }, FADE_DURATION);
  }, []);



  const handleProjectPageReady = useCallback(() => {
    setTimeout(() => {
      setOverlayStyle(prev => ({ ...prev, opacity: 0 }));
      setTimeout(() => { 
        setOverlayStyle({ 
          opacity: 0, 
          backgroundColor: '#e8e8e8',
          pointerEvents: 'none', 
          zIndex: -1 
        });
      }, FADE_DURATION);
    }, 50);
  }, []);

  const handleNavClick = useCallback((sectionId) => {
    if (sectionId === 'mission') {
      // Store current scroll position
      if (mainLenisRef.current) {
        mainScrollStateRef.current.scrollTop = mainLenisRef.current.scroll;
      }
      
      // Add transition overlay
      setOverlayStyle({ 
        opacity: 1, 
        backgroundColor: '#e8e8e8',
        pointerEvents: 'auto', 
        zIndex: 1000 
      });
      
      // Navigate to About page
      setTimeout(() => {
        const newPath = `/about`;
        window.history.pushState({ type: 'about' }, "About", newPath);
        setCurrentView({ type: 'about' });
      }, FADE_DURATION);
      
      return;
    }

    if (currentView.type !== 'main' || !mainLenisRef.current) return;
    const sectionElement = sectionsRef.current[sectionId];
    if (sectionElement) {
      mainLenisRef.current.scrollTo(sectionElement, {
        offset: 0, 
        duration: 2.5, 
        easing: (t) => t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2,
      });
      // Update history and currentView.section when a nav item is clicked
      window.history.pushState(
        { type: 'main', section: sectionId, scrollTop: 0 }, 
        '', 
        `/${sectionId === 'home' ? '' : sectionId}`
      );
      setCurrentView(prev => ({ ...prev, section: sectionId })); // Ensure currentView.section updates
      setActiveSection(sectionId); // Also keep activeSection in sync
    }
  }, [currentView.type]); // Dependencies for nav click


  // Add a handler for returning from the About page
  const handleBackFromAbout = useCallback(() => {
    setOverlayStyle({ 
      opacity: 1, 
      backgroundColor: '#e8e8e8',
      pointerEvents: 'auto', 
      zIndex: 1000 
    });

    setTimeout(() => {
      setCurrentView({ type: 'main', projectId: null, section: 'home' });
      setActiveSection('home');
      
      window.history.pushState(
        { type: 'main', section: 'home', scrollTop: 0 },
        '',
        '/'
      );
      
      setTimeout(() => {
        setOverlayStyle(prev => ({ ...prev, opacity: 0 }));
        
        setTimeout(() => { 
          setOverlayStyle({ 
            opacity: 0, 
            backgroundColor: '#e8e8e8',
            pointerEvents: 'none', 
            zIndex: -1 
          });
        }, FADE_DURATION);
      }, 100);
    }, FADE_DURATION);
  }, []);

  // Add a handler for when the About page is ready
  const handleAboutPageReady = useCallback(() => {
    setTimeout(() => {
      setOverlayStyle(prev => ({ ...prev, opacity: 0 }));
      setTimeout(() => { 
        setOverlayStyle({ 
          opacity: 0, 
          backgroundColor: '#e8e8e8',
          pointerEvents: 'none', 
          zIndex: -1 
        });
      }, FADE_DURATION);
    }, 50);
  }, []);

  // Add a transition state to handle black fade transitions
  const [pageTransition, setPageTransition] = useState({
    active: false,
    from: '',
    to: '',
    color: '#000000'
  });
  
  // Handle navigation from About to Services
  const handleNavigateToServices = useCallback(() => {
    // Start the black fade out
    setPageTransition({
      active: true,
      from: 'about',
      to: 'services',
      color: '#000000'
    });
    
    // After fade out completes, change the view
    setTimeout(() => {
      const newPath = `/services`;
      window.history.pushState({ type: 'services' }, "Services", newPath);
      setCurrentView({ type: 'services' });
      
      // Start fade in after a short delay
      setTimeout(() => {
        setPageTransition(prev => ({ ...prev, active: false }));
      }, 500);
    }, 800); // Match your fade duration
  }, []);

  const handleBackFromServices = useCallback(() => {
  // Start the black fade out transition
  setPageTransition({
    active: true,
    from: 'services',
    to: 'about',
    color: '#000000'
  });
  
    // After fade out completes, change the view to About
    setTimeout(() => {
      const newPath = `/about`;
      window.history.pushState({ type: 'about' }, "About", newPath);
      setCurrentView({ type: 'about' });
      
      // Start fade in after a short delay
      setTimeout(() => {
        setPageTransition(prev => ({ ...prev, active: false }));
      }, 500);
    }, 800); // Match your fade duration
  }, []);

  // Handle ready event from Services page
  const handleServicesPageReady = useCallback(() => {
    setTimeout(() => {
      setOverlayStyle(prev => ({ ...prev, opacity: 0 }));
      setTimeout(() => { 
        setOverlayStyle({ 
          opacity: 0, 
          backgroundColor: '#e8e8e8',
          pointerEvents: 'none', 
          zIndex: -1 
        });
      }, FADE_DURATION);
    }, 50);
  }, []);

  // Add a page transition overlay for the black fade effects
  const pageTransitionStyle = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: pageTransition.color,
    opacity: pageTransition.active ? 1 : 0,
    pointerEvents: pageTransition.active ? 'auto' : 'none',
    zIndex: 9999,
    transition: 'opacity 800ms ease-in-out',
  };


  // Handle navigation from Services to Steps
  const handleNavigateToSteps = useCallback(() => {
    // Start the black fade out
    setPageTransition({
      active: true,
      from: 'services',
      to: 'steps',
      color: '#000000'
    });
    
    // After fade out completes, change the view
    setTimeout(() => {
      const newPath = `/steps`;
      window.history.pushState({ type: 'steps' }, "Steps", newPath);
      setCurrentView({ type: 'steps' });
      
      // Start fade in after a short delay
      setTimeout(() => {
        setPageTransition(prev => ({ ...prev, active: false }));
      }, 500);
    }, 800); // Match your fade duration
  }, []);
  
  // Handle back navigation from Steps to Services
  const handleBackFromSteps = useCallback(() => {
    // Start the black fade out transition
    setPageTransition({
      active: true,
      from: 'steps',
      to: 'services',
      color: '#000000'
    });
    
    // After fade out completes, change the view to Services
    setTimeout(() => {
      const newPath = `/services`;
      window.history.pushState({ type: 'services' }, "Services", newPath);
      setCurrentView({ type: 'services' });
      
      // Start fade in after a short delay
      setTimeout(() => {
        setPageTransition(prev => ({ ...prev, active: false }));
      }, 500);
    }, 800); // Match your fade duration
  }, []);
  
  // Handle ready event from Steps page
  const handleStepsPageReady = useCallback(() => {
    setTimeout(() => {
      setOverlayStyle(prev => ({ ...prev, opacity: 0 }));
      setTimeout(() => { 
        setOverlayStyle({ 
          opacity: 0, 
          backgroundColor: '#e8e8e8',
          pointerEvents: 'none', 
          zIndex: -1 
        });
      }, FADE_DURATION);
    }, 50);
  }, []);


  // Handle navigation from Steps to Contact
  const handleNavigateToContact = useCallback(() => {
    // Start the black fade out
    setPageTransition({
      active: true,
      from: 'steps',
      to: 'contact',
      color: '#000000'
    });
    
    // After fade out completes, change the view
    setTimeout(() => {
      const newPath = `/contact`;
      window.history.pushState({ type: 'contact' }, "Contact", newPath);
      setCurrentView({ type: 'contact' });
      
      // Start fade in after a short delay
      setTimeout(() => {
        setPageTransition(prev => ({ ...prev, active: false }));
      }, 500);
    }, 800); // Match your fade duration
  }, []);
  
  // Handle back navigation from Contact to Steps
  const handleBackFromContact = useCallback(() => {
    // Start the black fade out transition
    setPageTransition({
      active: true,
      from: 'contact',
      to: 'steps',
      color: '#000000'
    });
    
    // After fade out completes, change the view to Steps
    setTimeout(() => {
      const newPath = `/steps`;
      window.history.pushState({ type: 'steps' }, "Steps", newPath);
      setCurrentView({ type: 'steps' });
      
      // Start fade in after a short delay
      setTimeout(() => {
        setPageTransition(prev => ({ ...prev, active: false }));
      }, 500);
    }, 800); // Match your fade duration
  }, []);
  
  // Handle Back to Home navigation from Contact
  const handleBackToHomeFromContact = useCallback(() => {
    // Start the black fade out transition
    setPageTransition({
      active: true,
      from: 'contact',
      to: 'main',
      color: '#000000'
    });
    
    // After fade out completes, change the view to main
    setTimeout(() => {
      setCurrentView({ type: 'main', projectId: null, section: 'home' });
      setActiveSection('home');
      
      window.history.pushState(
        { type: 'main', section: 'home', scrollTop: 0 },
        '',
        '/'
      );
      
      // Start fade in after a short delay
      setTimeout(() => {
        setPageTransition(prev => ({ ...prev, active: false }));
      }, 500);
    }, 800); // Match your fade duration
  }, []);
  
  // Handle ready event from Contact page
  const handleContactPageReady = useCallback(() => {
    setTimeout(() => {
      setOverlayStyle(prev => ({ ...prev, opacity: 0 }));
      setTimeout(() => { 
        setOverlayStyle({ 
          opacity: 0, 
          backgroundColor: '#e8e8e8',
          pointerEvents: 'none', 
          zIndex: -1 
        });
      }, FADE_DURATION);
    }, 50);
  }, []);

  const handlePointerEventPassthrough = useCallback((e) => {
    if (currentView.type !== 'main') return; 
    const canvasElement = document.querySelector('.canvas-container canvas');
    if (canvasElement) {
      const event = new PointerEvent(e.type, { clientX: e.clientX, clientY: e.clientY, bubbles: true });
      canvasElement.dispatchEvent(event);
    }
  }, [currentView.type]);

  // Get fluid color based on dark mode state - similar to ProductPage.js
  const getFluidColor = () => {
    return isDarkMode ? '#e8e8e8' : '#3b3b3b';
  };

  return (
    <div className="app-container">
      <div style={pageTransitionStyle}></div>
      {/* Add the About navigation button */}
      <button 
        className="nav-button"
        onClick={() => handleNavClick('mission')}
      >
        About
        <img 
          src="/scrolldownpfeil.svg" 
          alt="Arrow" 
          className="arrow-icon" 
        />
      </button>
      
      <div className="canvas-preloader" style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: '#e8e8e8',
        zIndex: 0
      }}></div>

      <div className="canvas-container" style={{ 
        display: currentView.type === 'about' || currentView.type === 'services' || currentView.type === 'steps' || currentView.type === 'contact' ? 'none' : 'block' 
      }}>
        <Canvas 
          camera={{ position: [0, 0, 5], fov: 45 }}
          gl={{ alpha: true, antialias: true, powerPreference: 'high-performance', clearColor: [0.91, 0.91, 0.91, 1] }}
          style={{ pointerEvents: 'auto' }} 
          key={currentView.type} 
        >
          <Suspense fallback={null}>
            <Background /> 
            <ambientLight intensity={0.5} />
            <pointLight position={[5, 5, 5]} intensity={1} />
            <OverlayNoiseFilter /> 
            <>
              <EffectComposer>
                <Fluid 
                  radius={0.36} curl={3} swirl={2} distortion={1} force={1} pressure={0.10} 
                  densityDissipation={0.95} velocityDissipation={1.00} intensity={4.45} 
                  rainbow={false} blend={5} showBackground={true} 
                  backgroundColor='transparent' 
                  fluidColor={getFluidColor()} 
                />
              </EffectComposer>
              <AutomatedFluidEffect />
            </>
          </Suspense>
        </Canvas>
      </div>

      <div className="transition-overlay" style={overlayStyle}></div>

      <div 
        className={`main-content-wrapper ${currentView.type === 'main' && overlayStyle.opacity === 0 ? 'fadein' : 'fadeout'}`}
        style={{ 
          zIndex: currentView.type === 'main' ? 5 : 1,
          display: currentView.type === 'about' || currentView.type === 'services' || currentView.type === 'steps' || currentView.type === 'contact' ? 'none' : 'block'
        }}
      >
        {currentView.type === 'main' && (
          <>
            <div 
              className="content-container" 
              ref={scrollContainerRef}
              onPointerMove={handlePointerEventPassthrough} 
            >
              <div className="all-sections-content">
                 <section id="home" className={activeSection === "home" ? "active" : ""} data-scroll-section>
                  <div className="section-content home-content">
                    <div className="home-row-container">
                      <img src="/blcks_black_short.svg" alt="BLCKS Logo" className="home-logo" />
                      <h1 className="home-heading">Creative AI & Design Studio</h1>
                      <img src="/scrolldownpfeil.svg" alt="Scroll Down" className="scroll-arrow" />
                      <p className="scroll-text">SCROLL DOWN</p>
                    </div>
                  </div>
                </section>
                <section id="slogan" className={activeSection === "slogan" ? "active" : ""} data-scroll-section>
                  <div className="section-content slogan-content">
                    <div className="slogan-text-container">
                      <AnimatedText 
                        effect="randomLetters" 
                        threshold={0.2}  // Lower threshold to trigger earlier
                        duration={3}
                        staggerAmount={0.3}
                        outDuration={0.7}
                        outStaggerAmount={0.4}
                      >
                        Wir verwandeln Ideen mit modernster Technologie in intelligente, interaktive und visuell beeindruckende Erlebnisse.
                      </AnimatedText>
                    </div>
                  </div>
                </section>
                <section id="showreel" className={activeSection === "showreel" ? "active" : ""} data-scroll-section>
                  <div className="section-content showreel-content">
                    <div className="showreel-text-container">
                      <video 
                        ref={showreelVideoRef}
                        src="https://files.blcks.at/hero_showreel.mp4" 
                        className="showreel-video" 
                        loop 
                        muted 
                        playsInline
                      ></video>
                    </div>
                  </div>
                </section>
                <section id="mission" className={activeSection === "mission" ? "active" : ""} data-scroll-section>
                  <div className="section-content mission-content">
                    <div className="mission-text-container">
                      <h2>OUR APPROACH</h2>
                      <div className="animated-mission-text"> {/* Wrapper div with our custom class */}
                        <AnimatedText 
                          effect="revealLetters" 
                          threshold={0.2}
                          duration={3}
                          staggerAmount={0.3}
                          outDuration={0.7}
                          outStaggerAmount={0.4}
                          elementType="p" // This is the key change - use p instead of h2
                          className="mission-description" // Use the same class as your static paragraph
                        >
                          Wir setzen auf eine neue Art der digitalen Transformation. Eine, die sich an dir, deiner Marke und deiner Zielgruppe orientiert, um nachhaltige, personalisierte Erlebnisse zu schaffen.
                        </AnimatedText>
                      </div>
                    </div>
                  </div>
                </section>
                <section id="services" className={activeSection === "services" ? "active" : ""} data-scroll-section>
                  <div className="section-content services-content">
                    <div className="services-text-container"><h2 className="section-title">SERVICES</h2><h2 className="services-big-heading">WEBENTWICKLUNG · 3D ERLEBNISSE · UI/UX DESIGN · SEO · E-COMMERCE · KÜNSTLICHE INTELLIGENZ · SUPPORT</h2></div>
                  </div>
                </section>
                <section id="work" className={activeSection === "work" ? "active" : ""} data-scroll-section>
                  <div className="project-gallery" id="itemsWrapper">
                    {Object.values(projectsData).map((project, index) => (
                      <div 
                        className={`gallery-item ${index % 2 === 0 ? 'right-aligned' : 'center-left'}`} 
                        key={project.id} 
                        onClick={() => handleProjectClick(project.id)}
                      >
                        <div className="project-image" data-project-id={project.id}>
                          <div className="project-heading">
                            <div className="project-info-left">
                              <h2>{project.name}</h2>
                              <div className="project-category">{project.category}</div>
                            </div>
                            {project.id !== 'victorinox' && (
                              <div className="view-project">VIEW PROJECT</div>
                            )}
                          </div>
                          {project.id === 'victorinox' ? (
                            <video 
                              ref={victorinoxVideoRef}
                              src="https://files.blcks.at/sphere.mp4" 
                              loop 
                              muted 
                              playsInline
                              style={{ width: '100%', height: '100%', objectFit: 'cover', position: 'absolute', top: 0, left: 0 }}
                            />
                          ) : (
                            <img src={project.heroImage} alt={`${project.name} Project`} />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </section>
                
                {/* Contact Section without overlay */}
                <section id="contact" className={`contact-section ${activeSection === "contact" ? "active" : ""}`} data-scroll-section ref={contactRef}>              
                  <div className="contact-content">
                    {/* Big Contact Email Heading in Center */}
                    <div className="contact-email-container">
                      <a href="mailto:<EMAIL>" className="contact-email-link">
                        <h2 className="contact-email-heading"><EMAIL></h2>
                      </a>
                    </div>
                    
                    {/* Bottom Bar with Copyright and Built By */}
                    <div className="contact-bottom">
                      <div className="contact-copyright">All rights reserved. © 2025 BLCKs</div>
                      <div className="contact-built-by">built by BLCKs with ❤</div>

                      {/* New footer section with 3 elements positioned as requested */}
                      <div className="footer-section">
                        {/* Footer links on the left */}
                        <div className="footer-links-container">
                          <div className="footer-links">
                            <a href="https://sponge-respect-ef4.notion.site/BLCKS-IMPRESSUM-2096ae94edc480e59febf43f19603ced?pvs=74" className="footer-link" target="_blank" rel="noopener noreferrer">Impressum</a>
                            <a href="https://sponge-respect-ef4.notion.site/BLCKS-DATENSCHUTZERKL-RUNG-2096ae94edc480e48be4c98184dca0f2?pvs=73" className="footer-link" target="_blank" rel="noopener noreferrer">Datenschutz</a>
                            <a href="https://sponge-respect-ef4.notion.site/BLCKS-COOKIE-RICHTLINIE-2096ae94edc480568784effb4d4c23b8?pvs=74" className="footer-link" target="_blank" rel="noopener noreferrer">Cookies</a>
                          </div>
                        </div>
                        
                        {/* Social icons in the middle */}
                        <div className="social-icons">
                          <a href="#" className="social-icon">
                            <img src="/instagram.svg" alt="Instagram" />
                          </a>
                          <a href="https://www.linkedin.com/in/philippfuchs-blcks" className="social-icon" target="_blank" rel="noopener noreferrer">
                            <img src="/linkedin.svg" alt="LinkedIn" />
                          </a>
                          <a href="#" className="social-icon">
                            <img src="/twitter.svg" alt="Twitter" />
                          </a>
                        </div>

                        {/* QR Code section on the right */}
                        <div className="qr-code-container">
                          <img src="/qr-code.svg" alt="WhatsApp QR Code" className="qr-code-image" />
                          <h3 className="qr-code-heading">WhatsApp Coming</h3>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Add the About view wrapper */}
      <div 
        className={`about-view-wrapper ${currentView.type === 'about' && overlayStyle.opacity === 0 ? 'fadein' : 'fadeout'}`}
        style={{ zIndex: currentView.type === 'about' ? 10 : 1 }}
      >
        {currentView.type === 'about' && (
          <About 
            onBack={handleBackFromAbout}
            onReady={handleAboutPageReady}
            onNext={handleNavigateToServices}
          />
        )}
      </div>

      {/* Services view wrapper */}
      <div 
        className={`services-view-wrapper ${currentView.type === 'services' && overlayStyle.opacity === 0 ? 'fadein' : 'fadeout'}`}
        style={{ zIndex: currentView.type === 'services' ? 10 : 1 }}
      >
        {currentView.type === 'services' && (
          <Services 
            onBack={handleBackFromServices}
            onReady={handleServicesPageReady}
            onNext={handleNavigateToSteps}
          />
        )}
      </div>

      {/* Steps view wrapper */}
      <div 
        className={`steps-view-wrapper ${currentView.type === 'steps' && overlayStyle.opacity === 0 ? 'fadein' : 'fadeout'}`}
        style={{ zIndex: currentView.type === 'steps' ? 10 : 1 }}
      >
        {currentView.type === 'steps' && (
          <Steps 
            onBack={handleBackFromSteps}
            onReady={handleStepsPageReady}
            onNext={handleNavigateToContact}
          />
        )}
      </div>

      {/* Contact view wrapper */}
      <div 
        className={`contact-view-wrapper ${currentView.type === 'contact' && overlayStyle.opacity === 0 ? 'fadein' : 'fadeout'}`}
        style={{ zIndex: currentView.type === 'contact' ? 10 : 1 }}
      >
        {currentView.type === 'contact' && (
          <Contact 
            onBack={handleBackFromContact}
            onReady={handleContactPageReady}
            onBackToHome={handleBackToHomeFromContact}
          />
        )}
      </div>

      <div 
        className={`project-view-wrapper ${currentView.type === 'project' && overlayStyle.opacity === 0 ? 'fadein' : 'fadeout'}`}
        style={{ zIndex: currentView.type === 'project' ? 10 : 1 }}
      >
        {currentView.type === 'project' && currentView.projectId && (
          <ProjectPage 
            projectId={currentView.projectId} 
            onBack={handleBackToMainFromProject}
            onReady={handleProjectPageReady}
            projectsData={projectsData} 
          />
        )}
      </div>
    </div>
  );
}

export default App;