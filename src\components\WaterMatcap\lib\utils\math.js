export const lerp = (start, end, t) => {
  return start * (1 - t) + end * t;
};

export const valueRemap = (
  value,
  inMin,
  inMax,
  outMin,
  outMax
) => {
  return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;
};

export const ruleOfThree = (a, b, c) => (b * c) / a;

export const degToRad = (angle) => (angle * Math.PI) / 180;

export const secToMs = (sec) => sec * 1000;

export const msToSec = (ms) => ms / 1000;

export const mod = (n, m) => ((n % m) + m) % m;

export const clamp = (min, max, value) =>
  Math.min(Math.max(value, min), max);

export const round = (value, decimals) =>
  Number(value.toFixed(decimals));

export const hexToRgb = (hex) => {
  const match = hex.replace(/#/, "").match(/.{1,2}/g);
  if (!match) return;
  /* check three components */
  if (!match[0] || !match[1] || !match[2]) {
    throw new Error("Invalid hex color");
  }
  const r = parseInt(match[0], 16);
  const g = parseInt(match[1], 16);
  const b = parseInt(match[2], 16);
  return { r, g, b };
};

export const mix = (a, b, t) => {
  return a * (1 - t) + b * t;
};

/** highp smoothstep */
export const smoothstep = (edge0, edge1, x) => {
  const denom = edge1 - edge0;
  if (Math.abs(denom) < 1e-6) return 0.5;

  const t = Math.max(0, Math.min(1, (x - edge0) / denom));
  return t * t * (3 - 2 * t);
};