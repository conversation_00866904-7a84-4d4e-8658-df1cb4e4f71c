import React from 'react'
import { MeshStandardMaterial } from 'three'
import * as THREE from 'three'
import { useGLTF } from '@react-three/drei'

  // Create materials with refs to update opacity later
  const whiteMaterial = new MeshStandardMaterial({
    color: '#fff',
    emissive: '#fff',
    roughness: 0.3,
    metalness: 0.8,
    emissiveIntensity: 2,
  })

export default function Model(props) {
  const { nodes, materials } = useGLTF('/blcks_logo_ext_1.glb')
  return (
    <group {...props} dispose={null}>
      <group scale={0.01}>
        <mesh geometry={nodes.Shape_0.geometry} material={whiteMaterial} position={[-804.847, 1.296, 0]} />
        <mesh geometry={nodes.Shape_0001.geometry} material={whiteMaterial} position={[-473.307, -35.007, 0.01]} />
        <mesh geometry={nodes.Shape_0002.geometry} material={whiteMaterial} position={[-48.374, -1.69, 0.02]} />
        <mesh geometry={nodes.Shape_0003.geometry} material={whiteMaterial} position={[432.566, -9.957, 0.03]} />
        <mesh geometry={nodes.Shape_0004.geometry} material={whiteMaterial} position={[901.199, 1.407, 0.04]} />
        <mesh geometry={nodes.Shape_1.geometry} material={whiteMaterial} position={[525.682, -273.682, 0.01]} />
        <mesh geometry={nodes.Shape_1001.geometry} material={whiteMaterial} position={[558.131, -270.446, 0.02]} />
        <mesh geometry={nodes.Shape_1002.geometry} material={whiteMaterial} position={[559.101, -281.708, 0.03]} />
        <mesh geometry={nodes.Shape_1003.geometry} material={whiteMaterial} position={[580.201, -273.482, 0.04]} />
        <mesh geometry={nodes.Shape_1004.geometry} material={whiteMaterial} position={[580.111, -273.68, 0.05]} />
        <mesh geometry={nodes.Shape_1005.geometry} material={whiteMaterial} position={[603.807, -273.35, 0.06]} />
        <mesh geometry={nodes.Shape_1006.geometry} material={whiteMaterial} position={[603.461, -278.612, 0.07]} />
        <mesh geometry={nodes.Shape_1007.geometry} material={whiteMaterial} position={[626.886, -269.36, 0.08]} />
        <mesh geometry={nodes.Shape_1008.geometry} material={whiteMaterial} position={[643.436, -273.68, 0.09]} />
        <mesh geometry={nodes.Shape_1009.geometry} material={whiteMaterial} position={[662.249, -273.902, 0.1]} />
        <mesh geometry={nodes.Shape_1010.geometry} material={whiteMaterial} position={[688.696, -273.482, 0.11]} />
        <mesh geometry={nodes.Shape_1011.geometry} material={whiteMaterial} position={[688.609, -273.68, 0.12]} />
        <mesh geometry={nodes.Shape_1012.geometry} material={whiteMaterial} position={[722.247, -273.35, 0.13]} />
        <mesh geometry={nodes.Shape_1013.geometry} material={whiteMaterial} position={[721.901, -278.612, 0.14]} />
        <mesh geometry={nodes.Shape_1014.geometry} material={whiteMaterial} position={[741.486, -273.68, 0.15]} />
        <mesh geometry={nodes.Shape_1015.geometry} material={whiteMaterial} position={[767.992, -272.864, 0.16]} />
        <mesh geometry={nodes.Shape_1016.geometry} material={whiteMaterial} position={[813.818, -273.678, 0.17]} />
        <mesh geometry={nodes.Shape_1017.geometry} material={whiteMaterial} position={[836.281, -273.482, 0.18]} />
        <mesh geometry={nodes.Shape_1018.geometry} material={whiteMaterial} position={[836.191, -273.68, 0.19]} />
        <mesh geometry={nodes.Shape_1019.geometry} material={whiteMaterial} position={[857.895, -273.674, 0.2]} />
        <mesh geometry={nodes.Shape_1020.geometry} material={whiteMaterial} position={[874.796, -273.68, 0.21]} />
        <mesh geometry={nodes.Shape_1021.geometry} material={whiteMaterial} position={[892.561, -273.719, 0.22]} />
        <mesh geometry={nodes.Shape_1022.geometry} material={whiteMaterial} position={[903.371, -278.261, 0.23]} />
        <mesh geometry={nodes.Shape_1023.geometry} material={whiteMaterial} position={[922.956, -273.68, 0.24]} />
        <mesh geometry={nodes.Shape_1024.geometry} material={whiteMaterial} position={[958.933, -273.674, 0.25]} />
        <mesh geometry={nodes.Shape_1025.geometry} material={whiteMaterial} position={[980.596, -269.36, 0.26]} />
        <mesh geometry={nodes.Shape_1026.geometry} material={whiteMaterial} position={[1005.18, -280.541, 0.27]} />
        <mesh geometry={nodes.Shape_1027.geometry} material={whiteMaterial} position={[1040.61, -273.678, 0.28]} />
        <mesh geometry={nodes.Shape_1028.geometry} material={whiteMaterial} position={[1054.836, -273.68, 0.29]} />
        <mesh geometry={nodes.Shape_1029.geometry} material={whiteMaterial} position={[1075.76, -273.686, 0.3]} />
        <mesh geometry={nodes.Shape_2.geometry} material={whiteMaterial} position={[1137.094, 185.283, 0.02]} />
        <mesh geometry={nodes.Shape_3.geometry} material={whiteMaterial} position={[1129.704, 179.09, 0.03]} />
      </group>
    </group>
  )
}

useGLTF.preload('/blcks_logo_ext_1.glb')
