.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(20, 20, 20, 0.75);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32px;
  padding: 0;
  box-sizing: border-box;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

.closeButton {
  position: absolute;
  top: 32px;
  right: 32px;
  background: none;
  border: none;
  color: #666;
  font-size: 15px;
  font-weight: 400;
  letter-spacing: 2px;
  padding: 12px 32px;
  cursor: pointer;
  outline: none;
  z-index: 1002;
  transition: color 0.2s;
}
.closeButton:hover {
  color: #fff;
}

.content {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.grid {
  position: absolute;
  width: 120%;
  height: 120%;
  top: -10%;
  left: -10%;
  display: grid;
  grid-template-columns: repeat(50,2%);
  grid-template-rows: repeat(50,2%);
  perspective: 1000px;
}

.grid--inactive {
  pointer-events: none;
}

.grid__item {
  position: relative;
  will-change: transform;
  cursor: pointer;
  z-index: 2;
}

.grid__item_img {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: 50% 50%;
  border-radius: 10px;
  will-change: transform;
  transition: box-shadow 0.2s;
  box-shadow: 0 4px 24px rgba(0,0,0,0.18);
}

.grid__item_img:hover {
  box-shadow: 0 8px 32px rgba(0,0,0,0.28);
}

/* Grid positions (Codrops original) */
.pos-1 { grid-area: 10 / 1 / span 12 / span 5; }
.pos-2 { grid-area: 1 / 18 / span 12 / span 5; }
.pos-3 { grid-area: 1 / 29 / span 12 / span 5; }
.pos-4 { grid-area: 15 / 12 / span 12 / span 5; }
.pos-5 { grid-area: 17 / 25 / span 12 / span 5; }
.pos-6 { grid-area: 20 / 43 / span 12 / span 5; }
.pos-7 { grid-area: 35 / 5 / span 12 / span 5; }
.pos-8 { grid-area: 40 / 14 / span 12 / span 5; }
.pos-9 { grid-area: 37 / 29 / span 12 / span 5; }
.pos-10 { grid-area: 35 / 42 / span 12 / span 5; }
.pos-11 { grid-area: 1 / 44 / span 12 / span 5; }
.pos-12 { grid-area: 32 / 20 / span 12 / span 5; }
.pos-13 { grid-area: 22 / 2 / span 12 / span 5; }
.pos-14 { grid-area: 9 / 38 / span 12 / span 5; }
.pos-15 { grid-area: 4 / 7 / span 12 / span 5; }
.pos-16 { grid-area: 28 / 36 / span 12 / span 5; }

.preview_overlay {
  position: absolute;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  background: rgba(30, 30, 30, 0.98);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview__item {
  position: relative;
  margin: 0 auto;
  padding: 0 5vw;
  display: grid;
  grid-template-rows: auto auto auto;
  grid-template-columns: 50% 50%;
  grid-template-areas:
    'preview-back preview-back'
    'preview-img preview-title'
    'preview-content preview-content';
  width: 80vw;
  max-width: 1100px;
  min-height: 440px;
  background: none;
  pointer-events: auto;
  perspective: 1000px;
}

.preview__item--open {
  pointer-events: auto;
}

.preview__item_back {
  grid-area: preview-back;
  justify-self: start;
  font-size: 0.857rem;
  text-transform: uppercase;
  font-weight: 500;
  padding-left: 90px;
  margin-bottom: 1rem;
  background: none;
  color: #fff;
  border: none;
  cursor: pointer;
  outline: none;
  z-index: 1003;
  transition: color 0.2s;
}

.preview__item_back:hover {
  color: #E3154D;
}

.preview__item_imgwrap {
  width: 100px;
  height: 150px;
  border-radius: 10px;
  overflow: hidden;
  grid-area: preview-img;
  transform-origin: 50% 100%;
  box-shadow: 0 4px 32px rgba(0,0,0,0.22);
}

.preview__item_img {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: 50% 50%;
}

.preview__item_title {
  grid-area: preview-title;
  font-size: clamp(1.5rem,12vw,20vh);
  margin: 0;
  grid-column-start: 1;
  align-self: center;
  padding-left: 1rem;
  position: relative;
  overflow: hidden;
  color: #fff;
  text-align: left;
  font-weight: 700;
  letter-spacing: 0.02em;
  pointer-events: none;
  display: flex;
  flex-wrap: wrap;
}

.char {
  display: inline-block;
  will-change: transform;
}

.preview__item_content {
  grid-area: preview-content;
  max-width: calc(1100px - 80vh);
  min-height: 220px;
  line-height: 1.3;
  color: #eee;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding-top: 1rem;
}

.preview__item_meta {
  color: #bbb;
  text-transform: uppercase;
  font-size: 0.857rem;
  padding: 1rem 0 0;
  display: flex;
  gap: 16px;
}

.preview__item_meta span {
  display: block;
}

.preview__item_description {
  font-size: 1.1rem;
  max-width: 400px;
  text-align: left;
  color: #ddd;
  margin: 1rem 0;
}

.preview__item_info {
  display: block;
  color: #E3154D;
  margin: 1rem 0;
  background: none;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  padding: 0;
}

.preview__item_button {
  color: #fff;
  border: 0;
  border-radius: 2rem;
  text-transform: uppercase;
  font: inherit;
  font-size: 0.875rem;
  font-weight: 500;
  background: #000;
  padding: 1rem 2rem;
  display: inline-block;
  cursor: pointer;
  margin-bottom: 1rem;
  transition: background 0.2s;
}

.preview__item_button:hover,
.preview__item_button:active,
.preview__item_button:focus {
  outline: none;
  background: #E3154D;
}

@media screen and (min-width: 53em) {
  .content {
    height: 100%;
  }
  .preview__item {
    height: 100%;
    width: 80vw;
    grid-template-columns: 47% 53%;
    grid-template-rows: minmax(max(5rem, 18vh), 1fr) auto auto 1fr;
    grid-template-areas:
      '... ...'
      'preview-back ...'
      'preview-title preview-title'
      'preview-img preview-content';
  }
  .preview__item_title {
    justify-self: center;
    padding: 0;
  }
  .preview__item_meta {
    padding: 1rem 0;
  }
  .preview__item_info {
    margin: 1rem 0 3rem;
  }
  .preview__item_imgwrap {
    width: 100%;
    height: 100%;
    grid-row-start: 3;
    border-radius: 10px 10px 0 0;
  }
  .preview__item_content {
    padding: 0 0 0 4rem;
  }
}

@media (max-width: 900px) {
  .content {
    flex-direction: column;
    width: 98vw;
    height: 98vh;
  }
  .grid {
    width: 90vw;
    height: 40vh;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 16px;
  }
  .preview__item_imgwrap {
    width: 220px;
    height: 220px;
  }
}

.centerTextSection {
  position: static;
  top: unset;
  left: unset;
  transform: none;
  z-index: auto;
  background: none;
  padding: 1.5rem 0 1.2rem 0;
  border-radius: 0;
  box-shadow: none;
  max-width: 700px;
  width: 100%;
  text-align: center;
  color: #fff;
  pointer-events: auto;
  margin: 1.5rem auto 1.5rem auto;
  display: block;
}

.centerHeading {
  font-size: 1.45rem;
  font-weight: 700;
  margin-bottom: 0.8rem;
  color: #fff;
  letter-spacing: 0.01em;
}

.centerHeading2 {
  font-size: 1.08rem;
  font-weight: 600;
  margin: 1.5rem 0 0.7rem 0;
  color: #fff;
  letter-spacing: 0.01em;
}

.centerText {
  font-size: 0.98rem;
  color: #eaeaea;
  margin-bottom: 0.7rem;
  line-height: 1.5;
}

.centerList {
  text-align: left;
  margin: 0 auto 0.8rem auto;
  padding-left: 1.2em;
  color: #eaeaea;
  font-size: 0.97rem;
}

.centerList li {
  margin-bottom: 0.35em;
  line-height: 1.4;
}

@media (max-width: 700px) {
  .centerTextSection {
    padding: 0.7rem 0 0.7rem 0;
    max-width: 98vw;
    font-size: 0.92rem;
    margin: 0.7rem auto 0.7rem auto;
  }
  .centerHeading {
    font-size: 1.08rem;
  }
  .centerHeading2 {
    font-size: 0.97rem;
  }
  .centerText {
    font-size: 0.92rem;
  }
  .centerList {
    font-size: 0.92rem;
  }
}

.gridHelperText {
  position: absolute;
  top: 6px;
  left: 8px;
  z-index: 10;
  background: rgba(0,0,0,0.38);
  color: #fff;
  font-size: 0.82rem;
  font-family: monospace, monospace;
  padding: 2px 7px;
  border-radius: 6px;
  pointer-events: none;
  user-select: none;
  opacity: 0.85;
  letter-spacing: 0.01em;
}

.gridHelperOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 120%;
  height: 120%;
  pointer-events: none;
  z-index: 100;
  user-select: none;
}
.gridHelperCols {
  position: absolute;
  top: -18px;
  left: 0;
  width: 100%;
  height: 0;
  pointer-events: none;
}
.gridHelperColNum {
  position: absolute;
  top: 0;
  font-size: 0.65rem;
  color: #fff;
  opacity: 0.32;
  background: none;
  transform: translateX(-50%);
  pointer-events: none;
}
.gridHelperRows {
  position: absolute;
  left: -22px;
  top: 0;
  width: 0;
  height: 100%;
  pointer-events: none;
}
.gridHelperRowNum {
  position: absolute;
  left: 0;
  font-size: 0.65rem;
  color: #fff;
  opacity: 0.32;
  background: none;
  transform: translateY(-50%);
  pointer-events: none;
}
.gridHelperLines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
} 