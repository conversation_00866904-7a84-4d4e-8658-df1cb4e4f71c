import React from 'react'
import { useGLTF } from '@react-three/drei'
import { MeshStandardMaterial } from 'three'
import * as THREE from 'three'

  // Create materials with refs to update opacity later
  const whiteMaterial = new MeshStandardMaterial({
    color: '#fff',
    emissive: '#fff',
    roughness: 0.3,
    metalness: 0.8,
    side: THREE.DoubleSide,
    emissiveIntensity: 2,
  })


export default function Model(props) {
  const { nodes, materials } = useGLTF('/roman1.glb')
  return (
    <group {...props} dispose={null}>
      <group scale={0.01}>
        <mesh geometry={nodes.Text.geometry} material={whiteMaterial} />
      </group>
    </group>
  )
}

useGLTF.preload('/roman1.glb')
