import { create<PERSON>ortal, use<PERSON><PERSON>e, useThree } from "@react-three/fiber";
import { useControls } from "leva";
import { useEffect, useMemo, useRef, useState } from "react";
import * as THREE from "three";

// import { Cameras, useCameraStore } from "./cameras";
import { FLOW_SIM_SIZE, RAYMARCH_WATER_CENTER, RAYMARCH_FLOW_SIZE } from "./constants";
import { DebugTextures } from "./debug-textures";
import { Env } from "./env";
import { renderFlow } from "./render-flow";
import { useAssets } from "./use-assets";
import { useLerpMouse } from "./use-lerp-mouse";
import { useMaterials } from "./use-materials";
import { useTargets } from "./use-targets";

export function Scene({ scrollProgress = 0, opacity = 1, position = [0,0,0] }) {
  // const activeCamera = useCameraStore((state) => state.camera);
  const { camera } = useThree();

  const [{ debugFloor, renderFloor, debugTextures }] = useControls(() => ({
    debugTextures: false,
    debugFloor: false,
    renderFloor: true
  }));

  const targets = useTargets();
  const { flowFbo, orbeFlowFbo } = targets;
  const assets = useAssets();
  const materials = useMaterials(targets, assets);
  const { flowMaterial, raymarchMaterial, updateFlowCamera } = materials;

  updateFlowCamera(camera);

  const [envMap, setEnvMap] = useState(null);

  // update environment
  useFrame(({ scene }) => {
    const env = scene.environment;
    if (!env) return;
    const currentEnv = raymarchMaterial.uniforms.envMap.value;
    if (currentEnv !== env) {
      raymarchMaterial.uniforms.envMap.value = env;
      const rotation = raymarchMaterial.uniforms.envMapRotation.value;

      const _e1 = new THREE.Euler();
      const _m1 = new THREE.Matrix4();

      _e1.copy(scene.environmentRotation);

      // accommodate left-handed frame
      _e1.x *= -1;
      _e1.y *= -1;
      _e1.z *= -1;

      _e1.y *= -1;
      _e1.z *= -1;

      rotation.setFromMatrix4(_m1.makeRotationFromEuler(_e1));

      setEnvMap(env);
    }
  });

  const [handlePointerMoveFloor, lerpMouseFloor, vRefsFloor] = useLerpMouse();

  const frameCount = useRef(0);

  const screenFbo = useMemo(() => {
    const fbo = new THREE.WebGLRenderTarget(10, 10, {
      depthBuffer: true,
      depthTexture: new THREE.DepthTexture(10, 10)
    });

    if (typeof window !== "undefined") {
      fbo.setSize(window.innerWidth, window.innerHeight);
    }

    return fbo;
  }, []);

  const size = useThree((state) => state.size);

  useEffect(() => {
    screenFbo.setSize(size.width, size.height);
  }, [size, screenFbo]);

  useEffect(() => {
    // for dev reasons, reset frame count when material gets reloaded
    frameCount.current = 0;
  }, [flowMaterial, flowFbo]);

  const flowScene = useMemo(() => new THREE.Scene(), []);

  // Update flow simulation
  useFrame(({ gl, scene, clock }, delta) => {
    const t = clock.getElapsedTime();

    // Always run cube effect
    let s = Math.sin(t * 1) - 0.2;
    if (s < 0) {
      s = 0;
    } else {
      s = 1;
    }
    let triangleHeight = s * 0.4 + 0.5;
    flowMaterial.uniforms.uTriangleHeight.value = triangleHeight;
    if (geoTryRef.current) {
      geoTryRef.current.scale.y = (triangleHeight - 0.5) * 3;
    }

    const shouldDoubleRender = delta > 1 / 75;

    gl.setRenderTarget(debugTextures ? screenFbo : null);

    // floor
    lerpMouseFloor(shouldDoubleRender ? delta / 2 : delta);
    renderFlow(
      gl,
      camera,
      flowScene,
      clock,
      flowMaterial,
      flowFbo,
      vRefsFloor,
      frameCount.current
    );

    if (shouldDoubleRender) {
      // floor
      lerpMouseFloor(delta / 2);
      renderFlow(
        gl,
        camera,
        flowScene,
        clock,
        flowMaterial,
        flowFbo,
        vRefsFloor,
        frameCount.current
      );
    }

    raymarchMaterial.uniforms.uFlowSize.value = FLOW_SIM_SIZE / 2;

    gl.render(scene, camera);
    frameCount.current++;
  }, 1);

  const geoTryRef = useRef(null);

  return (
    <group position={position}>
      {/* Flow simulation (floor) */}
      {createPortal(
        <mesh>
          {/* Has to be 2x2 to fill the screen using pos attr */}
          <planeGeometry args={[2, 2]} />
          <primitive object={flowMaterial} />
        </mesh>,
        flowScene
      )}

      {/* Pointer events (floor) - always present for mouse trail */}
      <mesh
        visible={debugFloor}
        rotation={[Math.PI / -2, 0, 0]}
        position={[0, 0, 0]}
        onPointerMove={handlePointerMoveFloor}
        onPointerOver={() => (vRefsFloor.shouldReset = true)}
      >
        <planeGeometry args={[FLOW_SIM_SIZE, FLOW_SIM_SIZE]} />
        <meshBasicMaterial map={flowFbo.read.texture} />
      </mesh>

      {/* Raymarched water (floor) */}
      <mesh
        rotation={[Math.PI / -2, 0, 0]}
        visible={renderFloor}
        position={RAYMARCH_WATER_CENTER}
      >
        <planeGeometry args={[RAYMARCH_FLOW_SIZE * 2, RAYMARCH_FLOW_SIZE]} />
        <primitive object={raymarchMaterial} />
      </mesh>

      {/* Cube and its effect: always active now */}
      {/**
      <mesh ref={geoTryRef} position={[0, -0.4, 0]} scale={[0.35, 0.15, 0.35]}>
        <primitive object={assets.pyramid.geometry} />
        <meshStandardMaterial color="black" metalness={1} />
      </mesh>
      */}

      {/* <mesh position={[0, 0.2, 0]} rotation={[-Math.PI / 2, 0, 0]}>
        <meshMatcapMaterial matcap={assets.matcap} />
        <planeGeometry args={[10, 10]} />
      </mesh> */}

      {/* <pointLight decay={2} intensity={200} position={[-8, 0.5, -1.7]} />
      <pointLight decay={2} intensity={200} position={[2, 0.5, -1.7]} />
      <pointLight decay={2} intensity={200} position={[0, 0.5, 2]} /> */}

      {/* <Cameras /> */} // Disabled for ScrollCameraController
      <Env />

      {/* Display textures */}
      {debugTextures && (
        <DebugTextures
          textures={{
            flow: flowFbo.read.texture,
            pyramidFlow: orbeFlowFbo.read.texture,
            screen: screenFbo.texture,
            screenDepth: screenFbo.depthTexture
          }}
        />
      )}
    </group>
  );
}