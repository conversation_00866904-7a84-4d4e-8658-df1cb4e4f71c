// --- <PERSON><PERSON> inlined linearizeDepth ---
float linearizeDepth(float depth, float near, float far) {
  float z = depth * 2.0 - 1.0; // back to NDC
  return 2.0 * near * far / (far + near - z * (far - near));
}
// --- End inlined linearizeDepth ---

// --- Begin inlined viewSpaceDepth ---
float viewSpaceDepth(float depth, float near, float far) {
  float z = depth * (far - near) - far;
  return (far + near + z) / (2.0 * far);
}
// --- End inlined viewSpaceDepth ---

// --- <PERSON>gin inlined valueRemap ---
float valueRemap(
  float value,
  float min,
  float max,
  float newMin,
  float newMax
) {
  return newMin + (value - min) * (newMax - newMin) / (max - min);
}
// --- End inlined valueRemap ---

float getThickness(float insideZ, float outsideZ, float uNear, float uFar) {
  float insideDepthLinear = linearizeDepth(insideZ, uNear, uFar);
  float outsideLinearDepth = linearizeDepth(outsideZ, uNear, uFar);
  // distance from the surface to the inside side
  float depthDifference = insideDepthLinear - outsideLinearDepth;

  return depthDifference;
}