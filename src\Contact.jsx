import React, { useRef, useEffect, useState } from 'react'
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from '@react-three/fiber'
import { OrbitControls } from '@react-three/drei'
import * as THREE from 'three'
import styles from './Contact.module.css'
import Lenis from 'lenis'
import { useScrollNavigation } from './useScrollNavigationWithIndicator'
import { Fluid } from '@whatisjery/react-fluid-distortion'; // Import Fluid
import { EffectComposer } from '@react-three/postprocessing'; // Import EffectComposer

import ReflectiveFloor from './components/ReflectiveFloor.jsx'
import Romannumber from './components/Romannumber.jsx'
import Blcks_logo from './components/Blcks_logo_ext_1.jsx'

// Camera controller component that responds to scroll
function CameraController({ scrollY }) {
  const { camera } = useThree()
  const initialPosition = useRef(null)
  
  useEffect(() => {
    if (!initialPosition.current) {
      initialPosition.current = { 
        x: camera.position.x,
        y: camera.position.y, 
        z: camera.position.z 
      }
    }
  }, [camera])
  
  useEffect(() => {
    if (!initialPosition.current) return
    
    const baseFOV = 60
    const maxFOV = 65
    const maxMoveDown = 0.5
    
    const scrollProgress = Math.min(scrollY / (window.innerHeight * 3.5), 1)
    const easedProgress = easeOutCubic(scrollProgress)
    
    camera.fov = baseFOV + easedProgress * (maxFOV - baseFOV)
    camera.position.y = initialPosition.current.y - (easedProgress * maxMoveDown)
    camera.updateProjectionMatrix()
    
  }, [scrollY, camera])
  
  const easeOutCubic = (x) => {
    return 1 - Math.pow(1 - x, 3)
  }
  
  return null
}

// Scene opacity controller
function SceneFadeController({ scrollY }) {
  const { scene } = useThree()
  
  useEffect(() => {
    const scrollStart = window.innerHeight * 0.5
    const scrollEnd = window.innerHeight * 2
    
    let opacity = 1
    if (scrollY > scrollStart) {
      opacity = 1 - Math.min((scrollY - scrollStart) / (scrollEnd - scrollStart), 1)
    }
    
    scene.traverse((object) => {
      if (object.isMesh && object.material) {
        if (Array.isArray(object.material)) {
          object.material.forEach(material => {
            if (material.opacity !== undefined) {
              material.opacity = opacity
              material.transparent = true
            }
          })
        } else {
          if (object.material.opacity !== undefined) {
            object.material.opacity = opacity
            object.material.transparent = true
          }
        }
      }
    })
    
  }, [scrollY, scene])
  
  return null
}

export default function Contact({ onBack, onReady, onBackToHome }) {
  const [scrollY, setScrollY] = useState(0)
  const overlayRef = useRef(null)
  const lenisRef = useRef(null)
  
  // Add the scroll navigation hook - Note: Contact has onBack but not onNext
  // Instead, we'll use onBackToHome for the "next" scroll action at the bottom
  const scrollNavRef = useScrollNavigation({
    onNext: onBackToHome, // Use backToHome as the "next" action
    onBack,
    scrollThreshold: 3
  });

  // Add useEffect for the onReady callback
  useEffect(() => {
    if (onReady) {
      onReady();
    }
  }, [onReady]);

  // Initialize Lenis smooth scrolling with enhanced smoothness settings
  useEffect(() => {
    // Initialize Lenis with smoother settings
    if (!overlayRef.current) return;
    
    const contentSection = overlayRef.current.querySelector(`.${styles.contentSection}`);
    if (!contentSection) {
      console.error("Could not find content section in Contact");
      return;
    }
    
    lenisRef.current = new Lenis({
      wrapper: overlayRef.current,
      content: contentSection,
      duration: 3,
      easing: (t) => {
        return 1 - Math.pow(1 - t, 5) + Math.sin(t * Math.PI) * 0.01;
      },
      smoothWheel: true,
      wheelMultiplier: 0.8,
      direction: 'vertical',
      gestureDirection: 'vertical',
      smooth: true,
      mouseMultiplier: 0.8,
      smoothTouch: true,
      touchMultiplier: 1.5,
      lerp: 0.075,
      infinite: false,
    })

    // Synchronize scroll values with React state
    const onScrollUpdate = (e) => {
      setScrollY(e.scroll);
    }

    lenisRef.current.on('scroll', onScrollUpdate)

    // Set up requestAnimationFrame for smoother animation
    function raf(time) {
      if (lenisRef.current) {
        lenisRef.current.raf(time)
      }
      requestAnimationFrame(raf)
    }
    
    // Start the animation loop
    requestAnimationFrame(raf)
    
    // Clean up function
    return () => {
      if (lenisRef.current) {
        lenisRef.current.off('scroll', onScrollUpdate)
        lenisRef.current.destroy()
        lenisRef.current = null
      }
    }
  }, [])
  
  // Handler for manual fluid effect on pointer move
  const handlePointerMove = (e) => {
    const canvasElement = document.querySelector('.contact-canvas-container canvas');
    if (canvasElement) {
      const event = new PointerEvent('pointermove', {
        clientX: e.clientX,
        clientY: e.clientY,
        bubbles: true
      });
      canvasElement.dispatchEvent(event);
    }
  };
  
  // Get fluid color - same as in other components
  const getFluidColor = () => {
    return '#101010';
  };
  
  // Merge the refs (Lenis ref and scroll navigation ref)
  const mergeRefs = (node) => {
    overlayRef.current = node;
    if (scrollNavRef) {
      scrollNavRef.current = node;
    }
  };
  
  return (
    <div className={styles.contactComponent} style={{ width: '100%', height: '100vh', position: 'relative' }}>

        <button 
        className="back-Button"
        onClick={onBack}
        style={{
          position: 'fixed',
          top: '2rem',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 100,
          background: 'transparent',
          border: 'none',
          cursor: 'pointer',
          padding: '10px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '0.90rem',
        }}
      >
        Back
      </button>
      
      {/* Back to Home button centered at bottom */}
      <button 
        className="next-Button"
        onClick={onBackToHome}
        style={{
          position: 'fixed',
          bottom: '2rem',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 100,
          background: 'transparent',
          border: 'none',
          cursor: 'pointer',
          padding: '10px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '0.90rem',
        }}
      >
        Back to Home
      </button>
      
      {/* 3D Canvas with Container and Fluid Effect */}
      <div className="contact-canvas-container" style={{ position: 'absolute', width: '100%', height: '100%' }}>
        <Canvas camera={{ position: [0, 0, 5], fov: 60 }}>
          <ambientLight intensity={0.5} />
          <pointLight position={[10, 10, 10]} intensity={0.5} />

          <color attach="background" args={['#000']} />
          <fog attach="fog" args={['#060606', 1, 200]} />

          {/* <Blcks_logo position={[0, -0.25, -4]} scale={[0.25, 0.25, 0.25]}/> */}
          
          <ReflectiveFloor />
          
          {/* Camera controllers */}
          <CameraController scrollY={scrollY} />
          <SceneFadeController scrollY={scrollY} />
        </Canvas>
      </div>
      
      {/* Scroll Overlay - use mergeRefs to combine both refs */}
      <div 
        className={styles.scrollOverlay} 
        ref={mergeRefs}
        onPointerMove={handlePointerMove} // Added pointer move handler for fluid effect
      >
        <section className={styles.contentSection}>
            {/* Contact Section from App3.jsx - just the content elements */}
            <div className={styles.contactWrapper}>
              <section className={styles.contactSection}>
                <div className={styles.contactContent}>
                  {/* Big Contact Email Heading in Center */}
                  <div className={styles.contactEmailContainer}>
                    <a href="mailto:<EMAIL>" className={styles.contactEmailLink}>
                      <h2 className={styles.contactEmailHeading}><EMAIL></h2>
                    </a>
                  </div>
                  
                  {/* Bottom Bar with Copyright and Built By */}
                  <div className={styles.contactBottom}>
                    <div className={styles.contactCopyright}>All rights reserved. © 2025 BLCKs</div>
                    <div className={styles.contactBuiltBy}>built by BLCKs with ❤</div>

                    {/* Footer section with 3 elements positioned as requested */}
                    <div className={styles.footerSection}>
                      {/* Footer links on the left */}
                      <div className={styles.footerLinksContainer}>
                        <div className={styles.footerLinks}>
                          <a href="https://aback-syrup-e00.notion.site/BLCKS-IMPRESSUM-20d5934475c580629a78fdc6bbee5d60" className={styles.footerLink} target="_blank" rel="noopener noreferrer">Impressum</a>
                          <a href="https://aback-syrup-e00.notion.site/BLCKS-DATENSCHUTZERKL-RUNG-20d5934475c580b69fdae48e3a5c6848" className={styles.footerLink} target="_blank" rel="noopener noreferrer">Datenschutz</a>
                          <a href="https://aback-syrup-e00.notion.site/BLCKS-COOKIE-RICHTLINIE-20d5934475c58034a1edc5c56179f398" className={styles.footerLink} target="_blank" rel="noopener noreferrer">Cookies</a>
                        </div>
                      </div>
                      
                      {/* Social icons in the middle */}
                      <div className={styles.socialIcons}>
                        <a href="#" className={styles.socialIcon}>
                          <img src="/instagram.svg" alt="Instagram" />
                        </a>
                        <a href="https://www.linkedin.com/in/philippfuchs-blcks" className="social-icon" target="_blank" rel="noopener noreferrer">
                          <img src="/linkedin.svg" alt="LinkedIn" />
                        </a>
                        <a href="#" className={styles.socialIcon}>
                          <img src="/twitter.svg" alt="Twitter" />
                        </a>
                      </div>

                      {/* QR Code section on the right */}
                      <div className={styles.qrCodeContainer}>
                        <img src="/qr-code.svg" alt="WhatsApp QR Code" className={styles.qrCodeImage} />
                        <h3 className={styles.qrCodeHeading}>WhatsApp Coming</h3>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </div>
        </section>
      </div>
    </div>
  )
}