import React from 'react';
import styles from './AboutOverlaySection.module.css';

export default function AboutOverlaySection({ open, onClose }) {
  if (!open) return null;
  return (
    <div className={styles.overlayBackdrop}>
      <div className={styles.overlayContent}>
        <button
          onClick={onClose}
          className={styles.closeButton}
          aria-label="Close About"
        >
          ×
        </button>
        <h2 className={styles.title}>Über Uns</h2>
        <p>
          Wir sind BLCKS – ein Creative AI & Design Studio.<br />
          Unser Team entwickelt skalierbare KI-Infrastrukturen und intelligente Automatisierungslösungen für zukunftsorientierte Unternehmen.
        </p>
        <p>
          Wir verbinden Kreativität, Technologie und Strategie, um echte Mehrwerte zu schaffen. <br />
          Erfahre mehr über unsere Mission, unser Team und unsere Projekte!
        </p>
      </div>
    </div>
  );
} 