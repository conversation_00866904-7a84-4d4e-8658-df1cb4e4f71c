import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import glsl from 'vite-plugin-glsl'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), glsl()],
  optimizeDeps: {
    include: [
      'three', 
      '@react-three/fiber', 
      '@react-three/drei',
      // Removed 'three-stdlib' since it's large and includes Draco
    ],
    exclude: [
      // Explicitly exclude Draco and related large libraries
      'three-stdlib',
      'three/examples/jsm/loaders/DRACOLoader',
      'three/examples/jsm/libs/draco/gltf',
    ],
    esbuildOptions: {
      define: {
        global: 'globalThis',
      },
      supported: {
        'top-level-await': true
      },
    },
  },
  resolve: {
    dedupe: ['three'],
  },
  build: {
    commonjsOptions: {
      transformMixedEsModules: true,
    },
    minify: 'terser', // Use terser for better tree-shaking
    terserOptions: {
      compress: {
        drop_console: true,
        dead_code: true,
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor': ['react', 'react-dom'],
          'three-core': ['three'],
          'react-three': ['@react-three/fiber', '@react-three/drei']
        }
      },
      external: [
        // Explicitly mark Draco-related files as external
        'three/examples/jsm/loaders/DRACOLoader.js',
        'three/examples/jsm/libs/draco/**',
      ]
    }
  },
  server: {
    hmr: {
      timeout: 5000
    }
  }
})