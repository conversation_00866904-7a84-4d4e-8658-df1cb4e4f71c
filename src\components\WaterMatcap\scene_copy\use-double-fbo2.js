import { useEffect, useMemo } from "react";
import * as THREE from "three";

export const useDoubleFBO = (width, height, options) => {
  const read = useMemo(() => {
    return new THREE.WebGLRenderTarget(width, height, options);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const write = useMemo(() => {
    return new THREE.WebGLRenderTarget(width, height, options);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    read.setSize(width, height);
    write.setSize(width, height);
  }, [width, height, read, write]);

  useEffect(() => {
    // dispose on unmount
    return () => {
      read.dispose();
      write.dispose();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fbo = useMemo(
    () => ({
      read,
      write,
      swap: () => {
        const temp = fbo.read;
        fbo.read = fbo.write;
        fbo.write = temp;
      },
      dispose: () => {
        read.dispose();
        write.dispose();
      }
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  return fbo;
};