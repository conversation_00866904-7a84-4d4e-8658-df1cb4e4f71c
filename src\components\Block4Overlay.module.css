@font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter_24pt-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter_24pt-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter_24pt-SemiBold.ttf') format('truetype');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter_24pt-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }
  
  .overlay, .contentWrapper, .infoHeading, .infoRow, .infoBlock, .infoLabel, .infoValue, .statsGrid, .statCard, .statNumber, .statUnit, .statDesc, .statIcon, .divider, .bigTitle, .bigDescription, .cardRow, .card, .cardTitle, .cardText, .newsGrid, .newsCard, .newsTag, .newsTitle, .newsDate, .newsButton, .newsButtonPrimary, .newsTitleCenter {
    font-family: 'Inter', sans-serif;
  }
  
  .bigTitle, .statNumber, .cardTitle, .newsTitleCenter {
    font-weight: 700;
  }
  
  .infoHeading, .newsButton, .newsButtonPrimary {
    font-weight: 600;
  }
  
  .infoLabel, .statDesc, .newsTag {
    font-weight: 500;
  }
  
  .infoValue, .statUnit, .bigDescription, .newsDate, .newsTitle, .cardText, .newsCard {
    font-weight: 400;
  }
  
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(20,20,20,0.75);
    z-index: 1000;
    overflow-y: auto;
    display: block;
    scrollbar-width: none; /* Firefox */
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }
  .overlay::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }
  
  .closeButton {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 2000;
    font-family: 'Inter', sans-serif;
    background: none;
    color: #fff;
    border: none;
    padding: 50px 50px 1rem 2rem;
    font-size: 1rem;
    cursor: pointer;
    margin: 0;
    border-radius: 0 0 0 8px;
    box-shadow: none;
  }
  
  .content {
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .contentWrapper {
    max-width: 1000px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    overflow-x: hidden;
    padding-bottom: 0px;
    padding-top: 20vh;
    width: 100%;
    scrollbar-width: none; /* Firefox */
  }
  .contentWrapper::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }

  .title {
    font-size: 48px;
    font-weight: 400;
    margin-bottom: 24px;
    color: #fff;
    letter-spacing: 1px;
  }

  .description {
    font-size: 28px;
    font-weight: 300;
    color: #d0d0d0;
    margin-bottom: 100px;
    line-height: 1.3;
    max-width: 1000px;
  }

  .infoRow {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    width: 1000px;
    max-width: 1000px;
    margin-top: 36px;
    margin-left: auto;
    margin-right: auto;
    align-items: end;
    text-align: left;
  }

  .infoBlock {
    min-width: 0;
    text-align: left;
  }

  .infoLabel {
    font-size: 11px;
    color: #b0b0b0;
    letter-spacing: 1.5px;
    margin-bottom: 6px;
    font-weight: 500;
  }

  .infoValue {
    font-size: 15px;
    color: #fff;
    font-weight: 400;
    line-height: 1.4;
    max-width: 350px;
    word-break: break-word;
  }

  .launchSiteWrapper {
    margin-left: auto;
    display: flex;
    align-items: center;
  }

  .launchSiteButton {
    display: flex;
    align-items: center;
    background: none;
    border: 1px solid #444;
    color: #fff;
    font-size: 13px;
    letter-spacing: 1.5px;
    padding: 10px 32px 10px 18px;
    border-radius: 2px;
    text-decoration: none;
    transition: border 0.2s, color 0.2s;
    position: relative;
  }
  .launchSiteButton:hover {
    border: 1px solid #fff;
    color: #fff;
  }

  .arrow {
    margin-left: 16px;
    font-size: 18px;
    transition: transform 0.2s;
  }
  .launchSiteButton:hover .arrow {
    transform: translateX(6px);
  }

  .infoHeading {
    font-size: 16px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 18px;
    margin-left: 0;
    text-align: left;
    letter-spacing: 1px;
  }

  .cardRow {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    width: 100%;
    max-width: 1000px;
    margin: 56px 0 0 0;
  }

  .card {
    background: rgba(40, 40, 40, 0.95);
    border-radius: 12px;
    padding: 28px 22px 24px 22px;
    box-shadow: 0 2px 16px 0 rgba(0,0,0,0.10);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
  }

  .cardTitle {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #fff;
  }

  .cardText {
    font-size: 15px;
    color: #d0d0d0;
    line-height: 1.5;
  }

  .bigTitle {
    font-size: 44px;
    font-weight: 400;
    color: #fff;
    margin-top: 0px;
    margin-bottom: 24px;
    letter-spacing: 1px;
    text-align: left;
  }

  .bigDescription {
    font-size: 28px;
    color: #d0d0d0;
    font-weight: 300;
    line-height: 1.5;
    margin-bottom: 0;
    max-width: 1000px;
    text-align: left;
  }

  .statsGrid {
    display: flex;
    flex-direction: column;
    gap: 40px;
    width: 1000px;
    max-width: 1000px;
    margin: 150px 0 0 0;
    border: none;
    background: none;
  }

  .statCard {
    position: relative;
    background: #111;
    min-height: 500px;
    border: 1px solid #222;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 48px 48px 44px 48px;
    transition: background 0.2s;
    overflow: hidden;
    aspect-ratio: unset;
  }

  .statCard:hover .statNumber,
  .statCard:hover .statTagArrow {
    color: #c3ffc6;
  }

  .statCard:hover .statTagArrow {
    transform: rotate(45deg);
  }

  /* Helper classes for React conditional rendering */
  .statNumberHover {
    color: #c3ffc6;
  }

  .statTagHover {
    color: #c3ffc6 !important;
  }

  .statTagHover .statTagArrow {
    color: #c3ffc6 !important;
  }

  .statNumber {
    font-size: 36px;
    font-weight: 400;
    color: #fff;
    margin-bottom: 16px;
    letter-spacing: 1px;
  }

  .statUnit {
    font-size: 32px;
    font-weight: 300;
    color: #aaa;
    margin-left: 4px;
  }

  .statDesc {
    font-size: 12px;
    color: #b0b0b0;
    font-weight: 300;
    margin-bottom: 12px;
    max-width: 900px;
  }

  .statIcon {
    position: absolute;
    top: 16px;
    right: 18px;
    font-size: 20px;
    color: #222;
    opacity: 0.5;
    pointer-events: none;
  }

  .divider {
    width: 1000px;
    max-width: 1000px;
    height: 0;
    border-bottom: 1px solid #222;
    margin: 150px 0;
  }

  .newsGrid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 0;
    width: 1000px;
    max-width: 1000px;
    margin: 150px 0 150px 0;
    border: none;
  }

  .newsCard {
    background: #111;
    border: 1px solid #222;
    min-height: 240px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 32px 32px 28px 32px;
    position: relative;
    padding-top: 32px;
  }

  .newsTag {
    display: inline-block;
    background: #181818;
    color: #b0b0b0;
    font-size: 12px;
    font-weight: 400;
    border-radius: 6px;
    padding: 4px 12px;
    margin-bottom: 20px;
    position: static;
    top: auto;
    left: auto;
  }

  .newsTitle {
    font-size: 20px;
    color: #fff;
    font-weight: 400;
    margin-bottom: 24px;
    line-height: 1.3;
    position: static;
    top: auto;
    left: auto;
    right: auto;
  }

  .newsDate {
    font-size: 14px;
    color: #b0b0b0;
    margin-bottom: 12px;
  }

  .newsButton {
    background: #181818;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    border: none;
    border-radius: 6px;
    padding: 14px 0;
    width: 100%;
    cursor: pointer;
    margin-top: 8px;
    transition: background 0.2s, color 0.2s;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .newsButton:hover {
    background: #222;
    color: #fff;
  }

  .newsButtonPrimary {
    background: #fff;
    color: #111;
    font-size: 15px;
    font-weight: 600;
    border: none;
    border-radius: 8px;
    padding: 14px 0;
    width: 120px;
    margin: 0 auto;
    display: block;
    cursor: pointer;
    margin-top: 32px;
    transition: background 0.2s, color 0.2s;
  }
  .newsButtonPrimary:hover {
    background: #eaeaea;
    color: #111;
  }

  .newsTitleCenter {
    font-size: 22px;
    color: #fff;
    font-weight: 400;
    text-align: center;
    margin-top: 40px;
  }

  .newsTop {
    margin-bottom: auto;
  }

  .statTopRow {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    margin-bottom: 12px;
  }

  .statHeadingIcon {
    display: inline-flex;
    align-items: center;
    margin-right: 8px;
    vertical-align: middle;
  }

  .statHeading {
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    margin-top: 0;
    margin-bottom: 12px;
    letter-spacing: 0.5px;
  }

  .statChartContainer {
    width: 100%;
    height: 200px;
    margin-top: 16px;
  }

  .statTag {
    display: inline-flex;
    align-items: center;
    background: #222;
    color: #ffaaaa;
    font-size: 14px;
    font-weight: 400;
    border-radius: 4px;
    padding: 6px 14px 6px 12px;
    margin-right: 8px;
    letter-spacing: 0.5px;
  }

  .statTagArrow {
    font-size: 14px;
    font-weight: 300;
    margin-left: 6px;
    display: inline-block;
    transform: rotate(-45deg);
  }

  .statHeading2 {
    font-size: 14px;
    font-weight: 400;
    color: #b0b0b0;
    margin-top: 0px;
    margin-bottom: 16px;
    letter-spacing: 0.2px;
    line-height: 1.5;
  }

  .statNumber, .statUnit {
    color: #fff !important;
  }

  .statTagArrow2 {
    font-size: 14px;
    font-weight: 300;
    margin-left: 6px;
    display: inline-block;
    transform: rotate(45deg);
    transition: transform 0.2s;
  }

  .statTagHover .statTagArrow2 {
    color: #c3ffc6 !important;
    transform: rotate(-45deg) !important;
  }

  .arrowRight {
    position: fixed;
    top: 50%;
    right: 100px;
    transform: translateY(-50%);
    z-index: 2000;
  }

  .arrowLeft {
    position: fixed;
    top: 50%;
    left: 100px;
    transform: translateY(-50%);
    z-index: 2000;
  }

  .arrowLeftImg {
    transform: rotate(180deg);
  }

  @keyframes arrowWiggle {
    0% { transform: translateY(0); }
    15% { transform: translateY(-4px); }
    30% { transform: translateY(3px); }
    45% { transform: translateY(-2px); }
    60% { transform: translateY(2px); }
    75% { transform: translateY(-1px); }
    100% { transform: translateY(0); }
  }

  @keyframes arrowWiggleLeft {
    0% { transform: rotate(180deg) translateY(0); }
    15% { transform: rotate(180deg) translateY(-4px); }
    30% { transform: rotate(180deg) translateY(3px); }
    45% { transform: rotate(180deg) translateY(-2px); }
    60% { transform: rotate(180deg) translateY(2px); }
    75% { transform: rotate(180deg) translateY(-1px); }
    100% { transform: rotate(180deg) translateY(0); }
  }

  .arrowRight img,
  .arrowLeftImg {
    transition: filter 0.2s cubic-bezier(0.4,0,0.2,1);
    cursor: pointer;
  }
  .arrowRight img:hover {
    animation: arrowWiggle 0.9s cubic-bezier(0.4,0,0.2,1) infinite;
  }
  .arrowLeftImg:hover {
    animation: arrowWiggleLeft 0.9s cubic-bezier(0.4,0,0.2,1) infinite;
    transform: rotate(180deg);
  }