import React, { useRef, useEffect, useState, useCallback } from 'react';
import gsap from 'gsap';
import styles from './Block1OverlayGallery.module.css';

// Utility functions (ported from utils.js)
const map = (x, a, b, c, d) => (x - a) * (d - c) / (b - a) + c;
const lerp = (a, b, n) => (1 - n) * a + n * b;
const calcWinsize = () => ({ width: window.innerWidth, height: window.innerHeight });
const getRandomNumber = (min, max) => Math.floor(Math.random() * (max - min + 1) + min);
const getMousePos = e => ({ x: e.clientX, y: e.clientY });

// Data for grid items (use your public images)
const gridData = [
  { thumb: '/forgeracer_1.avif', full: '/forgeracer_1.avif', title: 'Forgeracer 1', meta: ['Berlin, Germany', '16/05/2025'], description: 'Description for Forgeracer 1.', pos: 'pos-1' },
  // { thumb: '/forgeracer_2.avif', full: '/forgeracer_2.avif', title: 'Forgeracer 2', meta: ['Bangkok, Thailand', '22/02/2025'], description: 'Description for Forgeracer 2.', pos: 'pos-2' },
  // { thumb: '/forgeracer_3.avif', full: '/forgeracer_3.avif', title: 'Forgeracer 3', meta: ['Lisbon, Portugal', '09/12/2025'], description: 'Description for Forgeracer 3.', pos: 'pos-3' },
  // { thumb: '/forgeracer_4.avif', full: '/forgeracer_4.avif', title: 'Forgeracer 4', meta: ['Madrid, Spain', '18/10/2025'], description: 'Description for Forgeracer 4.', pos: 'pos-4' },
  // { thumb: '/forgeracer_bw_home.jpg', full: '/forgeracer_bw_home.jpg', title: 'Forgeracer BW', meta: ['Vienna, Austria', '12/03/2025'], description: 'Description for Forgeracer BW.', pos: 'pos-5' },
  { thumb: '/blcks_simple_bg_black_comp.jpg', full: '/blcks_simple_bg_black_comp.jpg', title: 'BLCKS Black', meta: ['Berlin, Germany', '21/08/2025'], description: 'Description for BLCKS Black.', pos: 'pos-6' },
  { thumb: '/blcks_simple_bg_dark_comp.jpg', full: '/blcks_simple_bg_dark_comp.jpg', title: 'BLCKS Dark', meta: ['Madrid, Spain', '18/10/2025'], description: 'Description for BLCKS Dark.', pos: 'pos-7' },
  // { thumb: '/blcks_simple_bg_light_comp.jpg', full: '/blcks_simple_bg_light_comp.jpg', title: 'BLCKS Light', meta: ['Berlin, Germany', '03/09/2025'], description: 'Description for BLCKS Light.', pos: 'pos-8' },
  // { thumb: '/aisolutions_bw_home.jpg', full: '/aisolutions_bw_home.jpg', title: 'AI Solutions', meta: ['London, UK', '16/01/2025'], description: 'Description for AI Solutions.', pos: 'pos-9' },
  { thumb: '/nordwood_bw_home_comp.jpg', full: '/nordwood_bw_home_comp.jpg', title: 'Nordwood', meta: ['Vienna, Austria', '12/03/2025'], description: 'Description for Nordwood.', pos: 'pos-10' },
  { thumb: '/nordwood_main_comp.jpg', full: '/nordwood_main_comp.jpg', title: 'Nordwood Main', meta: ['Berlin, Germany', '22/08/2025'], description: 'Description for Nordwood Main.', pos: 'pos-11' },
  // { thumb: '/nordwood_main.jpg', full: '/nordwood_main.jpg', title: 'Nordwood Main 2', meta: ['Berlin, Germany', '29/07/2025'], description: 'Description for Nordwood Main 2.', pos: 'pos-12' },
  { thumb: '/qubehotel_bw_home.jpg', full: '/qubehotel_bw_home.jpg', title: 'Qubehotel', meta: ['Sydney, Australia', '19/07/2025'], description: 'Description for Qubehotel.', pos: 'pos-13' },
  { thumb: '/qubehotel_image_2.jpg', full: '/qubehotel_image_2.jpg', title: 'Qubehotel 2', meta: ['Paris, France', '22/06/2025'], description: 'Description for Qubehotel 2.', pos: 'pos-14' },
  { thumb: '/blcks_white_short.svg', full: '/blcks_white_short.svg', title: 'BLCKS White', meta: ['Belgrade, Serbia', '04/11/2025'], description: 'Description for BLCKS White.', pos: 'pos-15' },
  { thumb: '/blcks_white.svg', full: '/blcks_white.svg', title: 'BLCKS White 2', meta: ['Moscow, Russia', '10/08/2025'], description: 'Description for BLCKS White 2.', pos: 'pos-16' },
];

// Grid Item with continuous 3D tilt effect and magnetic hover
function GridItem({ item, onClick, mouse, onHover, onHoverMove }) {
  const itemRef = useRef();
  const imgRef = useRef();
  const scaleTween = useRef();
  // Store animation state
  const state = useRef({
    xstart: getRandomNumber(70, 100),
    ystart: getRandomNumber(40, 65),
    rxstart: 5,
    rystart: 10,
    rX: 0,
    rY: 0,
    tZ: 0,
    isLeft: false,
    isTop: false,
    translationVals: { x: 0, y: 0 },
    rotationVals: { x: 0, y: 0 },
    hover: false,
    hoverEvent: null,
    // Magnetic effect state
    mag: { tx: 0, ty: 0, rId: null },
  });

  // Initial layout
  useEffect(() => {
    if (!itemRef.current) return;
    let winsize = calcWinsize();
    const rect = itemRef.current.getBoundingClientRect();
    state.current.isLeft = rect.left + rect.width / 2 < winsize.width / 2;
    state.current.isTop = rect.top + rect.height / 2 < winsize.height / 2;
    state.current.rY = state.current.isLeft
      ? map(rect.left + rect.width / 2, 0, winsize.width / 2, state.current.rystart, 0)
      : map(rect.left + rect.width / 2, winsize.width / 2, winsize.width, 0, -state.current.rystart);
    state.current.rX = state.current.isTop
      ? map(rect.top + rect.height / 2, 0, winsize.height / 2, -state.current.rxstart, 0)
      : map(rect.top + rect.height / 2, winsize.height / 2, winsize.height, 0, state.current.rxstart);
    state.current.tZ = state.current.isLeft
      ? map(rect.left + rect.width / 2, 0, winsize.width / 2, -200, -600)
      : map(rect.left + rect.width / 2, winsize.width / 2, winsize.width, -600, -200);
    gsap.set(itemRef.current, {
      rotationX: state.current.rX,
      rotationY: state.current.rY,
      z: state.current.tZ,
    });
  }, []);

  // Animation loop (only x/y, not scale)
  useEffect(() => {
    let running = true;
    function animate() {
      if (!itemRef.current || !running) return;
      let winsize = calcWinsize();
      // Calculate translation and rotation based on mouse
      state.current.translationVals.x = lerp(
        state.current.translationVals.x,
        map(mouse.x, 0, winsize.width, -state.current.xstart, state.current.xstart),
        0.08 // smoother
      );
      state.current.translationVals.y = lerp(
        state.current.translationVals.y,
        map(mouse.y, 0, winsize.height, -state.current.ystart, state.current.ystart),
        0.08 // smoother
      );
      // Rotations
      state.current.rotationVals.x = state.current.isTop
        ? lerp(
            state.current.rotationVals.x,
            map(mouse.y, 0, winsize.height / 2, state.current.rxstart, 0),
            0.08
          )
        : lerp(
            state.current.rotationVals.x,
            map(mouse.y, winsize.height / 2, winsize.height, 0, -state.current.rxstart),
            0.08
          );
      state.current.rotationVals.y = state.current.isLeft
        ? lerp(
            state.current.rotationVals.y,
            map(mouse.x, 0, winsize.width / 2, -state.current.rystart, 0),
            0.08
          )
        : lerp(
            state.current.rotationVals.y,
            map(mouse.x, winsize.width / 2, winsize.width, 0, state.current.rystart),
            0.08
          );
      gsap.set(itemRef.current, {
        x: -state.current.translationVals.x,
        y: -state.current.translationVals.y,
        rotationX: -state.current.rX - state.current.rotationVals.x,
        rotationY: -state.current.rY - state.current.rotationVals.y,
        z: state.current.tZ,
      });
      requestAnimationFrame(animate);
    }
    animate();
    return () => {
      running = false;
    };
  }, [mouse]);

  // Magnetic effect loop (separate rAF, only while hovering)
  useEffect(() => {
    if (!imgRef.current) return;
    let running = true;
    function magAnimate() {
      if (!running) return;
      let tx = state.current.mag.tx;
      let ty = state.current.mag.ty;
      if (state.current.hover && state.current.hoverEvent) {
        const rect = imgRef.current.getBoundingClientRect();
        const mx = state.current.hoverEvent.clientX - (rect.left + rect.width / 2);
        const my = state.current.hoverEvent.clientY - (rect.top + rect.height / 2);
        tx = lerp(tx, mx * 0.18, 0.18);
        ty = lerp(ty, my * 0.18, 0.18);
      } else {
        tx = lerp(tx, 0, 0.18);
        ty = lerp(ty, 0, 0.18);
      }
      state.current.mag.tx = tx;
      state.current.mag.ty = ty;
      gsap.set(imgRef.current, {
        x: tx,
        y: ty,
        // scale is handled by tween
      });
      state.current.mag.rId = requestAnimationFrame(magAnimate);
    }
    magAnimate();
    return () => {
      running = false;
      if (state.current.mag.rId) cancelAnimationFrame(state.current.mag.rId);
    };
  }, []);

  // Hover handlers (use GSAP tween for scale)
  const handleMouseEnter = e => {
    state.current.hover = true;
    state.current.hoverEvent = e;
    if (onHover) onHover(true);
    if (onHoverMove) onHoverMove({ x: e.clientX, y: e.clientY });
    if (scaleTween.current) scaleTween.current.kill();
    scaleTween.current = gsap.to(imgRef.current, { scale: 1.04, duration: 1.2, ease: 'expo' });
  };
  const handleMouseMove = e => {
    state.current.hoverEvent = e;
    if (onHoverMove) onHoverMove({ x: e.clientX, y: e.clientY });
  };
  const handleMouseLeave = () => {
    state.current.hover = false;
    state.current.hoverEvent = null;
    if (onHover) onHover(false);
    if (scaleTween.current) scaleTween.current.kill();
    scaleTween.current = gsap.to(imgRef.current, { x: 0, y: 0, scale: 1, duration: 1.4, ease: 'power4' });
  };

  return (
    <a
      href="#"
      className={`${styles.grid__item} ${styles[item.pos]}`}
      data-title={item.title}
      onClick={e => { e.preventDefault(); onClick(); }}
      tabIndex={0}
      ref={itemRef}
      onMouseEnter={handleMouseEnter}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    >
      {/* Helper text for debugging grid positions */}
      <div className={styles.gridHelperText}>
        {`${item.index + 1} ${item.pos}`}
      </div>
      <div
        className={styles.grid__item_img}
        ref={imgRef}
        style={{ backgroundImage: `url(${item.thumb})` }}
      />
    </a>
  );
}

// Preview
function Preview({ item, open, onClose }) {
  const titleRef = useRef();
  const imgWrapRef = useRef();
  const imgRef = useRef();
  const backCtrlRef = useRef();
  const contentRef = useRef();

  // Animate in/out
  useEffect(() => {
    if (open) {
      // Animate in
      gsap.set([titleRef.current, imgWrapRef.current, imgRef.current, backCtrlRef.current, contentRef.current], { clearProps: 'all' });
      gsap.set(titleRef.current.children, { opacity: 0, y: '100%' });
      gsap.set(imgWrapRef.current, { y: '100%', rotationX: -20 });
      gsap.set(imgRef.current, { y: '-100%' });
      gsap.set(backCtrlRef.current, { opacity: 0, x: '20%' });
      gsap.set(contentRef.current, { opacity: 0, y: '20%' });
      gsap.timeline()
        .to([...titleRef.current.children], { opacity: 1, y: '0%', duration: 1.5, ease: 'expo.inOut', stagger: 0.05 }, 0.1)
        .to([imgWrapRef.current, imgRef.current], { opacity: 1, y: '0%', rotationX: 0, duration: 1.5, ease: 'expo.inOut' }, 0.5)
        .to(backCtrlRef.current, { opacity: 1, x: '0%', duration: 1.5, ease: 'expo' }, 1.5)
        .to(contentRef.current, { opacity: 1, y: '0%', duration: 1.5, ease: 'expo' }, 1.5);
    }
  }, [open]);

  // Split title into chars for animation
  const splitTitle = useCallback(title => title.split('').map((c, i) => <span key={i} className={styles.char}>{c}</span>), [item]);

  if (!open) return null;
  return (
    <div className={styles.preview_overlay}>
      <div className={styles.preview__item + ' preview__item--open'}>
        <button className={styles.preview__item_back} ref={backCtrlRef} onClick={onClose}>Back</button>
        <div className={styles.preview__item_imgwrap} ref={imgWrapRef}>
          <div className={styles.preview__item_img} ref={imgRef} style={{ backgroundImage: `url(${item.full})` }} />
        </div>
        <h2 className={styles.preview__item_title} ref={titleRef}>{splitTitle(item.title)}</h2>
        <div className={styles.preview__item_content} ref={contentRef}>
          <div className={styles.preview__item_meta}>
            {item.meta.map((m, i) => <span key={i}>{m}</span>)}
          </div>
          <p className={styles.preview__item_description}>{item.description}</p>
          <button className={styles.preview__item_info}>+ Info</button>
          <button className={styles.preview__item_button}>Buy Tickets</button>
        </div>
      </div>
    </div>
  );
}

// Main Overlay
export default function Block1Overlay({ onClose }) {
  const [active, setActive] = useState(null);
  const [mouse, setMouse] = useState({ x: window.innerWidth / 2, y: window.innerHeight / 2 });
  const [hovered, setHovered] = useState(null); // index of hovered grid item
  const [hoveredPos, setHoveredPos] = useState({ x: 0, y: 0 });
  const [showTitle, setShowTitle] = useState(false);
  const gridRef = useRef();
  const floatingTitleRef = useRef();

  // Mouse move listener
  useEffect(() => {
    const handleMouseMove = e => {
      setMouse(getMousePos(e));
      if (hovered !== null) {
        setHoveredPos({ x: e.clientX, y: e.clientY });
      }
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [hovered]);

  // Animate grid items in
  useEffect(() => {
    if (gridRef.current) {
      const items = gridRef.current.querySelectorAll(`.${styles.grid__item}`);
      gsap.set(items, { scale: 1.5, opacity: 0 });
      gsap.to(items, {
        duration: 1.2,
        ease: 'expo',
        scale: 1,
        stagger: { amount: 0.4, grid: 'auto', from: 'center' },
      });
      gsap.to(items, {
        duration: 1.2,
        ease: 'power1',
        opacity: 1,
        stagger: { amount: 0.4, grid: 'auto', from: 'center' },
      });
    }
  }, []);

  // Floating title fade in/out
  useEffect(() => {
    if (floatingTitleRef.current) {
      gsap.to(floatingTitleRef.current, {
        opacity: hovered !== null ? 1 : 0,
        duration: 0.32,
        ease: 'power2.out',
        pointerEvents: 'none',
      });
    }
  }, [hovered]);

  return (
    <div className={styles.overlay}>
      <button onClick={onClose} className={styles.closeButton} aria-label="Close overlay">CLOSE</button>
      <div className={styles.content}>
        {/* Centered Heading and Text */}
        <div className={styles.centerTextSection}>
          <h1 className={styles.centerHeading}>Was genau ist AI Automation?</h1>
          <p className={styles.centerText}>
            AI Automation steht für die Verbindung von Künstlicher Intelligenz (AI) und Automatisierungstechnologien, um wiederkehrende Aufgaben und Prozesse automatisch und intelligent auszuführen. Während klassische Automatisierung vor allem auf festen Regeln basiert (z. B. „Wenn A, dann B“), geht AI Automation einen Schritt weiter:<br/>
            Sie nutzt lernende Systeme, die mitdenken, dazulernen und sich an neue Situationen anpassen können.
          </p>
          <p className={styles.centerText}><b>Typische Technologien, die dabei zum Einsatz kommen, sind:</b></p>
          <ul className={styles.centerList}>
            <li><b>Robotic Process Automation (RPA):</b> Für regelbasierte Aufgaben wie das Kopieren von Daten oder das Versenden von E-Mails.</li>
            <li><b>Machine Learning (ML):</b> Damit Systeme aus Daten lernen und z. B. Vorhersagen treffen können.</li>
            <li><b>Natural Language Processing (NLP):</b> Damit Maschinen menschliche Sprache verstehen und verarbeiten – z. B. in E-Mails oder Chats.</li>
          </ul>
          <h2 className={styles.centerHeading2}>Warum ist AI Automation so relevant für Unternehmen?</h2>
          <p className={styles.centerText}>
            In der heutigen datengetriebenen Welt müssen Unternehmen immer schneller, effizienter und präziser handeln. AI Automation hilft dabei, Prozesse:
          </p>
          <ul className={styles.centerList}>
            <li>schneller abzuwickeln</li>
            <li>fehlerfrei zu gestalten</li>
            <li>skalierbar zu machen</li>
          </ul>
          <p className={styles.centerText}>
            Und das in allen Branchen – ob in der Verwaltung, im Kundenservice, im Marketing oder in der Produktion.<br/>
            Ein großer Vorteil: Die Systeme lernen kontinuierlich dazu und passen sich an neue Anforderungen an – automatisch. Das spart nicht nur Zeit und Kosten, sondern ermöglicht auch, Mitarbeitende von repetitiven Aufgaben zu entlasten, damit sie sich auf das Wesentliche konzentrieren können.
          </p>
        </div>
        <div className={styles.grid} ref={gridRef}>
          {gridData.map((item, idx) => (
            <GridItem
              key={idx}
              item={{ ...item, index: idx }}
              onClick={() => setActive(idx)}
              mouse={mouse}
              onHover={isHovering => {
                setHovered(isHovering ? idx : null);
                setShowTitle(isHovering);
              }}
              onHoverMove={pos => setHoveredPos(pos)}
            />
          ))}
        </div>
        {active !== null && (
          <Preview item={gridData[active]} open={active !== null} onClose={() => setActive(null)} />
        )}
        {/* Floating title on hover */}
        <div
          ref={floatingTitleRef}
          className={styles.floatingTitle}
          style={{
            left: hoveredPos.x + 12,
            top: hoveredPos.y - 32,
            pointerEvents: 'none',
            position: 'fixed',
            zIndex: 2000,
            color: '#fff',
            fontWeight: 500,
            fontSize: 14,
            background: 'rgba(20,20,20,0.82)',
            padding: '4px 14px',
            borderRadius: 6,
            boxShadow: '0 2px 8px rgba(0,0,0,0.13)',
            opacity: 0,
            whiteSpace: 'nowrap',
            transition: 'opacity 0.2s',
            transform: 'translateY(-8px)',
            display: hovered !== null ? 'block' : 'none',
          }}
        >
          {hovered !== null ? gridData[hovered].title : ''}
        </div>
      </div>
    </div>
  );
} 