/* Import Satoshi font */
@import url('https://api.fontshare.com/v2/css?f[]=satoshi@900,700,500,301,300,400&display=swap');

/* Import local Telegraf fonts */
@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  font-family: 'Telegraf', sans-serif;
  color: black;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: transparent;
}

/* Canvas wrapper - fixed position */
.canvas-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

/* Canvas styles */
.canvas-wrapper canvas {
  touch-action: none;
  pointer-events: auto;
  opacity: 1;
}

/* Custom cursor styling */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
}

/* When hovering over canvas */
.canvas-wrapper:hover .custom-cursor {
  transform: translate(-50%, -50%) scale(1.5);
}

/* Portfolio header */
.portfolio-header {
  position: fixed;
  top: 2rem;
  left: 2rem;
  z-index: 10;
}

.portfolio-header h4 {
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.3;
  color: #222;
}

/* Copyright text on bottom left */
.copyright-text {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  z-index: 10;
  font-family: 'Telegraf', sans-serif;
  font-size: 0.8rem;
  color: #222;
}

/* Bottom Right Navigation */
.sidebar-nav {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1rem;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: right;
}

.sidebar-nav button {
  background: none;
  border: none;
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  color: #222;
  cursor: pointer;
  opacity: 0.5;
  transition: opacity 0.3s ease;
  text-align: right;
  padding: 0;
}

.sidebar-nav .active button {
  opacity: 1;
  font-weight: 600;
}

.sidebar-nav button:hover {
  opacity: 1;
}

.built-by {
  font-family: 'Telegraf', sans-serif;
  font-size: 0.8rem;
  color: #222;
  margin-top: 1rem;
}

/* Scrollable container */
.scroll-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
  z-index: 2;
  
  /* Hide scrollbar for different browsers */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* Hide scrollbar for Chrome, Safari, Opera */
.scroll-container::-webkit-scrollbar {
  display: none;
}

/* Make sure the reveal area interacts with mouse events */
.model-interaction-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 3;
  pointer-events: none; /* Default state */
}

/* When hovering over the hero section, enable pointer events for the model interaction */
.section#hero:hover + .model-interaction-layer {
  pointer-events: auto;
}

/* Sections */
.section {
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 10vh 0;
}

/* Combined About and Philosophy section - 150vh height */
.combined-about-section {
  min-height: 150vh;
  height: 150vh;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

/* Work section specific styling */
#work {
  height: auto;
  min-height: auto;
  display: block;
  padding: 20vh 0;
}

.section-content {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.hero-title {
  font-family: 'Telegraf', sans-serif;
  font-weight: 600;
  font-size: clamp(3rem, 10vw, 8rem);
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  letter-spacing: -2px;
}

.hero-subtitle {
  font-family: 'Telegraf', sans-serif;
  font-weight: 400;
  font-size: clamp(1.2rem, 2vw, 1.5rem);
  opacity: 0.8;
}

/* Grid Layout */
.layout-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: auto;
  gap: 2rem;
}

.section-heading {
  margin-bottom: 2rem;
}

/* Center the section heading */
.section-heading.centered {
  text-align: center;
  width: 100%;
}

/* Section Typography */
h3 {
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  text-transform: uppercase;
  color: black;
}

h4 {
  font-family: 'Telegraf', sans-serif;
  font-weight: 600;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: black;
}

p {
  font-family: 'Telegraf', sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  color: black;
}

.large-text {
  font-size: 1.2rem;
  line-height: 1.5;
  max-width: 1000px; /* Increased max-width to 1000px */
  width: 100%;
  margin: 0 auto;
}

/* Philosophy text - narrower width */
.large-text.philosophy-text {
  max-width: 800px; /* Make philosophy text narrower */
}

/* About Section - Centered text */
.section-text {
  margin: 0 auto;
  margin-bottom: 3rem;
  width: 100%;
}

.section-text.centered {
  text-align: center;
}

.profile-image-container {
  display: flex;
  justify-content: center;
  margin-top: 7rem;
  margin-bottom: 4rem; /* Added more margin between profile image and philosophy text */
  width: 100%;
}

.profile-image-square {
  width: 500px;
  height: 500px;
  overflow: hidden;
  position: relative;
  border-radius: 24px;
}

.profile-image-square img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Philosophy Section - Centered content without heading */
.philosophy-content {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24rem; /* Add some spacing above philosophy content */
}

.second-paragraph-wrapper {
  margin: 0 auto;
  text-align: center;
}

/* Services Section - Centered heading and text */
.services-heading-container {
  width: 100%;
  padding: 0vh 0;
}

.services-heading-container.centered {
  display: flex;
  justify-content: center;
  text-align: center;
}

.services-big-heading {
  font-family: 'Telegraf', sans-serif;
  font-weight: 600;
  font-size: 4rem;
  line-height: 1.1;
  text-align: center; /* Center text alignment */
  max-width: 1200px; /* Set max width for better readability */
}

/* Work Section - Alternating Gallery Style */
.project-gallery {
  display: flex;
  flex-direction: column;
  gap: 20rem; /* Doubled spacing as requested */
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
}

.gallery-item {
  width: 100%;
  display: flex;
  position: relative;
}

.gallery-item.right-aligned {
  justify-content: flex-end;
  padding-left: 20%; /* Apply padding to push the image to the right */
}

.gallery-item.center-left {
  justify-content: center;
  padding-right: 20%; /* Push it a bit to the left from center */
}

/* Project heading styling */
.project-heading {
  position: absolute;
  z-index: 5;
}

/* Remove these fixed positioning rules */
/*.gallery-item.right-aligned .project-heading {
  left: 28%; 
}

.gallery-item.center-left .project-heading {
  left: 22%;
}*/

.project-heading h2 {
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  font-size: 6rem;
  color: black;
  white-space: nowrap;
  line-height: 1.2;
}

/* Make all project images the same size */
.project-image {
  width: 65%; /* Consistent size for all project images */
  padding-bottom: 36.5625%; /* 16:9 aspect ratio of the 65% width */
  position: relative;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.05);
}

.project-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Contact Section */
.contact-details {
  grid-column: 6 / 11;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-link {
  font-size: 1.5rem;
  font-weight: 500;
  color: black;
  text-decoration: none;
  margin-top: 1rem;
  display: inline-block;
  position: relative;
}

.contact-link:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  bottom: -5px;
  left: 0;
  background-color: #222;
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease;
}

.contact-link:hover:after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

.social-links {
  display: flex;
  gap: 2rem;
  margin-top: 3rem;
}

.social-link {
  color: black;
  text-decoration: none;
  font-weight: 500;
  position: relative;
}

.social-link:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  bottom: -5px;
  left: 0;
  background-color: #222;
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease;
}

.social-link:hover:after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

/* Responsive adjustments */
@media (max-width: 1280px) {
  .large-text {
    max-width: 80%; /* Scale with screen size */
  }
  
  .large-text.philosophy-text {
    max-width: 70%; /* Keep philosophy text narrower */
  }
  
  .profile-image-square {
    width: 450px;
    height: 450px;
  }
  
  .services-big-heading {
    font-size: 3rem;
  }
  
  .project-gallery {
    gap: 10rem; /* Adjusted gap for smaller screens but still large */
  }
  
  .gallery-item.right-aligned {
    padding-left: 15%;
  }
  
  .gallery-item.center-left {
    padding-right: 15%;
  }
  
  .gallery-item.right-aligned .project-heading {
    left: 22%;
  }
  
  .gallery-item.center-left .project-heading {
    left: 15%;
  }
  
  .contact-details {
    grid-column: 5 / 10;
  }
}

@media (max-width: 992px) {
  .layout-grid {
    gap: 1rem;
  }
  
  .large-text {
    max-width: 90%; /* Scale with screen size */
  }
  
  .large-text.philosophy-text {
    max-width: 80%; /* Keep philosophy text narrower */
  }
  
  .profile-image-square {
    width: 400px;
    height: 400px;
  }
  
  .services-big-heading {
    font-size: 2.5rem;
  }
  
  .project-gallery {
    gap: 8rem; /* Still maintain large gaps on smaller screens */
  }
  
  /* Make all images full width on mobile */
  .gallery-item.right-aligned,
  .gallery-item.center-left {
    justify-content: center;
    padding-right: 0;
    padding-left: 0;
  }
  
  .project-image {
    width: 85%; /* Almost full width on mobile */
    padding-bottom: 47.8125%; /* 16:9 aspect ratio for 85% width */
  }
  
  /* Adjust heading position for mobile */
  .gallery-item.right-aligned .project-heading,
  .gallery-item.center-left .project-heading {
    left: 0;
    top: -3.5rem;
    transform: none;
  }
  
  .project-heading h2 {
    font-size: 1.5rem;
  }
  
  .contact-details {
    grid-column: 1 / -1;
  }
  
  .sidebar-nav {
    right: 1rem;
    bottom: 1rem;
  }
  
  .copyright-text {
    left: 1rem;
    bottom: 1rem;
  }
  
  .portfolio-header {
    left: 1rem;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: clamp(2rem, 8vw, 4rem);
  }
  
  .services-big-heading {
    font-size: 2rem;
  }
  
  .project-gallery {
    gap: 6rem; /* Still maintain decent spacing on mobile */
  }
  
  .project-image {
    width: 100%; /* Full width on small mobile */
    padding-bottom: 56.25%; /* 16:9 aspect ratio for full width */
  }
  
  .gallery-item.right-aligned .project-heading,
  .gallery-item.center-left .project-heading {
    top: -3rem;
  }
  
  .project-heading h2 {
    font-size: 1.2rem;
  }
  
  .large-text {
    font-size: 1rem;
    max-width: 95%; /* Scale with screen size */
  }
  
  .large-text.philosophy-text {
    max-width: 85%; /* Keep philosophy text narrower */
  }
  
  .profile-image-square {
    width: 300px;
    height: 300px;
  }
  
  /* Adjust combined section height for mobile */
  .combined-about-section {
    min-height: 170vh;
    height: auto;
  }
}

@media (max-width: 480px) {
  .profile-image-square {
    width: 280px;
    height: 280px;
  }
  
  .large-text {
    max-width: 100%; /* Full width on very small screens */
  }
  
  .large-text.philosophy-text {
    max-width: 95%; /* Slightly narrower for philosophy text even on small screens */
  }
  
  .services-big-heading {
    font-size: 1.5rem;
  }
  
  .copyright-text, .built-by {
    font-size: 0.7rem; /* Slightly smaller font on very small screens */
  }
}


.inversion-lens {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

.inversion-lens canvas {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}


.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/blcks_simple_bg_light.jpg');
  background-size: cover;
  background-position: center;
  z-index: -1; /* Place behind other content */
  opacity: 1;
}



/* Add these class definitions to your App.css file */

/* Custom size for the philosophy text */
.philosophy-text-size {
  font-size: 1.4rem; /* You can adjust this size as needed */
  line-height: 1.5;
  font-weight: 500; 
}

/* Custom size for the about description text */
.about-description-size {
  font-size: 1.2rem; /* You can adjust this size as needed */
  line-height: 1.5;
  font-weight: 500;
}

/* Ensure these custom sizes are also responsive */
@media (max-width: 1280px) {
  .philosophy-text-size {
    font-size: 1.3rem;
  }
  
  .about-description-size {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .philosophy-text-size {
    font-size: 1.2rem;
  }
  
  .about-description-size {
    font-size: 1.4rem;
  }
}

@media (max-width: 480px) {
  .philosophy-text-size {
    font-size: 1.1rem;
  }
  
  .about-description-size {
    font-size: 1.2rem;
  }
}



.fluid-canvas-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;  /* Above background but below content */
  pointer-events: none;  /* Let interactions pass through to content below */
}



/* Add these styles to your CSS to make sure there's no blue overlay */
canvas {
  background: transparent !important;
}

.fluid-effect-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}