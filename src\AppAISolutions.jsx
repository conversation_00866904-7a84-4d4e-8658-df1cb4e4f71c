import { Canvas } from '@react-three/fiber'
import { Suspense, useEffect, useRef, useState, useCallback } from 'react'
import styles from './AppAISolutions.module.css'
import { useThree } from '@react-three/fiber'
import Lenis from 'lenis'
import NoiseFilter from './components/NoiseFilter.jsx';
import { Fluid } from '@whatisjery/react-fluid-distortion';
import { EffectComposer } from '@react-three/postprocessing';
import { useInView } from 'react-intersection-observer';
import * as THREE from 'three';

//Import Pages
import ProjectPage from './ProjectPage';
//import AboutPage from './AboutPage';
import About from './About';
import Services from './Services';
import Steps from './Steps';
import Contact from './Contact';

import AnimatedText from './components/AnimatedText';
//import CustomizableShaderBackground from './CustomizableShaderBackground.jsx';
//import SimpleFadeSection from './components/SimpleFadeSection';

import Preloader from './Preloader';

//import WaterMatcap from './components/WaterMatcap/WaterMatcap';
import WaterMatcapScene from './components/WaterMatcap/WaterMatcapScene';

// Blog posts data structure
const blogPostsData = {
  digitalTransformation: { 
    id: "digitalTransformation", 
    name: "DIGITAL TRANSFORMATION", 
    title: "Digital Transformation of a Large Telematic Service", 
    description: "In this case study, we explore how a major telematic service provider transformed their operations through AI-powered solutions. We implemented custom machine learning algorithms to analyze vehicle data in real-time, improving predictive maintenance capabilities and reducing downtime by 47%. The digital transformation journey included modernizing legacy systems, implementing cloud infrastructure, and developing user-friendly interfaces for both operators and end users.",
    aboutTitle: "A comprehensive digital transformation that revolutionized telematic services through advanced AI implementation.",
    heroImage: "/blog-featured.jpg", 
    category: "AI IMPLEMENTATION · TELEMATIC", 
    client: "Telematic Service Provider", 
    year: "2025", 
    author: "Writer Name",
    timeAgo: "10 hours ago",
    images: [
      {src: "/blog-detail-1.jpg", alt: "Telematic dashboard interface"},
      {src: "/blog-detail-2.jpg", alt: "AI data processing visualization"},
      {src: "/blog-detail-3.jpg", alt: "System architecture diagram"},
      {src: "/blog-detail-4.jpg", alt: "Implementation team working on solution"}
    ],
    content: `
      <h2>Challenge</h2>
      <p>The client faced significant challenges with their existing telematic infrastructure. Legacy systems were unable to handle the increasing volume of data from connected vehicles, and predictive capabilities were limited. Customer satisfaction was declining due to service interruptions and the lack of real-time insights.</p>
      
      <h2>Solution</h2>
      <p>We developed a comprehensive digital transformation strategy that included:</p>
      <ul>
        <li>Implementation of scalable cloud infrastructure</li>
        <li>Development of custom machine learning models for predictive maintenance</li>
        <li>Real-time data processing pipeline with anomaly detection</li>
        <li>User-friendly dashboards for operators and end users</li>
        <li>Integration with existing enterprise systems</li>
      </ul>
      
      <h2>Results</h2>
      <p>The transformation delivered remarkable results:</p>
      <ul>
        <li>47% reduction in vehicle downtime</li>
        <li>68% improvement in maintenance efficiency</li>
        <li>35% increase in customer satisfaction scores</li>
        <li>Data processing capacity increased by 300%</li>
        <li>ROI achieved within 14 months</li>
      </ul>
      
      <h2>Technologies Used</h2>
      <p>The solution leveraged various cutting-edge technologies:</p>
      <ul>
        <li>TensorFlow for machine learning models</li>
        <li>Azure cloud infrastructure</li>
        <li>Apache Kafka for real-time data streaming</li>
        <li>React.js for frontend dashboards</li>
        <li>GraphQL for API development</li>
      </ul>
    `
  },
  aiHandImplementation: { 
    id: "aiHandImplementation", 
    name: "AI HAND IMPLEMENTATION", 
    title: "Implementation of AI-Powered Hand Gesture Recognition for Industry Control Systems", 
    description: "This project focused on developing a contactless control system for industrial environments using advanced hand gesture recognition. We created a custom computer vision solution that allows operators to control machinery through intuitive hand movements, enhancing safety and efficiency in environments where physical contact with control surfaces is problematic. The system works in challenging lighting conditions and with varying user hand sizes and positions.",
    aboutTitle: "A revolutionary gesture control system for industrial applications powered by cutting-edge computer vision AI.",
    heroImage: "/blog-hand.jpg", 
    category: "COMPUTER VISION · INDUSTRIAL AUTOMATION", 
    client: "Manufacturing Corporation", 
    year: "2025", 
    author: "Writer Name",
    timeAgo: "12 hours ago",
    images: [
      {src: "/blog-hand-detail-1.jpg", alt: "Hand gesture recognition in action"},
      {src: "/blog-hand-detail-2.jpg", alt: "System architecture diagram"},
      {src: "/blog-hand-detail-3.jpg", alt: "Training data visualization"},
      {src: "/blog-hand-detail-4.jpg", alt: "Factory implementation"}
    ],
    content: `
      <h2>Challenge</h2>
      <p>The manufacturing client needed a way for operators to control precision machinery in environments where traditional interfaces were impractical due to safety concerns, contamination risks, or the need to wear protective equipment. Existing solutions lacked accuracy and couldn't handle the variable lighting conditions of the factory floor.</p>
      
      <h2>Solution</h2>
      <p>We developed a specialized computer vision system that:</p>
      <ul>
        <li>Recognizes 15 distinct hand gestures with 99.3% accuracy</li>
        <li>Functions in variable lighting conditions from bright to dim</li>
        <li>Operates with sub-50ms latency for real-time control</li>
        <li>Adapts to different users without requiring individual calibration</li>
        <li>Integrates seamlessly with existing industrial control systems</li>
      </ul>
      
      <h2>Results</h2>
      <p>The implementation delivered significant improvements:</p>
      <ul>
        <li>28% increase in operator efficiency</li>
        <li>42% reduction in control-related errors</li>
        <li>Zero contamination incidents since implementation</li>
        <li>ROI achieved within 9 months</li>
        <li>Solution now being rolled out to 12 additional facilities</li>
      </ul>
      
      <h2>Technologies Used</h2>
      <p>The solution leveraged several advanced technologies:</p>
      <ul>
        <li>Custom CNN models for hand detection and gesture recognition</li>
        <li>NVIDIA Jetson hardware for edge computing</li>
        <li>PyTorch for deep learning implementation</li>
        <li>Custom data augmentation pipeline for training robustness</li>
        <li>C++ for low-latency integration with industrial systems</li>
      </ul>
    `
  },
  smartLighting: { 
    id: "smartLighting", 
    name: "SMART LIGHTING", 
    title: "Digital Transformation Through Smart Lighting Systems for Commercial Buildings", 
    description: "This case study examines how we transformed energy management for a commercial real estate portfolio through intelligent lighting systems. The project involved developing an AI-powered platform that optimizes lighting based on occupancy patterns, natural light availability, and user preferences. The system integrates with building management infrastructure and provides detailed analytics on energy consumption and savings, resulting in significant cost reductions and environmental benefits.",
    aboutTitle: "An innovative smart lighting solution that demonstrates the power of IoT and AI in modern building management.",
    heroImage: "/blog-lights.jpg", 
    category: "IOT · ENERGY MANAGEMENT", 
    client: "Commercial Real Estate Group", 
    year: "2025", 
    author: "Writer Name",
    timeAgo: "12 hours ago",
    images: [
      {src: "/blog-lights-detail-1.jpg", alt: "Smart lighting dashboard"},
      {src: "/blog-lights-detail-2.jpg", alt: "Energy savings visualization"},
      {src: "/blog-lights-detail-3.jpg", alt: "IoT network diagram"},
      {src: "/blog-lights-detail-4.jpg", alt: "Office implementation"}
    ],
    content: `
      <h2>Challenge</h2>
      <p>The client managed over 30 commercial buildings with outdated lighting systems that were energy-inefficient and costly to maintain. They needed a solution that would reduce energy consumption, provide centralized management, and enhance tenant satisfaction while integrating with existing building management systems.</p>
      
      <h2>Solution</h2>
      <p>We developed a comprehensive smart lighting platform that includes:</p>
      <ul>
        <li>IoT-enabled lighting fixtures with individual addressability</li>
        <li>AI algorithms for predictive lighting optimization</li>
        <li>Occupancy and natural light sensors throughout buildings</li>
        <li>Centralized management dashboard with building-specific controls</li>
        <li>Tenant-facing mobile app for personalized lighting preferences</li>
      </ul>
      
      <h2>Results</h2>
      <p>The implementation delivered impressive outcomes:</p>
      <ul>
        <li>41% reduction in lighting-related energy consumption</li>
        <li>$2.3M annual cost savings across the portfolio</li>
        <li>72% decrease in maintenance calls related to lighting</li>
        <li>23% improvement in tenant satisfaction scores</li>
        <li>Carbon footprint reduction equivalent to planting 15,000 trees</li>
      </ul>
      
      <h2>Technologies Used</h2>
      <p>The solution leveraged various technologies:</p>
      <ul>
        <li>Mesh network of IoT lighting controllers</li>
        <li>Machine learning for occupancy pattern recognition</li>
        <li>Cloud-based management platform with edge computing elements</li>
        <li>React Native for cross-platform mobile applications</li>
        <li>GraphQL API for system integration</li>
      </ul>
    `
  }
};

// Updated Industry data structure with heading, subheadings, and bullet points
const industryData = {
  industries: [
    { name: "Hotel", icon: "/icon-healthcare.svg", active: true },
    { name: "Immobilien", icon: "/icon-logistics.svg" },
    { name: "Versicherung", icon: "/icon-realestate.svg" },
    { name: "E-Commerce", icon: "/icon-education.svg" },
    { name: "Gesundheitswesen", icon: "/icon-fintech.svg" },
    { name: "Rechtsberatung & Notare", icon: "/icon-agriculture.svg" },
    { name: "Finanzdienstleister", icon: "/icon-ecommerce.svg" },
    { name: "Öffentliche Verwaltung", icon: "/icon-gaming.svg" }
  ],
  details: {
    "Hotel": {
      subheadings: [
        {
          title: "Intelligenter KI-Agent & Digitaler Concierge",
          bullets: [
            "Unser hochentwickelter KI-Agent führt 24/7 natürliche, mehrsprachige Gespräche – von Buchungsanfragen über Sonderwünsche bis hin zu individuellen Empfehlungen während des Aufenthalts. Er begleitet Gäste als digitaler Concierge, empfiehlt personalisierte Erlebnisse, Restaurants und Wellness-Angebote und sorgt so für ein rundum sorgloses Gästemanagement. Gleichzeitig ermöglicht er automatisiertes Upselling, etwa für Frühstück, Spa oder Late-Check-out – perfekt abgestimmt auf Gästetyp und Aufenthaltsdauer.",
          ]
        },
        {
          title: "Backoffice & Verwaltung",
          bullets: [
            "Unsere smarte E-Mail-Inbox übernimmt das Management und die Priorisierung von eingehenden E-Mails, beantwortet Routineanfragen automatisiert und sorgt so für effiziente Abläufe. Zudem unterstützt die KI mit automatisierten SEO-Wettbewerbsanalysen, Keyword-Recherche sowie der Planung, Erstellung und Veröffentlichung von Blogbeiträgen – für nachhaltige Online-Sichtbarkeit.",
          ]
        },
        {
          title: "Marketing",
          bullets: [
            "Automatisierte, personalisierte Newsletter-Kampagnen versenden gezielte Inhalte, die auf Aufenthaltsdaten, Interessen und Buchungsverhalten Ihrer Gäste basieren. Zudem erstellt die KI kreative Social-Media-Beiträge und Werbeanzeigen – abgestimmt auf Saison, Zielgruppen und aktuelle Trends. Mit der Echtzeit-Analyse von Gästefeedback und Bewertungen erkennen Sie Muster und Potenziale für gezielte Marketingmaßnahmen und Serviceverbesserungen.",
          ]
        }
      ]
    },
    "Immobilien": {
      subheadings: [
        {
          title: "Intelligenter KI-Agent für Anfragen",
          bullets: [
            "Unser KI-Agent steht rund um die Uhr bereit, um Anfragen automatisiert zu beantworten, Termine zu vereinbaren und Follow-ups durchzuführen. Dabei qualifiziert er Leads intelligent vor und leitet nur vielversprechende Kontakte direkt an Ihr Vertriebsteam weiter – für eine effiziente und zeitsparende Kundenbetreuung ohne Verzögerungen.",
          ]
        },
        {
          title: "Backoffice & Verwaltung",
          bullets: [
            "Unsere smarte E-Mail-Inbox managt und priorisiert eingehende Mails eigenständig, beantwortet Routineanfragen automatisch und entlastet so Ihr Team. Darüber hinaus sorgt die KI für automatisierte SEO-Wettbewerbsanalysen, führt Keyword-Recherchen durch und plant sowie erstellt Blogbeiträge, die sie anschließend selbständig veröffentlicht – so steigern Sie nachhaltig Ihre Online-Sichtbarkeit.",
          ]
        },
        {
          title: "Vertrieb & Marketing",
          bullets: [
            "Mit unserem KI-Lead-Agenten automatisieren Sie die personalisierte Kaltakquise per E-Mail – inklusive intelligentem Follow-up mit Erinnerungen und automatischer Terminvereinbarung. Zusätzlich unterstützt die KI bei der Leadgenerierung und bewertet Interessenten durch Lead-Scoring, damit Sie sich auf die wertvollsten Kontakte konzentrieren können. Auch die Erstellung und der Versand von Newslettern sowie von Social-Media-Inhalten wie Texten, Bildern und Videos erfolgen vollautomatisch und zielgerichtet.",
          ]
        }
      ]
    },
    "Versicherung": {
      subheadings: [
        {
          title: "Intelligenter KI-Agent für Neu- & Bestandskunden",
          bullets: [
            "Unser KI-Agent beantwortet Kundenanfragen rund um die Uhr automatisiert, vereinbart Termine und übernimmt Follow-ups. Er unterstützt bei der Schadensmeldung, begleitet Kunden durch den Prozess und bietet gezielte Upselling-Möglichkeiten, indem er passende Zusatzprodukte empfiehlt. Dabei qualifiziert er Leads intelligent und entlastet Ihr Vertriebsteam, um Abschlüsse effizienter zu gestalten.",
          ]
        },
        {
          title: "Backoffice & Verwaltung",
          bullets: [
            "Mit der smarten E-Mail-Inbox verwaltet die KI den Posteingang, beantwortet Routineanfragen selbstständig und priorisiert wichtige Mails. Zusätzlich unterstützt die KI durch automatisierte SEO-Analysen, Keyword-Recherche und die Planung, Erstellung sowie Veröffentlichung von Blogbeiträgen, um Ihre Online-Präsenz zu stärken.",
          ]
        },
        {
          title: "Vertrieb & Marketing",
          bullets: [
            "Der KI-Lead-Agent führt personalisierte Kaltakquise per E-Mail durch, inklusive automatischer Nachfassaktionen und Terminvereinbarungen. Er generiert neue Leads, bewertet diese nach Potenzial und erstellt personalisierte Newsletter sowie zielgerichtete Social-Media-Inhalte – Texte, Grafiken und Videos – für eine maximale Reichweite.",
          ]
        }
      ]
    },
    "E-Commerce": {
      subheadings: [
        {
          title: "Intelligenter KI-Agent für Kundenservice & Beratung",
          bullets: [
            "Unser fortschrittlicher KI-Agent unterstützt Ihre Kund:innen rund um die Uhr – ob bei Produktsuche, Fragen zu Lieferstatus oder Retouren. Er führt natürliche, kontextbezogene Gespräche, berät personalisiert auf Basis von Nutzerdaten und erhöht so die Conversion-Rate sowie die Kundenzufriedenheit. Automatisierte Follow-ups und gezielte Produktempfehlungen sorgen für nahtlose Betreuung entlang der gesamten Customer Journey.",
          ]
        },
        {
          title: "Backoffice & Verwaltung",
          bullets: [
            "Die smarte E-Mail-Inbox priorisiert und beantwortet automatisch Kundenanfragen, Bestelländerungen und Supporttickets. Darüber hinaus analysiert die KI Wettbewerberdaten und Keywords und erstellt vollautomatisch SEO-optimierte Blogartikel und Produktbeschreibungen – ideal für langfristige Sichtbarkeit und organischen Traffic.",
          ]
        },
        {
          title: "Vertrieb & Marketing",
          bullets: [
            "Mit unserer KI-gesteuerten Vertriebsautomatisierung erstellen Sie personalisierte E-Mail-Kampagnen, automatisieren Follow-ups und generieren neue Leads samt Scoring. Die KI erstellt zudem zielgerichtete Newsletter, Social-Media-Posts, Werbetexte und Produktvisualisierungen – abgestimmt auf Zielgruppe, Kaufverhalten und saisonale Trends. Produktbewertungen und Kundenfeedback werden in Echtzeit analysiert, um gezielt Optimierungspotenziale und Up- & Cross-Selling-Möglichkeiten zu identifizieren.",
          ]
        }
      ]
    },
    "Gesundheitswesen": {
      subheadings: [
        {
          title: "Intelligenter KI-Agent für Patientenkommunikation",
          bullets: [
            "Unser KI-Agent übernimmt die Patientenkommunikation rund um die Uhr – von der Terminvereinbarung über Behandlungsinformationen bis hin zur Nachsorge. Er kommuniziert natürlich und datenschutzkonform, erinnert automatisch an Termine oder Medikationen und beantwortet häufige Fragen schnell und zuverlässig. Das entlastet das Personal spürbar und verbessert das Patientenerlebnis.",
          ]
        },
        {
          title: "Verwaltung & Organisation",
          bullets: [
            "Die smarte E-Mail-Inbox organisiert den gesamten digitalen Posteingang: Sie priorisiert medizinische Anfragen, verwaltet Terminanfragen und filtert automatisch Weiterleitungen an das zuständige Fachpersonal. Zusätzlich unterstützt die KI bei der automatisierten Dokumentation, Formularverwaltung und beim internen Wissensmanagement – effizient und fehlerfrei.",
          ]
        },
        {
          title: "Marketing & Patientenbindung",
          bullets: [
            "Mit KI-basiertem Content-Marketing werden automatisch zielgruppenorientierte Gesundheitsartikel, Newsletter oder Hinweise zu Präventionsangeboten erstellt und veröffentlicht. Social-Media-Beiträge und Kampagnen werden auf saisonale Themen und spezifische Zielgruppen zugeschnitten. Patientenbewertungen und Online-Rezensionen analysiert die KI in Echtzeit – so erkennen Sie Optimierungspotenziale und stärken das Vertrauen in Ihre Einrichtung.",
          ]
        }
      ]
    },
    "Rechtsberatung & Notare": {
      subheadings: [
        {
          title: "Intelligenter KI-Agent für Mandantenkommunikation",
          bullets: [
            "Der KI-Agent übernimmt die erste Mandantenkommunikation, beantwortet häufig gestellte Fragen (z. B. zu Abläufen, Fristen, Dokumenten) und koordiniert Terminvereinbarungen – selbstverständlich datenschutzkonform. Auch Status-Updates zu laufenden Fällen oder automatisierte Erinnerungen an benötigte Unterlagen lassen sich effizient und rund um die Uhr abwickeln.",
          ]
        },
        {
          title: "Dokumentation & Fallmanagement",
          bullets: [
            "KI unterstützt bei der automatisierten Erstellung, Prüfung und Kategorisierung juristischer Dokumente – von Verträgen bis hin zu Vollmachten. Intelligente Systeme erfassen Inhalte, erkennen Risiken, formulieren Textvorschläge und bereiten Schriftsätze effizient vor. Die Verwaltung offener Akten, Fristen und Aufgaben erfolgt dabei smart und automatisiert.",
          ]
        },
        {
          title: "Marketing & Mandantengewinnung",
          bullets: [
            "Unsere KI erstellt SEO-optimierte Texte für Kanzleiwebsites, Social-Media-Beiträge und Newsletter – abgestimmt auf Fachgebiete und Zielgruppen. Potenzielle Mandanten werden durch KI-generierte Inhalte gezielt angesprochen. Gleichzeitig analysiert die KI Onlinebewertungen und identifiziert Themen, mit denen Sie sich als Experten positionieren können.",
          ]
        }
      ]
    },
    "Finanzdienstleister": {
      subheadings: [
        {
          title: "Intelligenter KI-Agent für Kundenberatung",
          bullets: [
            "Der KI-Agent übernimmt die erste Kundenansprache, beantwortet rund um die Uhr Fragen zu Produkten, Finanzplänen oder Dokumentenanforderungen – schnell, persönlich und kontextbezogen. Er führt durch komplexe Themen wie Kreditvergleiche oder Anlageoptionen, bietet smarte Empfehlungen auf Basis der Kundendaten und erinnert automatisch an fehlende Unterlagen oder Fristen.",
          ]
        },
        {
          title: "Backoffice & Compliance",
          bullets: [
            "Die smarte E-Mail-Inbox übernimmt automatische Beantwortungen, kategorisiert Anfragen und leitet sie intern effizient weiter. KI unterstützt zudem bei der Einhaltung regulatorischer Vorgaben durch automatisierte Dokumentenprüfung und Compliance-Monitoring. Auch interne Abläufe wie Vertragsverwaltung oder Datenpflege werden durch intelligente Automatisierung beschleunigt.",
          ]
        },
        {
          title: "Marketing & Kundenbindung",
          bullets: [
            "KI-generierte Newsletter, Produktinfos oder Blogbeiträge informieren Kunden gezielt – abgestimmt auf Interessen, Risikoprofil oder Lebenssituation. Social-Media-Kampagnen inkl. Text, Bild und Video werden automatisch aufbereitet. Kundenbewertungen und -verhalten analysiert die KI in Echtzeit – so können gezielte Aktionen zur Kundenbindung oder Rückgewinnung ausgelöst werden.",
          ]
        }
      ]
    },
    "Öffentliche Verwaltung": {
      subheadings: [
        {
          title: "Intelligenter KI-Agent für Bürgerkommunikation",
          bullets: [
            "Der KI-Agent beantwortet rund um die Uhr Anfragen von Bürger:innen zu Themen wie Meldeamt, Gebühren, Formularen oder Veranstaltungen – mehrsprachig, freundlich und präzise. Er hilft beim Ausfüllen von Anträgen, gibt Rückmeldungen zum Bearbeitungsstand und übernimmt die automatisierte Terminvergabe für Behördengänge.",
          ]
        },
        {
          title: "Backoffice & interne Prozesse",
          bullets: [
            "Die smarte E-Mail-Inbox filtert eingehende Nachrichten, beantwortet Standardanfragen automatisch und priorisiert dringliche Anliegen. Zusätzlich unterstützt die KI bei der Personalplanung, internen Kommunikation und Prozessoptimierung – etwa durch Analyse von Bearbeitungszeiten oder Engpässen in der Verwaltung.",
          ]
        },
        {
          title: "Verwaltung & Dokumentenmanagement",
          bullets: [
            "Mit KI-gestützter Dokumentenverarbeitung werden Formulare, Anträge und Bescheide automatisch erfasst, geprüft, sortiert und intern weitergeleitet. Die KI erkennt unvollständige Angaben, fordert fehlende Unterlagen an und unterstützt bei der rechtskonformen Archivierung – schnell, effizient und transparent.",
          ]
        }
      ]
    }
  }
};

// Simple black background component to replace the texture background
function BlackBackground() {
  const { scene } = useThree();
  
  useEffect(() => {
    // Set the scene background color to black (#000000)
    scene.background = new THREE.Color('#000000');
    
    return () => {
      // Clean up if needed
      scene.background = null;
    };
  }, [scene]);
  
  return null;
}

function OverlayNoiseFilter() {
  const { camera } = useThree();
  useEffect(() => { camera.layers.enable(31); }, [camera]);
  return <NoiseFilter intensity={0.03} speed={0.3} />;
}

function AutomatedFluidEffect() {
  const canvasRef = useRef(null);
  const animationStateRef = useRef({ isAnimating: false, lastX: 0, lastY: 0 });
  
  useEffect(() => {
    const getCanvas = () => {
      // Ensure we are targeting the visible, interactive canvas
      canvasRef.current = document.querySelector(`.${styles.canvasContainer} canvas`);
      return canvasRef.current;
    };
    const createPointerMove = (x, y) => {
      const canvas = getCanvas();
      if (!canvas) return;
      const event = new PointerEvent('pointermove', { clientX: x, clientY: y, bubbles: true });
      canvas.dispatchEvent(event);
      animationStateRef.current.lastX = x;
      animationStateRef.current.lastY = y;
    };
    const generateSmoothPath = (startX, startY, endX, endY, numPoints) => {
      const points = [];
      for (let i = 0; i <= numPoints; i++) {
        const t = i / numPoints;
        const controlX = (startX + endX) / 2 + (Math.random() - 0.5) * 100;
        const controlY = (startY + endY) / 2 + (Math.random() - 0.5) * 100;
        const x = Math.pow(1-t, 2) * startX + 2 * (1-t) * t * controlX + Math.pow(t, 2) * endX;
        const y = Math.pow(1-t, 2) * startY + 2 * (1-t) * t * controlY + Math.pow(t, 2) * endY;
        points.push({ x, y });
      }
      return points;
    };
    const simulateMousePath = () => {
      const canvas = getCanvas();
      if (!canvas || animationStateRef.current.isAnimating) return;
      animationStateRef.current.isAnimating = true;
      const rect = canvas.getBoundingClientRect();
      const startX = animationStateRef.current.lastX || (Math.random() * rect.width + rect.left);
      const startY = animationStateRef.current.lastY || (Math.random() * rect.height + rect.top);
      const endX = Math.random() * rect.width + rect.left;
      const endY = Math.random() * rect.height + rect.top;
      const numPoints = Math.floor(Math.random() * 10) + 15;
      const points = generateSmoothPath(startX, startY, endX, endY, numPoints);
      const baseDelay = 40;
      points.forEach((point, index) => {
        const pointDelay = baseDelay + Math.random() * 15;
        setTimeout(() => {
          createPointerMove(point.x, point.y);
          if (index === points.length - 1) {
            setTimeout(() => { animationStateRef.current.isAnimating = false; }, 300);
          }
        }, index * pointDelay);
      });
    };
    const getRandomInterval = () => (Math.random() * 3000) + 3000;
    let intervalId;
    const timeoutId = setTimeout(() => {
      simulateMousePath();
      intervalId = setInterval(simulateMousePath, getRandomInterval());
    }, 1000);
    return () => { clearTimeout(timeoutId); clearInterval(intervalId); };
  }, []);
  return null;
}

const FADE_DURATION = 500; // ms


function App() {
  const [isPreloaderComplete, setIsPreloaderComplete] = useState(false);
  const [activeIndustry, setActiveIndustry] = useState("Hotel");

  const handlePreloaderComplete = () => {
    setIsPreloaderComplete(true);
  };

  const [currentView, setCurrentView] = useState({ type: 'main', blogId: null, section: 'home' });
  const [activeSection, setActiveSection] = useState('home');
  const [overlayStyle, setOverlayStyle] = useState({
    opacity: 0, 
    backgroundColor: '#e8e8e8',
    pointerEvents: 'none', 
    zIndex: -1,
  });

  // Inside the App component
  const [contactRef, inViewContact] = useInView({
    threshold: 0.9,
    triggerOnce: false
  });

  // Effect to handle contact section visibility
  useEffect(() => {
    const contactSection = document.getElementById('contact');
    
    if (inViewContact && contactSection) {
      // Add class when contact section is in view
      contactSection.classList.add(styles.fadeToBlack);
    } else if (!inViewContact && contactSection) {
      // Remove class when scrolling away
      contactSection.classList.remove(styles.fadeToBlack);
    }
  }, [inViewContact]);

  // Add this to your App.js, replacing the existing contact section visibility effect
  useEffect(() => {
    const contactSection = document.getElementById('contact');
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Contact section is in view, add class to body
          document.body.classList.add('contact-active');
          contactSection.classList.add(styles.fadeToBlack);
        } else {
          // Contact section is out of view, remove class from body
          document.body.classList.remove('contact-active');
          contactSection.classList.remove(styles.fadeToBlack);
        }
      });
    }, { threshold: 0.4 }); // Trigger when 40% of the section is visible
    
    if (contactSection) {
      observer.observe(contactSection);
    }
    
    return () => {
      if (contactSection) {
        observer.unobserve(contactSection);
      }
    };
  }, []);

  // For transition effects
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isDarkMode, setIsDarkMode] = useState(false);
  
  const scrollContainerRef = useRef(null);
  const mainLenisRef = useRef(null);
  const sectionsRef = useRef({
    home: null, industries: null, services: null, product: null, team: null, blog: null, slogan: null, contact: null
  });
  const mainScrollStateRef = useRef({ scrollTop: 0 });
  
  // Store the blog section path for back button navigation
  const blogSectionPathRef = useRef({ path: "/", scrollTop: 0 });
  
  // Throttle function to prevent excessive calculations
  const throttle = (func, limit) => {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  };

  useEffect(() => {
    if ('scrollRestoration' in window.history) {
      window.history.scrollRestoration = 'manual';
    }

    const initialPath = window.location.pathname;
    const initialPathSegments = initialPath.split('/').filter(Boolean);
    let determinedInitialSection = 'home';
    let determinedInitialScrollTop = 0;
    let determinedViewType = 'main';
    let determinedBlogId = null;

    if (initialPath === '/' || initialPath === '/home') {
      determinedInitialSection = 'home';
      determinedInitialScrollTop = 0;
      window.history.replaceState({ type: 'main', section: 'home', scrollTop: 0 }, '', '/');
    } else if (initialPathSegments.length === 2 && initialPathSegments[0] === 'blog' && blogPostsData[initialPathSegments[1]]) {
      determinedViewType = 'blog';
      determinedBlogId = initialPathSegments[1];
      determinedInitialSection = null; 
    } else if (initialPathSegments.length === 1) { 
      const potentialSection = initialPathSegments[0];
      if (Object.keys(sectionsRef.current).includes(potentialSection)) {
        determinedInitialSection = potentialSection;
      } else {
        determinedInitialSection = 'home'; 
      }
      determinedInitialScrollTop = 0; 
      const newPathForReplace = `/${determinedInitialSection === 'home' ? '' : determinedInitialSection}`;
      window.history.replaceState({ type: 'main', section: determinedInitialSection, scrollTop: 0 }, '', newPathForReplace);
    } else { 
      determinedInitialSection = 'home';
      determinedInitialScrollTop = 0;
      window.history.replaceState({ type: 'main', section: 'home', scrollTop: 0 }, '', '/');
    }

    mainScrollStateRef.current.scrollTop = determinedInitialScrollTop;
    setActiveSection(determinedViewType === 'main' ? determinedInitialSection : 'home'); 
    setCurrentView({ 
      type: determinedViewType, 
      blogId: determinedBlogId,
      section: determinedViewType === 'main' ? determinedInitialSection : null 
    });

    const handlePopStateInternal = (event) => {
      setOverlayStyle({ 
        opacity: 1, 
        backgroundColor: '#e8e8e8',
        pointerEvents: 'auto', 
        zIndex: 1000 
      });
      setTimeout(() => {
        const state = event.state || {};
        const path = window.location.pathname;
        const pathSegmentsPop = path.split('/').filter(Boolean);
        let isNavigatingToBlog = false;
        let newViewConfig = {};

        if (state.type === 'blog' && state.blogId && blogPostsData[state.blogId]) {
          newViewConfig = { type: 'blog', blogId: state.blogId };
          isNavigatingToBlog = true;
        } else if (pathSegmentsPop.length === 2 && pathSegmentsPop[0] === 'blog' && blogPostsData[pathSegmentsPop[1]]) {
          newViewConfig = { type: 'blog', blogId: pathSegmentsPop[1] };
          isNavigatingToBlog = true;
        } else { 
          const sectionFromState = state.section || (pathSegmentsPop.length === 1 && Object.keys(sectionsRef.current).includes(pathSegmentsPop[0]) ? pathSegmentsPop[0] : 'home');
          mainScrollStateRef.current.scrollTop = state.scrollTop || 0; 
          newViewConfig = { type: 'main', blogId: null, section: sectionFromState };
          setActiveSection(sectionFromState); 
        }
        setCurrentView(newViewConfig);
        if (!isNavigatingToBlog) { 
          setTimeout(() => {
            setOverlayStyle(prev => ({ ...prev, opacity: 0 }));
            setTimeout(() => { 
              setOverlayStyle({ 
                opacity: 0, 
                backgroundColor: '#e8e8e8',
                pointerEvents: 'none', 
                zIndex: -1 
              });
            }, FADE_DURATION);
          }, 100); 
        }
      }, FADE_DURATION);
    };
    
    window.addEventListener('popstate', handlePopStateInternal);
    
    return () => {
      window.removeEventListener('popstate', handlePopStateInternal);
    };
  }, []); 

  useEffect(() => {
    let lenisInstance = null;
    let rafId = null;
    const container = scrollContainerRef.current;

    function raf(time) { 
      if (lenisInstance) {
        lenisInstance.raf(time);
        rafId = requestAnimationFrame(raf);
      }
    }

    if (currentView.type === 'main' && container) {
      lenisInstance = new Lenis({
        duration: 3.0, 
        easing: (t) => (t === 1 ? 1 : 1 - Math.pow(2, -10 * t)),
        orientation: 'vertical', 
        gestureOrientation: 'vertical', 
        smoothWheel: true,
        wheelMultiplier: 0.8, 
        smoothTouch: true, 
        touchMultiplier: 1.5, 
        infinite: false,
        wrapper: container, 
        content: container.querySelector(`.${styles.allSectionsContent}`),
      });
      
      mainLenisRef.current = lenisInstance;
      
      // Update section refs once
      Object.keys(sectionsRef.current).forEach(sectionId => {
        sectionsRef.current[sectionId] = document.getElementById(sectionId);
      });
      
      // Throttled scroll handler for better performance
      const handleScrollThrottled = throttle((e) => {
        // Store current scroll position
        const scrollPosition = lenisInstance.scroll;
        mainScrollStateRef.current.scrollTop = scrollPosition;
        
        // Update active section based on scroll position
        const windowHeight = window.innerHeight;
        let currentActiveSection = activeSection;
        let minDistance = Infinity;
        
        Object.entries(sectionsRef.current).forEach(([id, element]) => {
          if (!element) return; // Skip null elements
          
          const elementRect = element.getBoundingClientRect();
          const elementCenter = elementRect.top + elementRect.height / 2;
          const distanceFromCenter = Math.abs(elementCenter - windowHeight / 2);
          
          if (distanceFromCenter < minDistance) {
            minDistance = distanceFromCenter;
            currentActiveSection = id;
          }
        });
        
        if (currentActiveSection !== activeSection) {
          setActiveSection(currentActiveSection);
        }
        
      }, 50); // Using a shorter throttle time for smoother transitions
      
      lenisInstance.on('scroll', handleScrollThrottled);
      
      // Initial section setting when component mounts or view changes
      if (currentView.section && sectionsRef.current[currentView.section]) {
        const targetSection = sectionsRef.current[currentView.section];
        
        // If we have a stored scroll position for this section, use it
        const historyState = window.history.state;
        if (historyState?.type === 'main' && historyState.section === currentView.section && typeof historyState.scrollTop === 'number') {
          // Use stored position
          setTimeout(() => {
            if (lenisInstance && !lenisInstance.isStopped) {
              lenisInstance.scrollTo(historyState.scrollTop, { immediate: true });
            }
          }, 10);
        } else if (currentView.section !== 'home') {
          // Scroll to section
          setTimeout(() => {
            if (lenisInstance && !lenisInstance.isStopped && targetSection) {
              lenisInstance.scrollTo(targetSection, { immediate: false });
            }
          }, 10);
        }
      }
      
      // Start the animation frame loop
      rafId = requestAnimationFrame(raf);
      
      return () => {
        if (rafId) {
          cancelAnimationFrame(rafId);
        }
        
        if (lenisInstance) {
          lenisInstance.off('scroll', handleScrollThrottled);
          lenisInstance.destroy();
        }
        
        mainLenisRef.current = null;
      };
    } 
  }, [currentView.type, currentView.section]); 

  // New function to handle blog post clicks
  const handleBlogPostClick = useCallback((blogId) => {
    const blogPost = blogPostsData[blogId];
    if (blogPost) {
      if (mainLenisRef.current) {
        const currentScrollPosition = mainLenisRef.current.scroll;
        mainScrollStateRef.current.scrollTop = currentScrollPosition;
        
        // Save the exact scroll position
        blogSectionPathRef.current = {
          path: "/",
          scrollTop: currentScrollPosition
        };
      
        window.history.replaceState(
          { type: 'main', section: 'blog', scrollTop: mainScrollStateRef.current.scrollTop },
          '', 
          window.location.pathname 
        );
    
        setOverlayStyle({ 
          opacity: 1, 
          backgroundColor: '#e8e8e8',
          pointerEvents: 'auto', 
          zIndex: 1000 
        });
        setTimeout(() => {
          const newPath = `/blog/${blogId}`;
          window.history.pushState({ type: 'blog', blogId }, blogPost.name, newPath);
          setCurrentView({ type: 'blog', blogId });
        }, FADE_DURATION);
      }
    }
  }, []);

  const handleNavClick = useCallback((sectionId) => {
    if (sectionId === 'industries') {
      // Store current scroll position
      if (mainLenisRef.current) {
        mainScrollStateRef.current.scrollTop = mainLenisRef.current.scroll;
      }
      
      // Navigate directly to the industries section
      const sectionElement = sectionsRef.current[sectionId];
      if (sectionElement && mainLenisRef.current) {
        mainLenisRef.current.scrollTo(sectionElement, {
          offset: 0, 
          duration: 2.5, 
          easing: (t) => t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2,
        });
        
        window.history.pushState(
          { type: 'main', section: sectionId, scrollTop: 0 }, 
          '', 
          `/${sectionId}`
        );
        setCurrentView(prev => ({ ...prev, section: sectionId }));
        setActiveSection(sectionId);
      }
      
      return;
    }

    if (currentView.type !== 'main' || !mainLenisRef.current) return;
    const sectionElement = sectionsRef.current[sectionId];
    if (sectionElement) {
      mainLenisRef.current.scrollTo(sectionElement, {
        offset: 0, 
        duration: 2.5, 
        easing: (t) => t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2,
      });
      // Update history and currentView.section when a nav item is clicked
      window.history.pushState(
        { type: 'main', section: sectionId, scrollTop: 0 }, 
        '', 
        `/${sectionId === 'home' ? '' : sectionId}`
      );
      setCurrentView(prev => ({ ...prev, section: sectionId })); // Ensure currentView.section updates
      setActiveSection(sectionId); // Also keep activeSection in sync
    }
  }, [currentView.type]); // Dependencies for nav click

  // Add a transition state to handle black fade transitions
  const [pageTransition, setPageTransition] = useState({
    active: false,
    from: '',
    to: '',
    color: '#000000'
  });
  
  // Add a page transition overlay for the black fade effects
  const pageTransitionStyle = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: pageTransition.color,
    opacity: pageTransition.active ? 1 : 0,
    pointerEvents: pageTransition.active ? 'auto' : 'none',
    zIndex: 9999,
    transition: 'opacity 800ms ease-in-out',
  };

  const handlePointerEventPassthrough = useCallback((e) => {
    if (currentView.type !== 'main') return; 
    const canvasElement = document.querySelector(`.${styles.canvasContainer} canvas`);
    if (canvasElement) {
      const event = new PointerEvent(e.type, { clientX: e.clientX, clientY: e.clientY, bubbles: true });
      canvasElement.dispatchEvent(event);
    }
  }, [currentView.type]);

  // Get fluid color based on dark mode state
  const getFluidColor = () => {
    return isDarkMode ? '#e8e8e8' : '#3b3b3b';
  };

  // Handler for changing the active industry
  const handleIndustryClick = (industryName) => {
    setActiveIndustry(industryName);
  };

  // Step 1: Track if the hero section (home) is active
  const heroRef = useRef();
  const [isHeroActive, setIsHeroActive] = useState(true);
  useEffect(() => {
    const observer = new window.IntersectionObserver(
      ([entry]) => setIsHeroActive(entry.isIntersecting),
      { threshold: 0.1 }
    );
    if (heroRef.current) observer.observe(heroRef.current);
    return () => observer.disconnect();
  }, []);

  return (
    <div className={styles.appContainer}>

      {!isPreloaderComplete && (
        <Preloader onComplete={handlePreloaderComplete} />
      )}

      <div style={pageTransitionStyle}></div>

      <div className={styles.canvasPreloader} style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: '#000000', // Changed to black
        zIndex: 0
      }}></div>

      <div className={styles.canvasContainer} style={{ 
        display: currentView.type === 'about' || currentView.type === 'services' || currentView.type === 'steps' || currentView.type === 'contact' ? 'none' : 'block' 
      }}>
        <Canvas 
          camera={{ position: [0, 0, 5], fov: 45 }}
          gl={{ alpha: true, antialias: true, powerPreference: 'high-performance', clearColor: [0, 0, 0, 1] }} // Changed to black
          style={{ pointerEvents: 'auto' }} 
          key={currentView.type} 
        >
          <Suspense fallback={null}>
            <BlackBackground /> {/* Replaced texture background with black background */}
            <ambientLight intensity={0.5} />
            <pointLight position={[5, 5, 5]} intensity={1} />
            <WaterMatcapScene showCubeEffect={isHeroActive} />
            {/*
            <>
              <EffectComposer>
                <Fluid 
                  radius={0.36} curl={3} swirl={2} distortion={1} force={1} pressure={0.10} 
                  densityDissipation={0.95} velocityDissipation={1.00} intensity={4.45} 
                  rainbow={false} blend={5} showBackground={true} 
                  backgroundColor='transparent' 
                  fluidColor={getFluidColor()} 
                />
              </EffectComposer>
              <AutomatedFluidEffect />
            </>
            */}
          </Suspense>
        </Canvas>
        {/*<WaterMatcap style={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          zIndex: 0, 
          width: '100vw', 
          height: '100vh',
          pointerEvents: 'auto' 
        }} />*/}
      </div>

      <div className={styles.transitionOverlay} style={overlayStyle}></div>

      {/* Render BlogPage when a blog post is selected */}
      {currentView.type === 'blog' && currentView.blogId && (
        <ProjectPage 
          project={blogPostsData[currentView.blogId]} 
          isBlog={true}
          onClose={() => {
            setOverlayStyle({ 
              opacity: 1, 
              backgroundColor: '#e8e8e8',
              pointerEvents: 'auto', 
              zIndex: 1000 
            });
            setTimeout(() => {
              window.history.pushState(
                { type: 'main', section: 'blog', scrollTop: blogSectionPathRef.current.scrollTop }, 
                '', 
                blogSectionPathRef.current.path
              );
              setCurrentView({ type: 'main', blogId: null, section: 'blog' });
              
              setTimeout(() => {
                setOverlayStyle(prev => ({ ...prev, opacity: 0 }));
                setTimeout(() => {
                  setOverlayStyle({
                    opacity: 0, 
                    backgroundColor: '#e8e8e8',
                    pointerEvents: 'none', 
                    zIndex: -1
                  });
                  
                  if (mainLenisRef.current) {
                    mainLenisRef.current.scrollTo(blogSectionPathRef.current.scrollTop, { 
                      immediate: true 
                    });
                  }
                }, FADE_DURATION);
              }, 100);
            }, FADE_DURATION);
          }}
        />
      )}

      <div 
        className={`${styles.mainContentWrapper} ${currentView.type === 'main' && overlayStyle.opacity === 0 ? styles.fadein : styles.fadeout}`}
        style={{ 
          zIndex: currentView.type === 'main' ? 5 : 1,
          display: currentView.type === 'about' || currentView.type === 'services' || currentView.type === 'steps' || currentView.type === 'contact' ? 'none' : 'block'
        }}
      >
        {/* Top left logo */}
        <div className={styles.topLeftLogo}>
          <img src="/blcks_white_short.svg" alt="BLCKS Logo" />
        </div>
        {/* Bottom left copyright */}
        <div className={styles.bottomLeftCopyright}>
          © 2025 BLCKs
        </div>
        {currentView.type === 'main' && (
          <>
            <div 
              className={styles.contentContainer} 
              ref={scrollContainerRef}
              onPointerMove={handlePointerEventPassthrough} 
            >
              <div className={styles.allSectionsContent}>
                <section id="home" ref={heroRef} className={activeSection === "home" ? `${styles.section} ${styles.home} ${styles.active}` : `${styles.section} ${styles.home}`} data-scroll-section>
                  <div className={styles.sectionContent}>
                    <div className={styles.homeContainer}>
                      <div className={styles.homeContent}>
                        <h1 className={styles.homeTitle}>
                          Skalierbare KI-Lösungen<br />von BLCKS - gebaut für<br />echten Business Impact.
                        </h1>
                        
                        <div className={styles.homeEventDetails}>
                          <div className={styles.homeCopyright}>
                            © 2025 BLCKs
                          </div>
                          <div className={styles.homeTicketButton + ' ' + styles.hiddenTicketButton}>
                            <a href="#" className={styles.ticketLink + ' ' + styles.disabledTicketLink} tabIndex="-1" aria-disabled="true" onClick={e => e.preventDefault()}>
                              KONTAKTIERE UNS →
                            </a>
                          </div>
                          <div className={styles.scrollIndicator}>
                            <p className={styles.scrollText}>SCROLL DOWN</p>
                            <img src="/scrolldownpfeil_white.svg" alt="Scroll Down" className={styles.scrollArrow} />
                          </div>
                        </div>
                      </div>
                      
                      {/* Keep the scroll down text and arrow */}
                      
                    </div>
                  </div>
                </section>
                <section id="slogan" className={activeSection === "slogan" ? `${styles.section} ${styles.slogan} ${styles.active}` : `${styles.section} ${styles.slogan}`} data-scroll-section>
                  <div className={styles.sectionContent}>
                    <div className={styles.sloganContainer}>
                      <p className={styles.aboutUsText}>Unsere Mission</p>
                      
                      <div className={styles.sloganMainText}>
                        <h2 className={styles.sloganHeading}>
                          Wir erkennen KI-Potenziale und nutzen sie, um Ihre Effizienz zu steigern und nachhaltigen Erfolg zu sichern.
                        </h2>
                        {/* Decorative line/dots below the heading */}
                        <div className={styles.sloganDivider}></div>
                      </div>
                      
                      <div className={styles.sloganDescriptionContainer}>
                        <div className={styles.sloganDescription}>
                          <p className={styles.sloganParagraph}>
                            Bei BLCKS sind wir überzeugt, dass innovative KI-Lösungen die Arbeitsweise und Kommunikation von Unternehmen nachhaltig verbessern - und fit für die Anforderungen der Zukunft machen.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
                <section id="services" className={activeSection === "services" ? `${styles.section} ${styles.services} ${styles.active}` : `${styles.section} ${styles.services}`} data-scroll-section>
                  <div className={styles.sectionContent}>
                    <div className={styles.servicesGridContainer}>
                      <h2 className={styles.servicesHeadingNew}>Our Portfolio</h2>
                      <div className={styles.servicesGrid}>
                        {[
                          { name: "CORINTIS", logo: "corintis-logo", description: "Thermal management solutions for next-gen electronics." },
                          { name: "Chemify", logo: "chemify-logo", description: "Digitizing chemistry for automated molecule creation." },
                          { name: "FILECOIN", logo: "filecoin-logo", description: "Decentralized storage network for humanity's data." },
                          { name: "Flashbots", logo: "flashbots-logo", description: "Research and development for a transparent, efficient Ethereum." },
                          { name: "Privy", logo: "privy-logo", description: "Privacy infrastructure for web3 applications." },
                          { name: "Protocol Labs", logo: "protocol-labs-logo", description: "Open-source R&D lab building protocols, tools, and services." },
                          { name: "Anysignal", logo: "anysignal-logo", description: "Satellite communications for the modern world." },
                          { name: "DXOS", logo: "dxos-logo", description: "Open-source framework for distributed applications." },
                          { name: "GroupHug", logo: "grouphug-logo", description: "Creative solar panels for everyday spaces." },
                          { name: "BlueYard", logo: "blueyard-logo", description: "Venture capital for founders reimagining the world." },
                          { name: "QubeHotel", logo: "qubehotel-logo", description: "Smart hospitality solutions for hotels and guests." },
                          { name: "Nordwood", logo: "nordwood-logo", description: "Sustainable wood products for modern living." },
                          { name: "Forgeracer", logo: "forgeracer-logo", description: "Innovative racing technology and engineering." },
                          { name: "AISolutions", logo: "aisolutions-logo", description: "AI-powered business automation and analytics." },
                          { name: "RomanNumber", logo: "romannumber-logo", description: "Creative digital studio for branding and design." },
                          { name: "Blcks", logo: "blcks-logo", description: "Scalable AI solutions for real business impact." },
                          { name: "Engaged", logo: "engaged-logo", description: "Employee engagement and feedback platform." },
                          { name: "Following Wildfire", logo: "following-wildfire-logo", description: "Wildfire tracking and environmental data." },
                          { name: "MaxiBlur", logo: "maxiblur-logo", description: "Advanced image processing and effects." },
                          { name: "Philipp", logo: "philipp-logo", description: "Personal portfolio and creative projects." },
                          { name: "Telegraf", logo: "telegraf-logo", description: "Modern typeface for digital and print." },
                          { name: "Archivo", logo: "archivo-logo", description: "Bold sans-serif font for impactful design." },
                          { name: "NoiseFilter", logo: "noisefilter-logo", description: "Real-time noise reduction for audio and video." },
                          { name: "RevealEffect", logo: "revealeffect-logo", description: "UI effects for interactive web experiences." },
                        ].map((company, idx) => {
                          const cardRef = useRef(null);
                          const [isVisible, setIsVisible] = useState(false);

                          useEffect(() => {
                            const observer = new window.IntersectionObserver(
                              ([entry]) => {
                                if (entry.isIntersecting) {
                                  setIsVisible(true);
                                  observer.disconnect();
                                }
                              },
                              { threshold: 0.2 }
                            );
                            if (cardRef.current) observer.observe(cardRef.current);
                            return () => observer.disconnect();
                          }, []);

                          return (
                            <div
                              className={
                                styles.card + ' ' + (isVisible ? styles.cardVisible : styles.cardInitial)
                              }
                              key={idx}
                              ref={cardRef}
                            >
                              <div className={styles.cardTop}>
                                <div className={styles.cardTopLeft}></div>
                                <div className={styles.cardTopRight}>
                                  <span>{company.name}</span>
                                </div>
                              </div>
                              <div className={styles.cardBottom}>
                                <div className={styles.servicesCardLogo + ' ' + styles.hideOnHover}></div>
                                <div className={styles.servicesCardTitle + ' ' + styles.hideOnHover}>{company.name}</div>
                                <div className={styles.cardHoverText}>
                                  <p>{company.description}</p>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </section>
                <section id="example" className={`${styles.section} ${styles.example}`} data-scroll-section>
                  <div className={styles.sectionContent}>
                    {/* Content will be provided later */}
                  </div>
                </section>
                <section id="product" className={activeSection === "product" ? `${styles.section} ${styles.product} ${styles.active}` : `${styles.section} ${styles.product}`} data-scroll-section>
                  <div className={styles.sectionContent}>
                    <div className={styles.productContainer}>
                      <div className={styles.productContentWrapper}>
                        <div className={styles.productTextColumn}>
                          <h2 className={styles.productHeading}>
                            THE IMPACT OF AI ON THE WORLD IS FAR-REACHING AND PROFOUND, CHANGING THE WAY WE LIVE, WORK, AND INTERACT.
                          </h2>
                        </div>
                        
                        <div className={styles.productVisualColumn}>
                          <div className={styles.productVisual}>
                            <img src="/Screen1.avif" alt="iphoneScreen" className={styles.productImage} />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
                <section id="team" className={activeSection === "team" ? `${styles.section} ${styles.team} ${styles.active}` : `${styles.section} ${styles.team}`} data-scroll-section>
                  <div className={styles.sectionContent}>
                    <div className={styles.teamContainer}>
                      <div className={styles.teamHeaderRow}>
                        <h2 className={styles.teamHeading}>UNSER TEAM</h2>
                        <div className={styles.teamDottedLine}></div>
                      </div>
                      
                      <div className={styles.teamMembers}>
                        <div className={styles.teamMember}>
                          <div className={styles.memberImageContainer}>
                            <img src="/me_transparent.avif" alt="Team member" className={styles.memberImage} />
                          </div>
                          <div className={styles.memberInfo} style={{ backgroundColor: '#00CC88' }}>
                            <div className={styles.memberLabel}>IT & Development</div>
                            <h3 className={styles.memberName}>Philipp Fuchs</h3>
                            <div className={styles.memberBarcode}></div>
                          </div>
                        </div>
                        
                        <div className={styles.teamMember}>
                          <div className={styles.memberImageContainer}>
                            <img src="/maxi_blur.avif" alt="Team member" className={styles.memberImage} />
                          </div>
                          <div className={styles.memberInfo} style={{ backgroundColor: '#FFFFFF' }}>
                            <div className={styles.memberLabel}>Sales & Kundenberatung</div>
                            <h3 className={styles.memberName}>Maximilian Dirnberger</h3>
                            <div className={styles.memberBarcode}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
                <section id="blog" className={activeSection === "blog" ? `${styles.section} ${styles.blog} ${styles.active}` : `${styles.section} ${styles.blog}`} data-scroll-section>
                  <div className={styles.sectionContent}>
                    <div className={styles.blogContainer}>
                      <div className={styles.blogHeader}>
                        <h2 className={styles.blogHeading}>UNSER <span className={styles.blogHeadingAccent}>BLOG</span></h2>
                        
                        <div className={styles.blogNavigation}>
                          <button className={styles.blogNavButton}>
                            <span className={styles.blogNavArrow}>←</span>
                          </button>
                          <button className={styles.blogNavButton}>
                            <span className={styles.blogNavArrow}>→</span>
                          </button>
                        </div>
                      </div>

                      {/* Featured blog post (large) */}
                      <div 
                        className={styles.blogFeatured} 
                        onClick={() => handleBlogPostClick('digitalTransformation')}
                      >
                        <div className={styles.blogFeaturedLeft}>
                          {/* Dot pattern background */}
                          <div className={styles.blogDotPattern}></div>
                        </div>
                        <div className={styles.blogFeaturedRight}>
                          <div className={styles.blogCategory}>Category</div>
                          <h3 className={styles.blogTitle}>DIGITAL TRANSFORMATION OF A LARGE TELEMATIC SERVICE</h3>
                          <div className={styles.blogImage}>
                            <img src="/blog-featured.jpg" alt="Digital transformation" />
                          </div>
                          <div className={styles.blogMeta}>
                            <div className={styles.blogTime}>
                              <span className={styles.blogTimeIcon}>⏱</span>
                              <span>10 hours ago</span>
                            </div>
                            <div className={styles.blogAuthor}>
                              <span className={styles.authorIcon}>👤</span>
                              <span className={styles.authorName}>Writer Name</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Blog posts grid */}
                      <div className={styles.blogGrid}>
                        {/* Left blog post */}
                        <div 
                          className={styles.blogPost}
                          onClick={() => handleBlogPostClick('aiHandImplementation')}
                        >
                          <div className={styles.blogCategory}>Category</div>
                          <h3 className={styles.blogTitle}>DIGITAL TRANSFORMATION OF A LARGE TELEMATIC SERVICE</h3>
                          <div className={styles.blogImage}>
                            <img src="/blog-hand.jpg" alt="Digital transformation - hand" />
                          </div>
                          <div className={styles.blogMeta}>
                            <div className={styles.blogTime}>
                              <span className={styles.blogTimeIcon}>⏱</span>
                              <span>12 hours ago</span>
                            </div>
                            <div className={styles.blogAuthor}>
                              <span className={styles.authorIcon}>👤</span>
                              <span className={styles.authorName}>Writer Name</span>
                            </div>
                          </div>
                        </div>
                        
                        {/* Right blog post */}
                        <div 
                          className={styles.blogPost}
                          onClick={() => handleBlogPostClick('smartLighting')}
                        >
                          <div className={styles.blogCategory}>Category</div>
                          <h3 className={styles.blogTitle}>DIGITAL TRANSFORMATION OF A LARGE TELEMATIC SERVICE</h3>
                          <div className={styles.blogImage}>
                            <img src="/blog-lights.jpg" alt="Digital transformation - lights" />
                          </div>
                          <div className={styles.blogMeta}>
                            <div className={styles.blogTime}>
                              <span className={styles.blogTimeIcon}>⏱</span>
                              <span>12 hours ago</span>
                            </div>
                            <div className={styles.blogAuthor}>
                              <span className={styles.authorIcon}>👤</span>
                              <span className={styles.authorName}>Writer Name</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
                
                {/* Contact Section without overlay */}
                <section id="contact" className={`${styles.section} ${styles.contactSection} ${styles.contact} ${activeSection === "contact" ? styles.active : ""}`} data-scroll-section ref={contactRef}>              
                  <div className={styles.contactContent}>
                    {/* Big Contact Email Heading in Center */}
                    <div className={styles.contactEmailContainer}>
                      <a href="mailto:<EMAIL>" className={styles.contactEmailLink}>
                        <h2 className={styles.contactEmailHeading}><EMAIL></h2>
                      </a>
                    </div>
                    
                    {/* Bottom Bar with Copyright and Built By */}
                    <div className={styles.contactBottom}>
                      <div className={styles.contactCopyright}>All rights reserved. © 2025 BLCKs</div>
                      <div className={styles.contactBuiltBy}>built by BLCKs with ❤</div>

                      {/* New footer section with 3 elements positioned as requested */}
                      <div className={styles.footerSection}>
                        {/* Footer links on the left */}
                        <div className={styles.footerLinksContainer}>
                          <div className={styles.footerLinks}>
                            <a href="https://aback-syrup-e00.notion.site/BLCKS-IMPRESSUM-20d5934475c580629a78fdc6bbee5d60" className={styles.footerLink} target="_blank" rel="noopener noreferrer">Impressum</a>
                            <a href="https://aback-syrup-e00.notion.site/BLCKS-DATENSCHUTZERKL-RUNG-20d5934475c580b69fdae48e3a5c6848" className={styles.footerLink} target="_blank" rel="noopener noreferrer">Datenschutz</a>
                            <a href="https://aback-syrup-e00.notion.site/BLCKS-COOKIE-RICHTLINIE-20d5934475c58034a1edc5c56179f398" className={styles.footerLink} target="_blank" rel="noopener noreferrer">Cookies</a>
                          </div>
                        </div>
                        
                        {/* Social icons in the middle */}
                        <div className={styles.socialIcons}>
                          <a href="#" className={styles.socialIcon}>
                            <img src="/instagram.svg" alt="Instagram" />
                          </a>
                          <a href="https://www.linkedin.com/in/philippfuchs-blcks" className={styles.socialIcon} target="_blank" rel="noopener noreferrer">
                            <img src="/linkedin.svg" alt="LinkedIn" />
                          </a>
                          <a href="#" className={styles.socialIcon}>
                            <img src="/twitter.svg" alt="Twitter" />
                          </a>
                        </div>

                        {/* QR Code section on the right */}
                        <div className={styles.qrCodeContainer}>
                          <img src="/qr-code.svg" alt="WhatsApp QR Code" className={styles.qrCodeImage} />
                          <h3 className={styles.qrCodeHeading}>WhatsApp Coming</h3>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default App;