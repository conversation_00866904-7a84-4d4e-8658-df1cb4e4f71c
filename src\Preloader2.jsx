import React, { useEffect, useRef } from 'react';
import gsap from 'gsap';
import { CustomEase } from 'gsap/CustomEase';
import { SplitText } from 'gsap/SplitText';
import './Preloader.css';

const Preloader = () => {
  const preloaderRef = useRef(null);
  const splitOverlayRef = useRef(null);
  const containerRef = useRef(null);
  const cardRef = useRef(null);
  const introTitleRef = useRef(null);
  const outroTitleRef = useRef(null);

  useEffect(() => {
    // Register GSAP plugins
    gsap.registerPlugin(CustomEase, SplitText);

    // Create custom ease
    CustomEase.create("hop", ".8, 0, .3, 1");

    // Ensure DOM is fully ready before starting animations
    const initializeAnimation = () => {
      // Split text elements function
      const splitTextElements = (
        selector,
        type = "words,chars",
        addFirstChar = false
      ) => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element) => {
          const splitText = new SplitText(element, {
            type,
            wordsClass: "word",
            charsClass: "char",
          });

          if (type.includes("chars")) {
            splitText.chars.forEach((char, index) => {
              const originalText = char.textContent;
              char.innerHTML = `<span>${originalText}</span>`;

              if (addFirstChar && index === 0) {
                char.classList.add("first-char");
              }
            });
          }
        });
      };

      // Reset any previous animations
      gsap.set(".intro-title .char span, .outro-title .char span", {
        clearProps: "all"
      });
      gsap.set(".tag p .word", { clearProps: "all" });
      gsap.set(".container", { clipPath: "polygon(0 48%, 0 48%, 0 52%, 0 52%)" });
      gsap.set(".card", { clipPath: "polygon(0% 50%, 100% 50%, 100% 50%, 0% 50%)" });
      gsap.set(".preloader", { y: "0%" });
      gsap.set(".split-overlay", { y: "0%" });
      
      // Initialize split text elements
      splitTextElements(".intro-title h1", "words, chars", true);
      splitTextElements(".outro-title h1");
      splitTextElements(".tag p", "words");
      splitTextElements(".card h1", "words, chars", true);

      // Check if mobile
      const isMobile = window.innerWidth < 1000;

      // Ensure elements are visible before animation
      gsap.set(
        [
          ".intro-title .char span",
          ".outro-title .char span",
        ],
        { 
          y: "-100%",
          opacity: 1,
          visibility: "visible"
        }
      );

      // Setup initial positions for split overlay
      gsap.set(
        [
          ".split-overlay .intro-title .first-char span",
          ".split-overlay .outro-title .char span",
        ],
        { y: "0%" }
      );

      gsap.set(".split-overlay .intro-title .first-char span", {
        x: isMobile ? "7.5rem" : "18rem",
        y: isMobile ? "-1rem" : "-2.75rem",
        fontWeight: "900",
        scale: 0.75, 
      });

      gsap.set(".split-overlay .outro-title .char", {
        x: isMobile ? "-3rem" : "-8rem",
        fontSize: isMobile ? "6rem" : "14rem",
        fontWeight: "500",
      });

      // Create animation timeline with a slight delay to ensure everything is ready
      const tl = gsap.timeline({ 
        defaults: { ease: "hop" },
        delay: 0.1 
      });
      
      const tags = gsap.utils.toArray(".tag");

      // Animate tags
      tags.forEach((tag, index) => {
        tl.to(
          tag.querySelectorAll("p .word"),
          {
            y: "0%",
            duration: 0.75,
          },
          0.5 + index * 0.1
        );
      });

      // Main animation sequence
      tl.to(
        ".preloader .intro-title .char span",
        {
          y: "0%",
          duration: 0.75,
          stagger: 0.05,
        },
        0.5
      )
      .to(
        ".preloader .intro-title .char:not(.first-char) span",
        {
          y: "100%",
          duration: 0.75,
          stagger: 0.05,
        },
        2
      )
      .to(
        ".preloader .outro-title .char span", 
        {
          y: "0%",
          duration: 0.75,
          stagger: 0.075,
        }, 
        2.5
      )
      .to(
        ".preloader .intro-title .first-char",
        {
          x: isMobile ? "9rem" : "21.25rem",
          duration: 1,
        },
        3.5
      )
      .to(
        ".preloader .outro-title .char",
        {
          x: isMobile ? "-3rem" : "-8rem",
          duration: 1,
        },
        3.5
      )
      .to(
        ".preloader .intro-title .first-char",
        {
          x: isMobile ? "7.5rem" : "18rem",
          y: isMobile ? "-1rem" : "-2.75rem",
          fontWeight: "900",
          scale: 0.75,
          duration: 0.75
        },
        4.5
      )
      .to(
        ".preloader .outro-title .char",
        {
          x: isMobile ? "-3rem" : "-8rem",
          fontSize: isMobile ? "6rem" : "14rem",
          fontWeight: "500",
          duration: 0.75,
          onComplete: () => {
            gsap.set(".preloader", {
              clipPath: "polygon(0 0, 100% 0, 100% 50%, 0 50%)",
            });
            gsap.set(".split-overlay", {
              clipPath: "polygon(0 50%, 100% 50%, 100% 100%, 0 100%)",
            });
          },
        },
        4.5
      )
      .to(
        ".container",
        {
          clipPath: "polygon(0% 48%, 100% 48%, 100% 52%, 0% 52%)",
          duration: 1,
        },
        5
      );

      // Animate tags again
      tags.forEach((tag, index) => {
        tl.to(
          tag.querySelectorAll("p .word"),
          {
            y: "100%",
            duration: 0.75,
          },
          5.5 + index * 0.1
        );
      });

      // Final animation sequence
      tl.to(
        [".preloader", ".split-overlay"],
        {
          y: (i) => (i === 0 ? "-50%" : "50%"),
          duration: 1,
        },
        6
      ).to(
        ".container",
        {
          clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
          duration: 1
        },
        6
      ).to(
        ".container .card",
        {
          clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
          duration: 0.75
        },
        6.25
      ).to(
        ".container .card h1 .char span",
        {
          y: "0%",
          duration: 0.75,
          stagger: 0.05,
        },
        6.5
      );
    };

    // Wait for fonts to load and DOM to be fully ready
    const cleanup = () => {
      // Kill any active GSAP animations when component unmounts
      gsap.killTweensOf(".preloader, .split-overlay, .container, .card, .intro-title, .outro-title, .tag");
    };

    // Use requestIdleCallback or setTimeout to ensure DOM is fully ready
    const timeoutId = setTimeout(initializeAnimation, 100);

    // Add event listener for page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        // Reinitialize animation when page becomes visible after being hidden
        cleanup();
        setTimeout(initializeAnimation, 100);
      }
    });

    return () => {
      clearTimeout(timeoutId);
      cleanup();
      document.removeEventListener('visibilitychange', cleanup);
    };
  }, []);

  return (
    <>
      <div className="preloader" ref={preloaderRef}>
        <div className="intro-title" ref={introTitleRef}>
          <h1>Creative Studio</h1>
        </div>
        <div className="outro-title" ref={outroTitleRef}>
          <h1>10</h1>
        </div>
      </div>
      <div className="split-overlay" ref={splitOverlayRef}>
        <div className="intro-title">
          <h1>Creative Studio</h1>
        </div>
        <div className="outro-title">
          <h1>10</h1>
        </div>
      </div>
      <div className="tags-overlay">
        <div className="tag tag-1"><p>CREATIVE</p></div>
        <div className="tag tag-2"><p>DESIGN</p></div>
        <div className="tag tag-3"><p>STUDIO</p></div>
      </div>

      <div className="container" ref={containerRef}>
        <div className="hero-img">
          <img src="/nordwood_main.jpg" alt="Hero" />
        </div>
      </div>
    </>
  );
};

export default Preloader;





* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "DM Sans", sans-serif;
}

img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

h1 {
  text-transform: uppercase;
  font-size: 6rem;
  font-weight: 600;
  line-height: 1;
}

p {
  text-transform: uppercase;
  font-size: 13px;
  font-weight: 500;
}

.preloader, 
.split-overlay, 
.tags-overlay {
  position: fixed;
  width: 100vw;
  height: 100vh;
}

.preloader,
.split-overlay {
  background-color: #0a0a0a;
  color: #fff;
}

.preloader,
.tags-overlay {
  z-index: 2;
}

.split-overlay {
  z-index: 1;
}

.intro-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  text-align: center;
}

.outro-title {
  position: absolute;
  top: 50%;
  left: calc(50% + 10rem);
  transform: translate(-50%, -50%);
}

.tag {
  position: absolute;
  width: max-content;
  color: #5a5a5a;
  overflow: hidden;
}

.tag-1 {
  top: 15%;
  left: 15%;
}

.tag-2 {
  bottom: 15%;
  left: 25%;
}

.tag-3 {
  bottom: 30%;
  right: 15%;
}

.container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  clip-path: polygon(0 48%, 0 48%, 0 52%, 0 52%);
  z-index: 2;
}

.hero-img {
  position: absolute;
  width: 100%;
  height: 100%;
}

.intro-title .char,
.outro-title .char {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.intro-title .char, 
.outro-title .char {
  margin-top: 0.75rem;
}

.intro-title .char span,
.outro-title .char span,
.tag .word {
  position: relative;
  display: inline-block;
  transform: translateY(-100%);
  will-change: transform;
}

.intro-title .first-char {
  transform-origin: top left;
}