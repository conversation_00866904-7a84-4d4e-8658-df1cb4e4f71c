import { useRef, useMemo } from 'react'
import { use<PERSON>rame, useThree } from '@react-three/fiber'
import * as THREE from 'three'

export default function NoiseFilter({ intensity = 0.075, speed = 2.0 }) {
  const { viewport, camera } = useThree()
  const meshRef = useRef()
  
  // Create a shader material for the noise effect
  const noiseMaterial = useMemo(() => {
    return new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        resolution: { value: new THREE.Vector2(1, 1) }
      },
      transparent: true,
      depthWrite: false,
      depthTest: false,
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          // Project vertices in clip space
          gl_Position = vec4(position.xy, 1.0, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec2 resolution;
        varying vec2 vUv;
        
        #define SPEED ${speed.toFixed(1)}
        #define INTENSITY ${intensity.toFixed(4)}
        #define MEAN 0.0
        #define VARIANCE 0.5
        
        float gaussian(float z, float u, float o) {
          return (1.0 / (o * sqrt(2.0 * 3.1415))) * exp(-(((z - u) * (z - u)) / (2.0 * (o * o))));
        }
        
        void main() {
          vec2 uv = vUv;
          
          // Generate noise
          float t = time * SPEED;
          float seed = dot(uv, vec2(12.9898, 78.233));
          float noise = fract(sin(seed) * 43758.5453 + t);
          noise = gaussian(noise, MEAN, VARIANCE * VARIANCE);
          
          // Apply noise as an overlay with transparency
          vec3 grainColor = vec3(noise);
          
          // Set the final color with transparency to allow scene to show through
          gl_FragColor = vec4(grainColor, INTENSITY);
        }
      `
    });
  }, [intensity, speed]);

  // Animate the noise
  useFrame((state) => {
    if (meshRef.current) {
      noiseMaterial.uniforms.time.value = state.clock.getElapsedTime();
      noiseMaterial.uniforms.resolution.value.set(
        state.size.width * state.viewport.dpr, 
        state.size.height * state.viewport.dpr
      );
    }
  });

  return (
    // Use a different approach with an orthographic camera and full-screen quad
    <mesh ref={meshRef} material={noiseMaterial} frustumCulled={false}>
      <planeGeometry args={[2, 2]} /> {/* Full clip space coordinates */}
    </mesh>
  );
}