import React, { useRef } from 'react';
import styles from './AboutOverlaySection.module.css';

const SECTION_IDS = ['section1', 'section2', 'section3', 'section4'];

export default function AboutOverlaySection({ open, onClose }) {
  const sectionRefs = {
    section1: useRef(null),
    section2: useRef(null),
    section3: useRef(null),
    section4: useRef(null),
  };

  if (!open) return null;
  return (
    <div className={styles.overlayBackdrop}>
      <div className={styles.overlayContent}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100vh', position: 'absolute', top: 0, left: 0, width: '100vw', pointerEvents: 'none', zIndex: 2001 }}>
          <button
            onClick={onClose}
            className={styles.closeButton}
            aria-label="Close About"
            style={{ position: 'static', pointerEvents: 'auto' }}
          >
            ×
          </button>
        </div>
        {/* Section 1: Custom content bottom left */}
        <section
          id="section1"
          ref={sectionRefs.section1}
          className={`${styles.sectionFlexWrapper} ${styles.section1Custom}`}
        >
          <div className={styles.section1ContentWrapper}>
            <div className={styles.section1SmallTexts}>
              <span>ABOUT</span>
              <span>V01</span>
              <span>FILE_ID<br/>#001</span>
              <span>IDENTITY<br/>CORE PRINCIPLE</span>
            </div>
            <div className={styles.section1AboutHeading}>
              Wir machen die digitale Welt zu einer<br />sinnvollen Verlängerung der Realität.
            </div>
          </div>
        </section>
        {/* Section 1.2: Spacer section */}
        <section
          id="section1-2"
          className={styles.sectionFlexWrapper}
          style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          <h2 style={{ fontSize: '2.5rem', color: '#fff', fontFamily: 'Inter, Arial, sans-serif', fontWeight: 600 }}>Section 1.2</h2>
        </section>
        {/* Section 2: Two-column layout */}
        <section
          id="section2"
          ref={sectionRefs.section2}
          className={`${styles.sectionFlexWrapper} ${styles.section2Custom}`}
        >
          <div className={styles.section2Left}>
            <div className={styles.section2Subheading}>
              #01 UNSERE VISION
            </div>
            <h2 className={styles.section2Heading}>
              Wir glauben, dass sich Technologie dem Menschen anpassen sollte – nicht umgekehrt. Unsere KI-Lösungen sind so gestaltet, dass sie sich nahtlos in Arbeitsabläufe einfügen, Entscheidungen erleichtern und echte Effizienz ermöglichen. Dabei geht es uns nicht um KI um der KI willen, sondern um intelligente Systeme, die den Arbeitsalltag verbessern und die digitale Welt zu einer sinnvollen Erweiterung der Realität machen.
            </h2>
          </div>
        </section>
        {/* Section 3: Centered cards AND bottom-left-aligned content */}
        <section
          id="section3"
          ref={sectionRefs.section3}
          className={`${styles.sectionFlexWrapper} ${styles.section3Custom}`}
        >
          {/* Headings above cards */}
          <div className={styles.section3Left}>
            <div className={styles.section3Subheading}>
              #02 UNSERE PHILOSOPHIE
            </div>
            <h2 className={styles.section3Heading}>
              KI als Baustein echter Transformation - präzise <br/>zusammengesetzt, mit Fokus auf Wirkung und Wachstum.
            </h2>
          </div>
          {/* Centered cards */}
          <div className={styles.section3CardWrapper}>
            <div className={styles.section3CardRow}>
              {/* Card 1 */}
              <div className={styles.section3Card}>
                <div className={styles.section3CardBarGraph}>
                  <img src="/card_bars_v1.svg" alt="Card 1 visual" style={{width: '100%', height: '80px', objectFit: 'contain'}} />
                </div>
                <div className={styles.section3CardSmallTexts}>
                  <span>BLOCK.ID</span>
                  <span>V01</span>
                  <span>BUILDING BLOCK</span>
                  <span>#01</span>
                </div>
                <div className={styles.section3CardHeading}>01 BLCK</div>
                <div className={styles.section3CardParagraph}>
                  Wir glauben nicht an KI als Spielerei – sondern als Werkzeug mit echtem Nutzen. Unsere Lösungen sind keine Proof-of-Concepts, sondern produktive Systeme, die Prozesse automatisieren, Entscheidungen beschleunigen und Kosten senken.
                </div>
              </div>
              {/* Card 2 */}
              <div className={styles.section3Card}>
                <div className={styles.section3CardBarGraph}>
                  <img src="/card_bars_v2.svg" alt="Card 2 visual" style={{width: '100%', height: '80px', objectFit: 'contain'}} />
                </div>
                <div className={styles.section3CardSmallTexts}>
                  <span>BLOCK.ID</span>
                  <span>V02</span>
                  <span>BUILDING BLOCK</span>
                  <span>#02</span>
                </div>
                <div className={styles.section3CardHeading}>02 BLCK</div>
                <div className={styles.section3CardParagraph}>
                KI ist kein Gimmick, das man einfach „draufsetzt“. <br/>Wir analysieren zuerst Ihre bestehenden Abläufe, identifizieren Automatisierungspotenzial und bauen darauf Lösungen, die wirklich greifen – nahtlos integriert, individuell angepasst und nachhaltig wirksam.
                </div>
              </div>
              {/* Card 3 */}
              <div className={styles.section3Card}>
                <div className={styles.section3CardBarGraph}>
                  <img src="/card_bars_v3.svg" alt="Card 3 visual" style={{width: '100%', height: '80px', objectFit: 'contain'}} />
                </div>
                <div className={styles.section3CardSmallTexts}>
                  <span>BLOCK.ID</span>
                  <span>V03</span>
                  <span>BUILDING BLOCK</span>
                  <span>#03</span>
                </div>
                <div className={styles.section3CardHeading}>03 BLCK</div>
                <div className={styles.section3CardParagraph}>
                  Wir entwickeln Lösungen mit Weitblick: modular, wartbar und zukunftssicher. Denn echte Transformation entsteht nicht durch punktuelle Tools – sondern durch Systeme, die mit Ihrem Business mitwachsen.
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* Section 4: Block1Card clone, centered */}
        <section
          id="section4"
          ref={sectionRefs.section4}
          className={styles.sectionFlexWrapper}
          style={{
            minHeight: '90vh',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            position: 'relative',
            paddingTop: '0px',
          }}
        >
          {/* Headings above cards */}
          <div className={styles.section4Left}>
            <div className={styles.section4Subheading}>
              #03 UNSER TEAM
            </div>
            <h2 className={styles.section4Heading}>
              Wir bauen die Zukunft - Block für Block.
            </h2>
          </div>
          <div className={styles.section4CardWrapper}>
            {/* Card 1 */}
            <div className={styles.section4Container}>
              <span className={styles.section4TopLabel}>#1</span>
              <div className={styles.section4Card}>
                <h2 className={styles.section4Headline}>Philipp Fuchs</h2>
                <div className={styles.section4HeaderRow}>
                  <div className={styles.section4HeaderLeft}>
                    <span>PHILIPP FUCHS<br/>GRAZ, AUT<br/>NUM ID: X001</span>
                  </div>
                  <div className={styles.section4HeaderCenter}>
                    <span>FOUNDER & CEO</span>
                  </div>
                  <div className={styles.section4HeaderRight}>
                    <span>LAB #1</span>
                  </div>
                </div>
                <div className={styles.section4DottedDivider}>
                  {Array.from({length: 30}).map((_,i) => <span key={i}>.</span>)}
                </div>
                <div className={styles.section4BodyRow}>
                  <div className={styles.section4BodyLeft}>
                    <div className={styles.section4ProcessType}>PROFIL ID #001</div>
                    <svg className={styles.section4Barcode} width="100" height="60" viewBox="0 0 100 90">
                      <rect x="0" y="0" width="2" height="125" fill="#000"/>
                      <rect x="4" y="0" width="5" height="90" fill="#000"/>
                      <rect x="12" y="0" width="4" height="90" fill="#000"/>
                      <rect x="19" y="0" width="4" height="90" fill="#000"/>
                      <rect x="26" y="0" width="4" height="90" fill="#000"/>
                      <rect x="29" y="0" width="1" height="90" fill="#000"/>
                      <rect x="33" y="0" width="3" height="90" fill="#000"/>
                      <rect x="40" y="0" width="2" height="90" fill="#000"/>
                      <rect x="46" y="0" width="1" height="90" fill="#000"/>
                      <rect x="50" y="0" width="2" height="90" fill="#000"/>
                      <rect x="57" y="0" width="2" height="90" fill="#000"/>
                      <rect x="62" y="0" width="1" height="90" fill="#000"/>
                      <rect x="66" y="0" width="2" height="90" fill="#000"/>
                      <rect x="73" y="0" width="2" height="90" fill="#000"/>
                      <rect x="79" y="0" width="1" height="90" fill="#000"/>
                      <rect x="83" y="0" width="2" height="90" fill="#000"/>
                      <rect x="90" y="0" width="1" height="90" fill="#000"/>
                    </svg>
                  </div>
                  <div className={styles.section4BodyRight}>
                    <div className={styles.section4LogoRow}>
                      <span className={styles.section4Logo}>BLCKS<sup className={styles.section4Superscript}>®</sup></span>
                    </div>
                    <div className={styles.section4RefId}>BLCKS AI<br/>REFERENCE ID 2023241</div>
                    <div className={styles.section4Principle}>AI AUTOMATION &<br/>DEVELOPMENT COMPANY</div>
                  </div>
                </div>
              </div>
            </div>
            {/* Card 2 */}
            <div className={styles.section4Container}>
              <span className={styles.section4TopLabel}>#2</span>
              <div className={styles.section4Card}>
                <h2 className={styles.section4Headline}>Maximilian Dirnberger</h2>
                <div className={styles.section4HeaderRow}>
                  <div className={styles.section4HeaderLeft}>
                    <span>MAXIMILIAN DIRNBERGER<br/>GRAZ, AUT<br/>NUM ID: X002</span>
                  </div>
                  <div className={styles.section4HeaderCenter}>
                    <span>HEAD OF SALES</span>
                  </div>
                  <div className={styles.section4HeaderRight}>
                    <span>LAB #2</span>
                  </div>
                </div>
                <div className={styles.section4DottedDivider}>
                  {Array.from({length: 30}).map((_,i) => <span key={i}>.</span>)}
                </div>
                <div className={styles.section4BodyRow}>
                  <div className={styles.section4BodyLeft}>
                    <div className={styles.section4ProcessType}>PROFIL ID #002</div>
                    <svg className={styles.section4Barcode} width="100" height="60" viewBox="0 0 100 90">
                      <rect x="0" y="0" width="2" height="125" fill="#000"/>
                      <rect x="4" y="0" width="5" height="90" fill="#000"/>
                      <rect x="12" y="0" width="4" height="90" fill="#000"/>
                      <rect x="19" y="0" width="4" height="90" fill="#000"/>
                      <rect x="26" y="0" width="4" height="90" fill="#000"/>
                      <rect x="29" y="0" width="1" height="90" fill="#000"/>
                      <rect x="33" y="0" width="3" height="90" fill="#000"/>
                      <rect x="40" y="0" width="2" height="90" fill="#000"/>
                      <rect x="46" y="0" width="1" height="90" fill="#000"/>
                      <rect x="50" y="0" width="2" height="90" fill="#000"/>
                      <rect x="57" y="0" width="2" height="90" fill="#000"/>
                      <rect x="62" y="0" width="1" height="90" fill="#000"/>
                      <rect x="66" y="0" width="2" height="90" fill="#000"/>
                      <rect x="73" y="0" width="2" height="90" fill="#000"/>
                      <rect x="79" y="0" width="1" height="90" fill="#000"/>
                      <rect x="83" y="0" width="2" height="90" fill="#000"/>
                      <rect x="90" y="0" width="1" height="90" fill="#000"/>
                    </svg>
                  </div>
                  <div className={styles.section4BodyRight}>
                    <div className={styles.section4LogoRow}>
                      <span className={styles.section4Logo}>BLCKS<sup className={styles.section4Superscript}>®</sup></span>
                    </div>
                    <div className={styles.section4RefId}>BLCKS AI<br/>REFERENCE ID 2023242</div>
                    <div className={styles.section4Principle}>AI AUTOMATION &<br/>DEVELOPMENT COMPANY</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* Section 5: Placeholder section */}
        <section
          id="section5"
          className={styles.sectionFlexWrapper}
          style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          <h2 style={{ fontSize: '2.5rem', color: '#fff', fontFamily: 'Inter, Arial, sans-serif', fontWeight: 600 }}>Section 5</h2>
        </section>
      </div>
    </div>
  );
}