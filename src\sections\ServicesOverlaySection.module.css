@font-face {
  font-family: 'Supply';
  src: url('/fonts/Supply-Regular.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s;
}

.layout {
  display: flex;
  flex-direction: row;
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
}

.menu {
  width: 260px;
  min-width: 200px;
  background: transparent;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 60px 0 0 100px;
  position: relative;
}

.menu::after {
  content: "";
  position: absolute;
  top: 0;
  right: -25px; /* 50px from the left edge of the menu */
  width: 1px;
  height: 100%;
  background: #222;
  border-radius: 1px;
  z-index: 1;
}
.menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.menuItem,
.menuItemActive {
  font-family: 'Supply', Arial, sans-serif;
  font-size: 1.15rem;
  margin-bottom: 18px;
  color: #878782;
  font-weight: 400;
  letter-spacing: 0.01em;
  cursor: pointer;
  transition: color 0.2s;
  display: flex;
  align-items: center;
}
.menuItemActive {
  color: #fff;
  font-weight: 400;
}
.menuDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #fff;
  display: inline-block;
  margin-right: 10px;
}

.contentRight {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 60px 0 0 60px;
  position: relative;
  font-family: 'Inter', Arial, sans-serif;
}
.logoRow {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
}
.logoIcon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  margin-top: 1px;
}
.logoText {
  font-family: 'Supply', 'Inter', Arial, sans-serif;
  font-size: 1.5rem;
  color: #878782;
  font-weight: 400;
  letter-spacing: 0.01em;
}
.heading {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 3.50rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 50px;
  line-height: 1.10;
  max-width: 1100px;
}
.paragraph {
  font-size: 1.15rem;
  color: #a5a5a1;
  max-width: 700px;
  margin-bottom: 36px;
  line-height: 1.50;
  font-family: 'Inter', Arial, sans-serif;
  max-width: 1100px;
}
.visual3d {
  position: absolute;
  right: 60px;
  bottom: 40px;
  width: 320px;
  height: 220px;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
.visual3dImg {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.closeButton {
  position: absolute;
  top: 1.2rem;
  right: 1.5rem;
  background: none;
  border: none;
  color: #fff;
  font-size: 2.2rem;
  cursor: pointer;
  z-index: 10;
  transition: color 0.2s;
}
.closeButton:hover {
  color: #ff5a5a;
}

.sectionHeading {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 1.75rem;
  font-weight: 700;
  color: #000;
  margin: 0 0 32px 0;
  line-height: 1.1;
  text-align: left;
  width: 100%;
}

.bulletColumnsWrapper {
  display: flex;
  flex-direction: row;
  justify-content: left;
  align-items: flex-start;
  gap: 48px;
  margin-bottom: 36px;
  width: 100%;
}

.bulletColumn {
  list-style: disc inside;
  padding: 0 18px;
  margin: 0;
  font-size: 1.1rem;
  color: #000;
  min-width: 200px;
  font-family: 'Inter', Arial, sans-serif;
}

.screenshotWrapper {
  display: flex;
  justify-content: left;
  align-items: center;
  width: 100%;
  margin-top: 18px;
}

.screenshotImg {
  max-width: 800px;
  width: 100%;
  height: auto;
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.10);
}

.sectionPlaceholder {
  font-size: 1.5rem;
  color: #000;
  margin-top: 80px;
  text-align: center;
  width: 100%;
}

.contentRightScrollable {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 60px 0 0 60px;
  position: relative;
  overflow-y: auto;
  max-height: 100vh;
  padding-right: 32px;
  margin-left: 100px;
  scrollbar-width: none; /* Firefox */
}
.contentRightScrollable::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.sectionNavSpacer {
  height: 32px;
  width: 100%;
}


.productCardsGridWrapper {
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-bottom: 2rem;
  margin-left: 0;
  margin-right: 0;
}

.productCardsGrid {
  display: flex;
  flex-direction: row;
  gap: 2rem;
  will-change: transform;
  user-select: none;
  cursor: grab;
  /* Prevent wrapping */
  flex-wrap: nowrap;
}

.productCard {
  width: 250px;
  height: 250px;
  flex: 0 0 250px;
  border: 1px solid #222;
  border-radius: 0px;
  padding: 32px 28px 24px 28px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end; /* Align content to bottom */
  position: relative; /* For icon positioning */
}

.productCardIcon {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.productCardHeading {
  font-size: 1.15rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 12px;
  font-family: 'Inter', Arial, sans-serif;
}

.productCardDesc {
  font-size: 0.90rem;
  color: #a5a5a1;
  font-family: 'Inter', Arial, sans-serif;
  line-height: 1.4;
}

@media (max-width: 1100px) {
  .productCardsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }
}

@media (max-width: 700px) {
  .productCardsGrid {
    grid-template-columns: 1fr;
    gap: 18px;
  }
}

.productCardsHeading {
  font-size: 1.25rem;
  font-weight: 500;
  color: #fff;
  margin-bottom: 0px;
  margin-top: 0px;
  text-align: left;
  font-family: 'Inter', Arial, sans-serif;
}

@media (max-width: 700px) {
  .productCardsHeading {
    font-size: 1.3rem;
    margin-bottom: 16px;
  }
}


.productCardsNavRow {
  display: flex;
  justify-content: flex-start;
  gap: 1rem;
  margin-bottom: 1.25rem;
  margin-top: 2rem;
}

.productCardsNavBtn {
  border: 1px solid #222;
  background: black;
  color: #000;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  font-size: 1.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.productCardsGrid::-webkit-scrollbar {
  display: none;
}


.aiExpertHelpSection {
  width: 100%;
  margin: 100px 0 0 0;
  display: flex;
  flex-direction: column;
  align-items: left;
}

.aiExpertHelpHeadline {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 2rem;
  font-weight: 700;
  color: #878782;
  text-align: left;
  margin-bottom: 50px;
  line-height: 1.30;
  max-width: 1100px;
}

.aiExpertHelpColumns {
  display: flex;
  flex-direction: row;
  justify-content: left;
  align-items: flex-start;
  gap: 56px;
  width: 100%;
  max-width: 1200px;
}

.aiExpertHelpCol {
  display: flex;
  flex-direction: column;
  min-width: 260px;
  max-width: 340px;
}

.aiExpertHelpColTitle {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 1.25rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 12px;
}

.aiExpertHelpColText {
  font-size: 1rem;
  color: #a5a5a1;
  line-height: 1.35;
  font-family: 'Inter', Arial, sans-serif;
}

@media (max-width: 1100px) {
  .aiExpertHelpColumns {
    flex-direction: column;
    gap: 32px;
    align-items: center;
  }
  .aiExpertHelpCol {
    min-width: 220px;
    max-width: 100%;
    width: 100%;
  }
}

.statsCardsSection {
  width: 100%;
  margin: 150px 0 0 0;
  display: flex;
  flex-direction: column;
  align-items: left;
}

.statsCardsRowWrapper {
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-bottom: 2rem;
}

.statsCardsRow {
  display: flex;
  flex-direction: row;
  gap: 2rem;
  will-change: transform;
  user-select: none;
  cursor: grab;
  flex-wrap: nowrap;
}

.statsCard {
  width: 750px;
  flex: 0 0 750px;
  background: #d9d9d9;
  border-radius: 12px;
  padding: 48px 40px 36px 40px;
  height: 450px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}

.statsCardTopRow {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 32px;
  margin-bottom: 18px;
}

.statsCardPercentRow {
  display: flex;
  align-items: flex-start;
  font-size: 7rem;
  font-weight: 700;
  color: #000;
  margin-bottom: 18px;
}

.statsCardPercent {
  font-size: 8rem;
  font-weight: 900;
  line-height: 1;
  letter-spacing: -0.04em;
}

.statsCardPercentSymbol {
  font-size: 3rem;
  font-weight: 900;
  margin-left: 2px;
  margin-top: 8px;
  letter-spacing: -0.04em;
}

.statsCardDotGrid {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-rows: repeat(8, 1fr);
  grid-template-columns: repeat(10, 1fr);
  gap: 12px;
}

.statsCardDotGridRow {
  display: contents;
}

.statsCardDot,
.statsCardDotFilled {
  width: 24px;
  height: 24px;
  aspect-ratio: 1 / 1;
  border-radius: 50%;
  display: block;
  margin: auto;
}

.statsCardDot {
  background: #e2e2e2;
}

.statsCardDotFilled {
  background: #000;
}

.statsCardDonutWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statsCardDonutSvg {
  width: 100%;
  height: 100%;
  display: block;
}

.statsCardDesc {
  font-size: 1rem;
  color: #000000;
  margin-bottom: 36px;
  font-family: 'Inter', Arial, sans-serif;
  font-weight: 700;
  max-width: 200px;
}

.statsCardSource {
  font-size: 0.85rem;
  color: #000000;
  background: #fff;
  border-radius: 0px;
  padding: 7px 18px;
  font-family: 'Supply', Arial, sans-serif;
  font-weight: 500;
  letter-spacing: 0.01em;
  margin-top: 0;
}

.statsCardPlaceholder {
  width: 100%;
  height: 100%;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bbb;
  font-size: 1.5rem;
  font-family: 'Inter', Arial, sans-serif;
}

@media (max-width: 1200px) {
  .statsCardsRow {
    flex-direction: column;
    gap: 32px;
    align-items: center;
  }
  .statsCard {
    min-width: 220px;
    max-width: 100%;
    width: 100%;
  }
}

.sectionBlock {
  width: 100%;
  margin-bottom: 80px;
}

.xaasColumnsWrapper {
  display: flex;
  flex-direction: row;
  justify-content: left;
  align-items: flex-start;
  gap: 56px;
  width: 100%;
  max-width: 1200px;
  margin-top: 36px;
}

.xaasCol {
  display: flex;
  flex-direction: column;
  min-width: 260px;
  max-width: 340px;
}

.xaasColTitle {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 1.25rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 12px;
  margin-top: 24px;
}

.xaasColText {
  font-size: 1rem;
  color: #a5a5a1;
  line-height: 1.35;
  font-family: 'Inter', Arial, sans-serif;
}

@media (max-width: 1100px) {
  .xaasColumnsWrapper {
    flex-direction: column;
    gap: 32px;
    align-items: center;
  }
  .xaasCol {
    min-width: 220px;
    max-width: 100%;
    width: 100%;
  }
}

.xaasFeatureCardsSection {
  width: 100%;
  margin: 56px 0 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.xaasFeatureCardsRowWrapper {
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-bottom: 2rem;
}

.xaasFeatureCardsRow {
  display: flex;
  flex-direction: row;
  gap: 40px; /* Match the JS gap */
  will-change: transform;
  user-select: none;
  cursor: grab;
  flex-wrap: nowrap;
  align-items: flex-end; /* Align cards to the bottom */
}

.xaasFeatureCardsNavRow {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  gap: 1rem;
  margin-bottom: 2rem;
  margin-top: 0rem;
}

.xaasFeatureCard {
  border-radius: 0px;
  border: 1px solid #222;
  padding: 36px 32px 28px 32px;
  min-width: 300px;
  max-width: 300px;
  flex: 1 1 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-height: 300px; /* Increase card height */
}

.xaasFeatureCardTitle {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 1.15rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 18px;
  text-align: left;
  width: 100%;
}

.xaasFeatureCardImg {
  width: 100%;
  max-width: 340px;
  height: auto;
  border-radius: 18px;
  object-fit: contain;
  background: #a5a5a1;
}

.xaasFeatureCardDesc {
  color: #a5a5a1;
  font-size: 0.90 !important;
  line-height: 1.4;
}

.xaasFeatureCardList {
  color: #a5a5a1;
}

.xaasFeatureCardContent {
  margin-top: auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

@media (max-width: 1200px) {
  .xaasFeatureCardsRow {
    flex-direction: column;
    gap: 32px;
    align-items: center;
  }
  .xaasFeatureCard {
    min-width: 220px;
    max-width: 100%;
    width: 100%;
  }
}

.xaasLargeTextSection {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 2rem;
  font-weight: 700;
  color: #878782;
  margin: 64px 0 0px 0;
  line-height: 1.30;
  max-width: 1100px;
  text-align: left;
  align-self: center;
  letter-spacing: 0.01em;
}

@media (max-width: 900px) {
  .xaasLargeTextSection {
    font-size: 1.3rem;
    max-width: 98vw;
    margin: 36px 0 0 0;
  }
}

.xaasPlansSection {
  width: 100%;
  border-radius: 0;
  margin: 64px 0 0 0;
  padding: 56px 0 80px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.xaasPlansHeaderRow {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 48px;
}

.xaasPlansTitle {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 2.2rem;
  font-weight: 900;
  color: #fff;
  margin: 0 0 8px 0;
  text-align: left;
}

.xaasPlansSubtitle {
  font-size: 1.5rem;
  color: #a0a0a0;
  font-family: 'Inter', Arial, sans-serif;
  text-align: left;
  font-weight: 600;
}

.xaasPlansCardsRow {
  display: flex;
  flex-direction: row;
  gap: 48px;
  width: 100%;
  justify-content: flex-start;
}

.xaasPlanCard {
  background: #111;
  color: #fff;
  border-radius: 48px;
  padding: 44px 38px 28px 38px;
  min-width: 340px;
  max-width: 420px;
  flex: 1 1 340px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.xaasPlanCardTitle {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 1.5rem;
  font-weight: 900;
  color: #000;
  margin-bottom: 18px;
}

.xaasPlanCardDesc {
  font-size: 1.13rem;
  color: #000;
  margin-bottom: 22px;
  font-family: 'Inter', Arial, sans-serif;
}

.xaasPlanCardList {
  list-style: disc inside;
  color: #000;
  font-size: 1.13rem;
  margin-bottom: 32px;
  padding-left: 0;
}
.xaasPlanCardList li {
  margin-bottom: 10px;
  padding-left: 0;
  position: relative;
}
.xaasPlanCardList li::marker {
  color: #ffe600;
  font-size: 1.2em;
}

.xaasPlanCardButton {
  width: 100%;
  background: #ffe600;
  color: #000;
  font-family: 'Inter', Arial, sans-serif;
  font-size: 1.18rem;
  font-weight: 900;
  border: none;
  border-radius: 0 0 32px 32px;
  padding: 18px 0;
  margin-top: auto;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.xaasPlanCardButton:hover {
  background: #fff200;
  color: #000;
}

@media (max-width: 1100px) {
  .xaasPlansCardsRow {
    flex-direction: column;
    gap: 32px;
    align-items: center;
  }
  .xaasPlanCard {
    min-width: 220px;
    max-width: 100%;
    width: 100%;
  }
}

.profServicesHeading {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 3.5rem;
  font-weight: 700;
  color: #fff;
  margin: 36px 0 0 0;
  line-height: 1.05;
  max-width: 1100px;
  text-align: left;
  letter-spacing: 0.01em;
}

.profServicesSubheading {
  font-size: 1.25rem;
  color: #fff;
  font-family: 'Inter', Arial, sans-serif;
  margin: 64px 0 36px 0;
  text-align: left;
  font-weight: 500;
}

@media (max-width: 900px) {
  .profServicesHeading {
    font-size: 2rem;
    max-width: 98vw;
    margin: 24px 0 0 0;
  }
  .profServicesSubheading {
    font-size: 1.1rem;
    margin: 18px 0 0 0;
  }
  .menu {
    padding-left: 24px;
  }
  .contentRightScrollable {
    margin-left: 0;
    padding-left: 16px;
  }
}

.profServicesOfferingsRow {
  display: flex;
  flex-direction: row;
  gap: 56px;
  justify-content: flex-start;
  align-items: flex-start;
  margin: 36px 0 0 0;
  width: 100%;
  max-width: 1100px;
}

.profServicesOfferingsList {
  list-style: disc inside;
  font-size: 1.25rem;
  color: #000;
  font-family: 'Inter', Arial, sans-serif;
  margin: 0;
  padding: 0 24px 0 0;
  min-width: 220px;
}

.profServicesLargeText {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 2rem;
  font-weight: 700;
  color: #878782;
  margin: 100px 0 100px 0;
  line-height: 1.30;
  max-width: 1100px;
  text-align: left;
  letter-spacing: 0.01em;
}

@media (max-width: 1100px) {
  .profServicesOfferingsRow {
    flex-direction: column;
    gap: 18px;
    max-width: 98vw;
  }
  .profServicesOfferingsList {
    min-width: 0;
    font-size: 1.05rem;
    padding: 0 0 0 0;
  }
  .profServicesLargeText {
    font-size: 1.2rem;
    max-width: 98vw;
    margin: 36px 0 0 0;
  }
}

.profServicesProcessSection {
  width: 100%;
  margin: 64px 0 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.profServicesProcessRow {
  display: flex;
  flex-direction: row;
  gap: 40px;
  width: 100%;
  justify-content: center;
}

.profServicesProcessCard {
  background: #f6f6f6;
  border-radius: 32px;
  padding: 36px 32px 28px 32px;
  min-width: 420px;
  max-width: 540px;
  flex: 1 1 420px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  box-shadow: 0 2px 12px rgba(0,0,0,0.03);
}

.profServicesProcessStepNum {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 3.2rem;
  font-weight: 900;
  color: #bdbdbd;
  margin-bottom: 0.2em;
}

.profServicesProcessStepTitle {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 2.8rem;
  font-weight: 900;
  color: #000;
  margin-bottom: 1.2em;
}

.profServicesProcessPhasesRow {
  display: flex;
  flex-direction: row;
  gap: 48px;
  margin-bottom: 24px;
}

.profServicesProcessPhase {
  font-size: 1.1rem;
  color: #000;
  font-family: 'Inter', Arial, sans-serif;
  margin-right: 12px;
}

.profServicesProcessPhaseSub {
  color: #bdbdbd;
  font-size: 1rem;
  font-weight: 400;
}

.profServicesProcessBarRow {
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 18px;
  gap: 12px;
}

.profServicesProcessBar {
  height: 14px;
  background: #111;
  border-radius: 6px;
  display: inline-block;
  min-width: 80px;
}

.profServicesProcessBarDots {
  display: flex;
  flex-direction: row;
  gap: 16px;
  margin-left: 18px;
}

.profServicesProcessDot {
  width: 10px;
  height: 10px;
  background: #000;
  border-radius: 2px;
  display: inline-block;
}

@media (max-width: 1200px) {
  .profServicesProcessRow {
    flex-direction: column;
    gap: 32px;
    align-items: center;
  }
  .profServicesProcessCard {
    min-width: 220px;
    max-width: 100%;
    width: 100%;
  }
}

.profServicesFinalText {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 2.1rem;
  font-weight: 900;
  color: #000;
  margin: 64px 0 0 0;
  line-height: 1.15;
  max-width: 1100px;
  text-align: left;
  letter-spacing: 0.01em;
}

@media (max-width: 900px) {
  .profServicesFinalText {
    font-size: 1.2rem;
    max-width: 98vw;
    margin: 36px 0 0 0;
  }
}

.statsCardVisualLayout {
  width: 100%;
  height: 100%;
  position: relative;
}

.statsCardVisualWhite {
  width: 100%;
  height: 100%;
  background: #fff;
  position: relative;
}

.statsCardVisualGray {
  width: 220px;
  height: 220px;
  background: #e2e2e2;
  position: absolute;
  bottom: -16px;
  right: -16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statsCardVisualBlack {
  width: 180px;
  height: 180px;
  background: #000;
}

@media (max-width: 900px) {
  .statsCardRightCol {
    min-width: 0;
    width: 100%;
    justify-content: center;
  }
  .statsCardDonutWrapper {
    width: 80px;
    height: 80px;
  }
  .statsCardVisualLayout {
    min-width: 80px;
    min-height: 60px;
  }
  .statsCardDotGrid {
    gap: 6px;
  }
  .statsCardDotGridRow {
    gap: 6px;
  }
  .statsCardVisualWhite {
    width: 60px;
    height: 40px;
    margin-right: 8px;
  }
  .statsCardVisualGray {
    width: 30px;
    height: 30px;
    right: 0;
    top: 20px;
  }
  .statsCardVisualBlack {
    width: 20px;
    height: 20px;
  }
}

.statsCardsNavRow {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.statsCardsNavBtn {
  border: 1px solid #222;
  color: #000;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  font-size: 1.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}


.statsCardFlexRow {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 100%;
  width: 100%;
}

.statsCardLeftCol {
  width: 300px;
  flex: 0 0 300px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  align-items: flex-start;
  padding: 0 0 0 8px;
}

.statsCardBottomGroup {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.statsCardRightCol {
  width: 400px;
  min-width: 400px;
  max-width: 400px;
  height: 400px;
  min-height: 400px;
  max-height: 400px;
  flex: 0 0 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 900px) {
  .statsCardFlexRow {
    flex-direction: column;
    align-items: flex-start;
    height: auto;
  }
  .statsCardLeftCol, .statsCardRightCol {
    min-width: 0;
    width: 100%;
    padding: 0;
  }
  .statsCardRightCol {
    justify-content: center;
    margin-top: 18px;
  }
  .statsCardPercentRow {
    font-size: 2.8rem;
    margin-bottom: 10px;
  }
  .statsCardDesc {
    font-size: 1rem;
    margin-bottom: 10px;
  }
  .statsCardSource {
    font-size: 0.95rem;
    padding: 5px 10px;
  }
}


.statsCardsRow::-webkit-scrollbar {
  display: none;
} 

.horizontalDivider {
  position: relative;
  height: 0;
  border: none;
  border-top: 1px solid #222;
  margin: 100px 0 100px 0px;
  left: 0;
  width: 100%;
} 

.xaasFeatureCardsNavBtn {
  border: 1px solid #222;
  background: transparent;
  color: #000;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  font-size: 1.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
} 

.profServicesFeatureCardsRowWrapper {
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-bottom: 2rem;
}

.profServicesFeatureCardsRow {
  display: flex;
  flex-direction: row;
  gap: 40px;
  will-change: transform;
  user-select: none;
  cursor: grab;
  flex-wrap: nowrap;
  align-items: flex-end;
}

.profServicesFeatureCardsNavRow {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  gap: 1rem;
  margin-bottom: 2rem;
  margin-top: 0rem;
} 

.profFeatureCard {
  width: 350px;
  height: 350px;
  flex: 0 0 350px;
  border: 1px solid #111;
  padding: 32px 28px 24px 28px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  position: relative;
}

.profFeatureCardIcon {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.profFeatureCardTitle {
  font-size: 1.15rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 12px;
  font-family: 'Inter', Arial, sans-serif;
}

.profFeatureCardDesc {
  font-size: 0.90rem;
  color: #a5a5a1;
  font-family: 'Inter', Arial, sans-serif;
  line-height: 1.4;
  margin-bottom: 12px;
}

.profFeatureCardList {
  color: #a5a5a1;
  font-size: 0.90rem;
  margin-bottom: 0;
  padding-left: 18px;
  list-style: disc inside;
  font-family: 'Inter', Arial, sans-serif;
}

.profFeatureCardList li {
  margin-bottom: 8px;
} 

.profFeatureCardBottomRow {
  display: flex;
  flex-direction: row;
  gap: 40px;
  will-change: transform;
  user-select: none;
  cursor: grab;
  flex-wrap: nowrap;
  align-items: flex-end;
  width: 100%;
  margin-top: 32px;
  margin-bottom: 2rem;
}

.profFeatureCardBottom {
  width: 750px;
  height: 450px;
  flex: 0 0 750px;
  background: #d9d9d9;
  border-radius: 12px;
  padding: 48px 40px 36px 40px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  position: relative;
  border: none;
}

.profFeatureCardBottomHeader {
  position: absolute;
  top: 12px;
  left: 40px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  z-index: 2;
}

.profFeatureCardBottomStepNum {
  font-size: 6rem;
  font-weight: 900;
  color: #878782;
  margin-bottom: 0;
  letter-spacing: -0.04em;
  font-family: 'Inter', Arial, sans-serif;
}

.profFeatureCardBottomTitle {
  font-size: 4rem;
  font-weight: 900;
  letter-spacing: -0.04em;
  color: #111;
  margin-bottom: 0;
  margin-top: -30px;
  font-family: 'Inter', Arial, sans-serif;
}

.profFeatureCardBottomPhasesRow {
  display: flex;
  flex-direction: row;
  gap: 48px;
  margin-bottom: 24px;
}

.profFeatureCardBottomPhase {
  font-size: 1rem;
  color: #000;
  font-family: 'Inter', Arial, sans-serif;
  margin-right: 12px;
  font-weight: 500;
}

.profFeatureCardBottomPhaseSub {
  color: #878782;
  font-size: 1rem;
  font-weight: 500;
}

.profFeatureCardBottomBarRow {
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 18px;
  gap: 12px;
}

.profFeatureCardBottomBar {
  height: 14px;
  background: #111;
  border-radius: 6px;
  display: inline-block;
  min-width: 80px;
}

.profFeatureCardBottomBarDots {
  display: flex;
  flex-direction: row;
  gap: 16px;
  margin-left: 18px;
}

.profFeatureCardBottomDot {
  width: 10px;
  height: 10px;
  background: #000;
  border-radius: 2px;
  display: inline-block;
}

.profFeatureCardBottomRowWrapper {
  width: 100%;
  overflow-x: hidden;
  position: relative;
  margin-bottom: 24px;
}

.profFeatureCardBottomNavRow {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  gap: 1rem;
  margin-bottom: 12px;
  margin-top: 0;
}

.profFeatureCardBottomNavBtn {
  border: 1.5px solid #222;
  background: #fff;
  color: #111;
  border-radius: 8px;
  width: 44px;
  height: 44px;
  font-size: 1.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.profFeatureCardBottomNavBtn:hover {
  background: #f5f5f5;
  color: #000;
  border-color: #222;
}

.profServicesProcessRowWrapper {
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-bottom: 2rem;
}

.profServicesProcessNavRow {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  gap: 1rem;
  margin-bottom: 2rem;
  margin-top: 0;
} 