import { Canvas } from '@react-three/fiber'
import { useTexture, OrbitControls } from '@react-three/drei'
import { Suspense, useEffect, useRef, useState } from 'react'
import Lenis from 'lenis'
import './App.css'
import Model from './components/3d_bg.jsx'
import InversionLens from './components/InversionLens.jsx'
import NoiseFilter from './components/NoiseFilter.jsx'
import { useThree } from '@react-three/fiber'

function Background() {
  // Simple light background
  const texture = useTexture('/blcks_simple_bg_light.jpg')
  return <primitive object={texture} attach="background" />
}

// Create a separate component for the noise filter
function OverlayNoiseFilter() {
  const { camera } = useThree()
  
  // Position the noise filter directly in front of the camera
  useEffect(() => {
    // This ensures the filter is always in front of the camera
    camera.layers.enable(31)
  }, [camera])
  
  return (
    <NoiseFilter intensity={0.10} speed={0.8} />
  )
}

function App() {
  const scrollRef = useRef(null);
  const [activeSection, setActiveSection] = useState("hero");

  // Initialize Lenis smooth scrolling
  useEffect(() => {
    const lenis = new Lenis({
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
      direction: 'vertical',
      gestureDirection: 'vertical',
      smooth: true,
      smoothTouch: false,
      touchMultiplier: 2,
      wrapper: scrollRef.current,
      content: scrollRef.current
    });

    // Handle section changes on scroll
    const handleScroll = () => {
      const sections = document.querySelectorAll('.section');
      const scrollPosition = scrollRef.current.scrollTop;
      
      sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        
        if (
          scrollPosition >= sectionTop - 300 &&
          scrollPosition < sectionTop + sectionHeight - 300
        ) {
          setActiveSection(section.getAttribute('id'));
        }
      });
    };

    // Add scroll event listener
    scrollRef.current.addEventListener('scroll', handleScroll);

    // Set up RAF for Lenis
    function raf(time) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }
    requestAnimationFrame(raf);

    // Cleanup
    return () => {
      lenis.destroy();
      scrollRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Navigation click handler
  const handleNavClick = (sectionId) => {
    const section = document.getElementById(sectionId);
    if (section && scrollRef.current) {
      scrollRef.current.scrollTo({
        top: section.offsetTop,
        behavior: 'smooth'
      });
    }
  };

  return (
    <>
      {/* Fixed 3D Background */}
      <div className="background-image"></div>

      <div className="canvas-wrapper">
        <Canvas 
          camera={{ position: [0, 5, 5], fov: 45 }}
          gl={{ 
            alpha: true,           // Enable transparency
            antialias: true,       // Enable antialiasing
            powerPreference: 'high-performance' // Optimize for performance
          }}
        >
          <Suspense fallback={null}>
            {/*<ambientLight intensity={0.5} />
            <directionalLight 
              position={[5, 10, 5]} 
              intensity={1} 
              castShadow 
            />
            <pointLight position={[-5, 5, -5]} intensity={0.5} />
            <Model 
              position={[0, 0, 0]} 
              rotation={[-Math.PI/4, 0, 0]} 
              scale={[1, 1, 1]}
            />*/}
            <OverlayNoiseFilter />
          </Suspense>
        </Canvas>
      </div>
      
      {/* Page header */}
      <header className="portfolio-header">
        <h4>PORTFOLIO OF<br/>BLCKS</h4>
      </header>
      
      {/* Copyright text on bottom left */}
      <div className="copyright-text">
        All rights reserved. © 2025 BLCKs
      </div>
      
      {/* Bottom Right Navigation - Removed HOME and PHILOSOPHY */}
      <nav className="sidebar-nav">
        <ul>
          <li className={activeSection === "about" ? "active" : ""}>
            <button onClick={() => handleNavClick("about")}>ABOUT</button>
          </li>
          <li className={activeSection === "services" ? "active" : ""}>
            <button onClick={() => handleNavClick("services")}>SERVICES</button>
          </li>
          <li className={activeSection === "work" ? "active" : ""}>
            <button onClick={() => handleNavClick("work")}>WORK</button>
          </li>
          <li className={activeSection === "contact" ? "active" : ""}>
            <button onClick={() => handleNavClick("contact")}>CONTACT</button>
          </li>
        </ul>
        <div className="built-by">
          built by BLCKS with ❤
        </div>
      </nav>
      
      {/* Scrollable Content with Lenis */}
      <div className="scroll-container" ref={scrollRef}>
        {/* Hero Section */}
        <section className="section" id="hero">
          <div className="section-content">
            <div className="hero-content">
              <h1 className="hero-title">CREATING <br/>DIGITAL <br/>EXPERIENCES</h1>
              <p className="hero-subtitle">Creative Developer & Digital Designer</p>
            </div>
          </div>
        </section>
        
        {/* Combined About Section - Centered layout, 150vh height */}
        <section className="section combined-about-section" id="about">
          <div className="section-content">
            <div className="section-heading centered">
              <h3>ABOUT US</h3>
            </div>
            
            <div className="section-text centered">
              <p className="large-text about-description-size">
                BLOCKS ist ein Creative AI & Design Studio, das Ideen in intelligente,
                interaktive und visuell fesselnde Erlebnisse verwandelt – mit Technologie 
                am Puls der Zeit und einem klaren Gespür für Ästhetik und Wirkung,
                gestalten wir digitale Webauftritte, die nicht nur gut aussehen, sondern Menschen wirklich erreichen.
              </p>
            </div>
            
            <div className="profile-image-container">
              <div className="profile-image-square">
                <InversionLens src="/me.jpg" alt="BLCKS profile" />
              </div>
            </div>
            
            {/* Philosophy content moved into the about section */}
            <div className="philosophy-content">
              <div className="second-paragraph-wrapper">
                <p className="large-text second-paragraph philosophy-text philosophy-text-size">
                  Bei BLOCKS folgen wir nicht einfach Trends - wir setzen auf eine neue Art der digitalen Transformation.
                  Eine, die sich an dir, deiner Marke und deiner Zielgruppe orientiert, um nachhaltige, personalisierte Erlebnisse zu schaffen.
                </p>
              </div>
            </div>
          </div>
        </section>
        
        {/* Services Section - Centered layout */}
        <section className="section" id="services">
          <div className="section-content">
            <div className="section-heading centered">
              <h3>SERVICES</h3>
            </div>
            
            <div className="services-heading-container centered">
              <h2 className="services-big-heading">WEB DEVELOPMENT · 3D EXPERIENCES · UI/UX DESIGN · BRANDING · LOGO DESIGN · MOTION · CONCEPTS </h2>
            </div>
          </div>
        </section>
        
        {/* Work Section - With project headings */}
        <section className="section" id="work">
          <div className="project-gallery">
            {/* Project 1 - Right aligned */}
            <div className="gallery-item right-aligned">
              <div className="project-heading" style={{left: "15%", top: "50%", transform: "translateY(-50%)"}}>
                <h2>DESO</h2>
              </div>
              <div className="project-image">
                <InversionLens src="/poppr_bw.png" />
              </div>
            </div>
            
            {/* Project 2 - Center-Left aligned */}
            <div className="gallery-item center-left">
              <div className="project-heading" style={{left: "30%", top: "30%", transform: "translateY(-30%)"}}>
                <h2>FILM SECESSION</h2>
              </div>
              <div className="project-image">
                <InversionLens src="/glenn-catteeuw.webp" alt="Project 2" />
              </div>
            </div>
            
            {/* Project 3 - Right aligned */}
            <div className="gallery-item right-aligned">
              <div className="project-heading" style={{left: "15%", top: "50%", transform: "translateY(-50%)"}}>
                <h2>COCA COLA</h2>
              </div>
              <div className="project-image">
                <InversionLens src="/poppr_bw.png" alt="Project 3" />
              </div>
            </div>
            
            {/* Project 4 - Center-Left aligned */}
            <div className="gallery-item center-left">
              <div className="project-heading" style={{left: "30%", top: "30%", transform: "translateY(-30%)"}}>
                <h2>LUNCHBOX</h2>
              </div>
              <div className="project-image">
                <InversionLens src="/glenn-catteeuw.webp" alt="Project 4" />
              </div>
            </div>
            
            {/* Project 5 - Right aligned */}
            <div className="gallery-item right-aligned">
              <div className="project-heading" style={{left: "15%", top: "50%", transform: "translateY(-50%)"}}>
                <h2>VICTORINOX</h2>
              </div>
              <div className="project-image">
                <InversionLens src="/poppr_bw.png" alt="Project 5" />
              </div>
            </div>
          </div>
        </section>
        
        {/* Contact Section */}
        <section className="section" id="contact">
          <div className="section-content layout-grid">
            <div className="section-heading">
              <h3>CONTACT</h3>
            </div>
            
            <div className="contact-details">
              <p className="large-text">Let's create something amazing together</p>
              <a href="mailto:<EMAIL>" className="contact-link"><EMAIL></a>
              
              <div className="social-links">
                <a href="#" className="social-link">Twitter</a>
                <a href="#" className="social-link">GitHub</a>
                <a href="#" className="social-link">LinkedIn</a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}

export default App