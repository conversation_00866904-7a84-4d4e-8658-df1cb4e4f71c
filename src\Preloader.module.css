/* Font import */
@font-face {
  font-family: 'Archivo';
  src: url('/fonts/archivo-black-regular.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Scoped styles for the preloader wrapper and its children */
.preloaderWrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
}

.preloaderWrapper * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.preloaderWrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preloaderWrapper h1 {
  text-transform: uppercase;
  font-family: "Archivo", sans-serif;
  font-size: 7rem;
  font-weight: 600;
  line-height: 1;
}

.preloaderWrapper p {
  text-transform: uppercase;
  font-family: "Archivo", sans-serif;
  font-size: 13px;
  font-weight: 500;
}

.preloader, 
.splitOverlay, 
.tagsOverlay {
  position: fixed;
  width: 100vw;
  height: 100vh;
}

.preloader,
.splitOverlay {
  background-color: #000000;
  color: #e8e8e8;
}

.preloader,
.tagsOverlay {
  z-index: 2;
}

.splitOverlay {
  z-index: 1;
}

/* Consider adding specific positioning for the split versions */
.splitOverlay .introTitle {
  /* Adjust position to ensure it's visible in the bottom half */
  top: 75%; /* Move it down so it's fully in the bottom half */
}

.introTitle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  text-align: center;
}

.outroTitle {
  position: absolute;
  top: 50%;
  left: calc(40%);
  transform: translate(-50%, -50%);
}

.tag {
  position: absolute;
  width: max-content;
  color: #272727;
  overflow: hidden;
}

.tag1 {
  top: 15%;
  left: 15%;
}

.tag2 {
  bottom: 15%;
  left: 25%;
}

.tag3 {
  bottom: 30%;
  right: 15%;
}

.container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  clip-path: polygon(0 48%, 0 48%, 0 52%, 0 52%);
  z-index: 2;
  pointer-events: none;
}

.heroImg {
  position: absolute;
  width: 100%;
  height: 100%;
}

/* These styles are now global in your component but they'll 
   only apply to elements inside .preloaderWrapper */
.introTitle :global(.char),
.outroTitle :global(.char) {
  position: relative;
  display: inline-block;
  overflow: hidden;
  margin-top: 0.75rem;
}

.introTitle :global(.char span),
.outroTitle :global(.char span),
.tag :global(.word) {
  position: relative;
  display: inline-block;
  transform: translateY(-100%);
  will-change: transform;
}

.introTitle :global(.first-char),
.introTitle :global(.special-char) {
  transform-origin: top left;
}

.preloaderTag {
  position: absolute;
  width: max-content;
  color: #5a5a5a;
  overflow: hidden;
}



/* Mobile optimizations */
@media screen and (max-width: 767px) {
  /* Adjust font sizes */
  .preloaderWrapper h1 {
    font-size: 3.5rem; /* Reduced font size for mobile */
  }

  .preloaderWrapper p {
    font-size: 11px; /* Slightly smaller text */
  }

  /* Adjust positioning for main titles */
  .introTitle {
    width: 90%; /* Allow some margin on the sides */
  }

  .outroTitle {
    left: 50%; /* Center on mobile */
  }

  /* Adjust tag positions for smaller screens */
  .tag1 {
    top: 10%;
    left: 10%;
  }

  .tag2 {
    bottom: 10%;
    left: 10%;
  }

  .tag3 {
    bottom: 20%;
    right: 10%;
  }

  /* Reduce margins for characters */
  .introTitle :global(.char),
  .outroTitle :global(.char) {
    margin-top: 0.5rem;
  }

  /* Make split overlay positioning more mobile-friendly */
  .splitOverlay .introTitle {
    top: 65%; /* Adjusted for better visibility on mobile */
  }
}

/* Small phones */
@media screen and (max-width: 480px) {
  .preloaderWrapper h1 {
    font-size: 2.8rem; /* Even smaller font size for very small screens */
  }

  /* Make tags more visible by spreading them out */
  .tag1 {
    top: 8%;
    left: 8%;
  }

  .tag2 {
    bottom: 8%;
    left: 8%;
  }

  .tag3 {
    bottom: 16%;
    right: 8%;
  }
}