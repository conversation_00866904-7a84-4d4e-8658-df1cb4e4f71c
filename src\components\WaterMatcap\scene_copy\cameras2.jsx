import { OrbitControls, use<PERSON><PERSON>per } from "@react-three/drei";
import { useThree } from "@react-three/fiber";
import { useControls } from "leva";
import { memo, useEffect, useRef } from "react";
import { CameraHelper, PerspectiveCamera } from "three";
import { create } from "zustand";

// Camera instances
export const mainCamera = new PerspectiveCamera(75, 1, 1, 100);
export const orbitCamera = new PerspectiveCamera(75, 1, 1, 100);

export const Cameras = memo(CamerasInner);

const cameraMap = {
  orbit: orbitCamera,
  main: mainCamera,
};

// Zustand store for camera management and scroll-based camera movement
export const useCameraStore = create((set) => ({
  camera: mainCamera,
  set: (camera) => set({ camera }),

  // Original and final orbit camera positions
  originalOrbitPosition: { x: 0, y: 0.35, z: 2 },
  originalOrbitRotation: { x: -0.12, y: 0, z: 0 },
  finalOrbitPosition: { x: 2, y: 0.0, z: 4 }, // New final position - you can adjust these
  finalOrbitRotation: { x: 0.21, y: 1.97, z: 0 }, // New final rotation - you can adjust these

  setOriginalOrbitPosition: (pos) => set({ originalOrbitPosition: pos }),
  setOriginalOrbitRotation: (rot) => set({ originalOrbitRotation: rot }),
  setFinalOrbitPosition: (pos) => set({ finalOrbitPosition: pos }),
  setFinalOrbitRotation: (rot) => set({ finalOrbitRotation: rot }),
}));

function CamerasInner() {
  const set = useCameraStore((state) => state.set);
  const threeSet = useThree((state) => state.set);
  const originalOrbitPosition = useCameraStore((state) => state.originalOrbitPosition);
  const originalOrbitRotation = useCameraStore((state) => state.originalOrbitRotation);
  const finalOrbitPosition = useCameraStore((state) => state.finalOrbitPosition);
  const finalOrbitRotation = useCameraStore((state) => state.finalOrbitRotation);
  const setFinalOrbitPosition = useCameraStore((state) => state.setFinalOrbitPosition);
  const setFinalOrbitRotation = useCameraStore((state) => state.setFinalOrbitRotation);

  const [{ camera, orbitFinalX, orbitFinalY, orbitFinalZ, orbitFinalRotX, orbitFinalRotY, orbitFinalRotZ }] = useControls(() => ({
    camera: {
      value: "main",
      options: ["main", "orbit"],
    },
    // Final orbit camera position controls only
    orbitFinalX: {
      value: finalOrbitPosition.x,
      label: 'Camera Final X',
      step: 0.01,
      min: -5,
      max: 5,
    },
    orbitFinalY: {
      value: finalOrbitPosition.y,
      label: 'Camera Final Y',
      step: 0.01,
      min: -5,
      max: 5,
    },
    orbitFinalZ: {
      value: finalOrbitPosition.z,
      label: 'Camera Final Z',
      step: 0.01,
      min: -5,
      max: 5,
    },
    orbitFinalRotX: {
      value: finalOrbitRotation.x,
      label: 'Camera Final Rot X',
      step: 0.01,
      min: -Math.PI,
      max: Math.PI,
    },
    orbitFinalRotY: {
      value: finalOrbitRotation.y,
      label: 'Camera Final Rot Y',
      step: 0.01,
      min: -Math.PI,
      max: Math.PI,
    },
    orbitFinalRotZ: {
      value: finalOrbitRotation.z,
      label: 'Camera Final Rot Z',
      step: 0.01,
      min: -Math.PI,
      max: Math.PI,
    },
  }));

  // Update store when Leva controls change (only final positions)
  useEffect(() => {
    setFinalOrbitPosition({ x: orbitFinalX, y: orbitFinalY, z: orbitFinalZ });
    setFinalOrbitRotation({ x: orbitFinalRotX, y: orbitFinalRotY, z: orbitFinalRotZ });
  }, [orbitFinalX, orbitFinalY, orbitFinalZ, orbitFinalRotX, orbitFinalRotY, orbitFinalRotZ, setFinalOrbitPosition, setFinalOrbitRotation]);

  useEffect(() => {
    mainCamera.lookAt(0, 0, 0);

    // Set orbit camera to original position initially
    orbitCamera.position.set(originalOrbitPosition.x, originalOrbitPosition.y, originalOrbitPosition.z);
    orbitCamera.rotation.set(originalOrbitRotation.x, originalOrbitRotation.y, originalOrbitRotation.z);

    const selectedCamera = cameraMap[camera];
    set(selectedCamera);
    selectedCamera.updateProjectionMatrix();
    threeSet({ camera: selectedCamera });
  }, [camera, set, threeSet, originalOrbitPosition, originalOrbitRotation]);

  const width = useThree((state) => state.size.width);
  const height = useThree((state) => state.size.height);

  useEffect(() => {
    mainCamera.aspect = width / height;
    mainCamera.updateProjectionMatrix();
    orbitCamera.aspect = width / height;
    orbitCamera.updateProjectionMatrix();
  }, [width, height]);

  return (
    <>
      <primitive
        object={mainCamera}
        position={[0, 3, 0]}
        fov={40}
        near={0.1}
        far={10}
        aspect={width / height}
      />
      <primitive
        object={orbitCamera}
        near={0.1}
        far={7}
        aspect={width / height}
      />
    </>
  );
}

export function CameraDebugHelper() {
  const mainCameraRef = useRef(mainCamera);
  const activeCamera = useCameraStore((state) => state.camera);

  useHelper(activeCamera === orbitCamera && mainCameraRef, CameraHelper);

  return null;
}

// Simple camera controller for hybrid scroll navigation
export function ScrollCameraController({ cameraProgress }) {
  const originalOrbitPosition = useCameraStore((state) => state.originalOrbitPosition);
  const originalOrbitRotation = useCameraStore((state) => state.originalOrbitRotation);
  const finalOrbitPosition = useCameraStore((state) => state.finalOrbitPosition);
  const finalOrbitRotation = useCameraStore((state) => state.finalOrbitRotation);

  useEffect(() => {
    // Simple interpolation between original and final positions
    const progress = Math.max(0, Math.min(1, cameraProgress));

    // Interpolate position
    const currentX = originalOrbitPosition.x + (finalOrbitPosition.x - originalOrbitPosition.x) * progress;
    const currentY = originalOrbitPosition.y + (finalOrbitPosition.y - originalOrbitPosition.y) * progress;
    const currentZ = originalOrbitPosition.z + (finalOrbitPosition.z - originalOrbitPosition.z) * progress;

    // Interpolate rotation
    const currentRotX = originalOrbitRotation.x + (finalOrbitRotation.x - originalOrbitRotation.x) * progress;
    const currentRotY = originalOrbitRotation.y + (finalOrbitRotation.y - originalOrbitRotation.y) * progress;
    const currentRotZ = originalOrbitRotation.z + (finalOrbitRotation.z - originalOrbitRotation.z) * progress;

    // Apply to orbit camera
    orbitCamera.position.set(currentX, currentY, currentZ);
    orbitCamera.rotation.set(currentRotX, currentRotY, currentRotZ);
    orbitCamera.updateProjectionMatrix();

  }, [cameraProgress, originalOrbitPosition, originalOrbitRotation, finalOrbitPosition, finalOrbitRotation]);

  return null;
}