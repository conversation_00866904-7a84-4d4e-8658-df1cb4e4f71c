/* Import Satoshi font */
@import url('https://api.fontshare.com/v2/css?f[]=satoshi@900,700,500,301,300,400&display=swap');

/* Import local Telegraf fonts */
@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Archivo';
  src: url('/fonts/archivo-black-regular.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  font-family: 'Telegraf', sans-serif;
  color: black;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: transparent;
}

/* App structure */
.appContainer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: lightgrey;
}

.canvasContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
  pointer-events: none;
}

.canvasContainer canvas {
  pointer-events: auto;
}

.backgroundImage {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/blcks_simple_bg_light.jpg');
  background-size: cover;
  background-position: center;
  z-index: -1;
  opacity: 1;
}

/* Smooth transitions between views */
.mainView,
.projectView {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.5s ease;
}

.fadein {
  opacity: 1;
  pointer-events: auto;
}

.fadeout {
  opacity: 0;
  pointer-events: none;
}

.contentContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 2;
  pointer-events: auto;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.contentContainer::-webkit-scrollbar {
  display: none;
}

/* Global section properties */
.section {
  height: 100vh;
  width: 100%;
  position: relative;
  background-color: rgba(255, 255, 255, 0.0);
}

/* Override for slogan section specifically */
.section.slogan {
  height: 150vh;
  position: relative;
}

/* Override for slogan section specifically */
.section.industries {
  height: 125vh;
  position: relative;
}

/* Override for slogan section specifically */
.section.services {
  height: auto;
  position: relative;
}

/* Override for slogan section specifically */
.section.product {
  height: 150vh;
  position: relative;
}

/* Override for slogan section specifically */
.section.team {
  height: 125vh;
  position: relative;
}

/* Override for slogan section specifically */
.section.blog {
  height: 250vh;
  position: relative;
}






/* Updated Home section */
.home {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  position: relative;
}

.homeContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 50px;
}

.homeContent {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: 100%;
}

.homeTitle {
  font-size: 3.5rem;
  font-weight: 600;
  line-height: 1.3;
  color: white;
  margin-bottom: 50px;
  max-width: 70%;
}

.homeEventDetails {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  color: white;
  font-size: 0.9rem;
  letter-spacing: 1px;
  border-top: 0px solid rgba(255, 255, 255, 0.3);
  padding-top: 20px;
}

.homeEventLocation, 
.homeEventDate,
.homeEventPricing,
.homeTicketButton {
  flex: 1;
}

.homeEventLocation div, 
.homeEventDate div, 
.pricingOption div {
  margin-bottom: 5px;
  font-family: 'Telegraf', sans-serif;
}

.homeEventPricing {
  display: flex;
  gap: 30px;
  flex: 2;
}

.pricingOption {
  display: flex;
  flex-direction: column;
}

.pricingOption div:last-child {
  font-weight: 600;
}

.homeTicketButton {
  text-align: left;
  order: 0;
}

.ticketLink {
  display: inline-block;
  background-color: white;
  color: black;
  padding: 15px 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.ticketLink:hover {
  background-color: rgba(255, 255, 255, 0.8);
}

/* Scroll indicator at bottom */
.scrollIndicator {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 0px;
  order: 1;
}

.scrollText {
  font-family: 'Telegraf', sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 1px;
  color: #e8e8e8;
  white-space: nowrap;
  margin-right: 15px;
}

.scrollArrow {
  width: 24px;
  height: auto;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .homeContainer {
    padding: 40px;
  }
  
  .homeTitle {
    font-size: 3.5rem;
    max-width: 90%;
  }
  
  .homeEventPricing {
    gap: 20px;
  }
}

@media (max-width: 900px) {
  .homeTitle {
    font-size: 3rem;
    margin-bottom: 50px;
  }
  
  .homeEventDetails {
    flex-wrap: wrap;
    row-gap: 30px;
  }
  
  .homeEventLocation, 
  .homeEventDate {
    flex: 0 0 50%;
  }
  
  .homeEventPricing {
    flex: 0 0 60%;
  }
  
  .homeTicketButton {
    flex: 0 0 40%;
    text-align: right;
  }
}

@media (max-width: 768px) {
  .homeContainer {
    padding: 30px 20px;
  }
  
  .homeTitle {
    font-size: 2.5rem;
    max-width: 100%;
    margin-bottom: 40px;
  }
  
  .homeEventDetails {
    flex-direction: column;
    row-gap: 20px;
  }
  
  .homeEventLocation, 
  .homeEventDate,
  .homeEventPricing,
  .homeTicketButton {
    flex: 0 0 auto;
    width: 100%;
  }
  
  .homeTicketButton {
    text-align: left;
    margin-top: 20px;
    order: 0;
  }
  
  .scrollIndicator {
    justify-content: flex-start;
    order: 1;
  }
}

.homeCopyright {
  display: flex;
  align-items: center;
  color: #e8e8e8;
  font-family: 'Telegraf', sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 1px;
  white-space: nowrap;
  margin-right: 15px;
}

@media (max-width: 768px) {
  .homeCopyright {
    margin-right: 0;
    margin-bottom: 10px;
    font-size: 0.85rem;
  }
}











/* Slogan section styling to match the image */
.sloganContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  padding: 50px;
  color: white;
  text-align: left;
  position: absolute;
  bottom: 0;
  left: 0;
}

.aboutUsText {
  font-size: 1.2rem;
  color: #cccccc;
  margin-bottom: 30px;
  text-align: left;
  font-weight: 300;
}

.sloganMainText {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  margin-bottom: 30px;
}

.sloganHeading {
  font-size: 5rem;
  font-weight: 600;
  line-height: 1.1;
  letter-spacing: -0.5px;
  text-align: left;
  max-width: 70%;
  margin-bottom: 15px;
  text-transform: uppercase;
}

.sloganDivider {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.3);
  margin: 30px 0;
  position: relative;
  overflow: hidden;
}

.sloganDivider::after {
  content: "............................................................................";
  position: absolute;
  top: -10px;
  left: 0;
  color: rgba(255, 255, 255, 0.5);
  letter-spacing: 10px;
}

.sloganDescriptionContainer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.sloganDescription {
  text-align: right;
  max-width: 50%;
}

.sloganParagraph {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #e0e0e0;
  margin-bottom: 20px;
  font-weight: 300;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .sloganHeading {
    font-size: 4rem;
    max-width: 90%;
  }
  
  .sloganDescription {
    max-width: 70%;
  }
}

@media (max-width: 768px) {
  .sloganContainer {
    padding: 30px;
  }
  
  .sloganHeading {
    font-size: 2.5rem;
    max-width: 100%;
  }
  
  .sloganDescription {
    max-width: 100%;
  }
  
  .sloganDescriptionContainer {
    justify-content: flex-start;
  }
  
  .sloganDescription {
    text-align: left;
  }
}





/* --- UPDATED SERVICES GRID SECTION --- */
.servicesGridContainer {
  width: 100%;
  max-width: 100vw;
  padding: 60px 40px 40px 40px;
  color: #111;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  margin-top: 200px;
}

.servicesHeadingNew {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 40px;
  letter-spacing: 1px;
  color: #ffffff;
  text-align: left;
  width: 100%;
  max-width: 1400px;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(6, 1fr);
  gap: 40px;
  width: 100%;
  max-width: 1300px;
  margin: 0 auto;
}

.card {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  min-height: 210px;
  transition: box-shadow 0.2s, transform 0.2s;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.card:hover {
  /* Remove shadow and transform */
  box-shadow: none;
  transform: none;
}

.cardTop {
  width: 100%;
  height: 38px;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-end;
  padding: 0;
}

.cardTopLeft {
  flex: 1;
  min-width: 0;
  height: 100%;
  background: #fff;
}

.cardTopRight {
  width: auto;
  height: 100%;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 18px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: #ffffff;
  letter-spacing: 1.5px;
  background-image: linear-gradient(to right, #fff 100%, transparent 0%);
  background-size: 0% 100%;
  background-repeat: no-repeat;
  background-position: left;
  transition: background-size 0.4s cubic-bezier(0.4,0,0.2,1), color 0.2s;
}

.card:hover .cardTopRight {
  background-size: 100% 100%;
  color: #ffffff;
}

.cardBottom {
  background: #fff;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 18px 18px 18px;
}

.servicesCardLogo {
  width: 48px;
  height: 48px;
  background: #eee;
  border-radius: 0;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.servicesCardTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #111;
  text-align: center;
}

/* Responsive layouts */
@media (max-width: 1200px) {
  .servicesGrid {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(8, 1fr);
    gap: 24px;
  }
}

@media (max-width: 900px) {
  .servicesGrid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(12, 1fr);
    gap: 18px;
  }
}

@media (max-width: 600px) {
  .servicesGridContainer {
    padding: 30px 6px 20px 6px;
  }
  
  .servicesHeadingNew {
    font-size: 1.3rem;
    margin-bottom: 18px;
  }
  
  .servicesGrid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(24, 1fr);
    gap: 12px;
  }
  
  .card {
    min-height: 100px;
    padding: 18px 8px;
  }
  
  .servicesCardLogo {
    width: 32px;
    height: 32px;
    margin-bottom: 10px;
  }
  
  .servicesCardTitle {
    font-size: 0.95rem;
  }
}





/* Product section styles */
.product {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 125vh;
  overflow: hidden; /* For the grid overlay */
  position: relative;
}

/* Grid background pattern */
.product::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 90%;
  background-image: 
    linear-gradient(to right, rgba(50, 50, 50, 0.3) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(50, 50, 50, 0.3) 1px, transparent 1px);
  background-size: 100px 100px;
  z-index: 1;
}

.productContainer {
  width: 100%;
  max-width: 100%;
  padding: 50px;
  color: white;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  z-index: 2; /* Above the grid pattern */
  display: flex;
  flex-direction: column;
}

.productContentWrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
}

.productTextColumn {
  position: absolute;
  top: 20%;
  left: 0;
  width: 60%;
  z-index: 3;
}

.productHeading {
  font-size: 4rem;
  font-weight: 600;
  line-height: 1.1;
  text-transform: uppercase;
  max-width: 90%;
}

.productVisualColumn {
  position: absolute;
  bottom: 0%;
  right: 20%;
  width: 50%;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.productVisual {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.productImage {
  width: 350px; /* Increased size */
  height: auto;
  transform: rotate(0deg);
}

/* Responsive styles */
@media (max-width: 1400px) {
  .productHeading {
    font-size: 3.5rem;
  }
  
  .productImage {
    width: 350px;
  }
  
  .productTextColumn {
    width: 70%;
  }
}

@media (max-width: 1200px) {
  .productContainer {
    padding: 40px;
  }
  
  .productHeading {
    font-size: 3rem;
    max-width: 100%;
  }
  
  .productImage {
    width: 300px;
  }
}

@media (max-width: 900px) {
  .productTextColumn {
    width: 80%;
  }
  
  .productVisualColumn {
    width: 60%;
  }
  
  .productImage {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .productContainer {
    padding: 30px 20px;
  }
  
  .productTextColumn {
    position: relative;
    top: 5%;
    width: 100%;
    margin-bottom: 40px;
  }
  
  .productHeading {
    font-size: 2.2rem;
  }
  
  .productVisualColumn {
    position: relative;
    width: 100%;
    justify-content: center;
  }
  
  .productVisual {
    justify-content: center;
  }
  
  .productImage {
    width: 200px;
  }
}



/* Team section styles */
.team {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.teamContainer {
  width: 100%;
  max-width: 100%;
  padding: 50px;
  color: white;
  position: absolute;
  bottom: 40%;
  left: 0;
  z-index: 2;
}

/* Header row with heading, dotted line and description */
.teamHeaderRow {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 80px;
  position: relative;
}

.teamHeading {
  font-size: 5rem;
  font-weight: 600;
  line-height: 1.1;
  margin-right: auto;
}

.teamDottedLine {
  flex-grow: 1;
  height: 1px;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.5) 50%, transparent 50%);
  background-size: 10px 1px;
  background-repeat: repeat-x;
  margin: 0 40px;
  margin-top: 30px;
}

.teamDescription {
  max-width: 300px;
  text-align: right;
  font-size: 1rem;
  line-height: 1.6;
  color: #e0e0e0;
}

/* Decorative circle and line graphic */
.teamCircleGraphic {
  position: absolute;
  top: 50%;
  right: 50%;
  width: 400px;
  height: 400px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  z-index: 1;
  pointer-events: none;
}

.teamCircleGraphic::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 500px;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.2);
  transform: rotate(-45deg);
}

.teamCircleGraphic::after {
  content: '';
  position: absolute;
  top: 40%;
  left: 80%;
  width: 100px;
  height: 100px;
  background-color: rgba(80, 80, 80, 0.3);
  border-radius: 50%;
}

/* Team members grid */
.teamMembers {
  display: flex;
  justify-content: space-between;
  gap: 30px;
  margin-top: 40px;
  position: relative;
  z-index: 3;
}

.teamMember {
  display: flex;
  flex-direction: row;
  width: 45%;
  height: 300px;
  overflow: hidden;
  border-radius: 10px;
}

.memberImageContainer {
  width: 50%;
  overflow: hidden;
}

.memberImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: grayscale(100%);
}

.memberInfo {
  width: 50%;
  padding: 30px;
  display: flex;
  flex-direction: column;
}

.memberLabel {
  font-size: 1rem;
  margin-bottom: 15px;
  color: #333;
}

.memberName {
  font-size: 2.5rem;
  font-weight: 600;
  line-height: 1;
  color: #000;
  margin-bottom: auto;
}

.memberBarcode {
  width: 60px;
  height: 40px;
  background-image: repeating-linear-gradient(
    to right,
    #000,
    #000 2px,
    transparent 2px,
    transparent 4px
  );
  align-self: flex-end;
}

/* White card specific styles */
.teamMember:nth-child(2) .memberInfo {
  color: #000;
}

.teamMember:nth-child(2) .memberLabel {
  color: #333;
}

.teamMember:nth-child(2) .memberName {
  color: #000;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .teamContainer {
    padding: 40px;
  }
  
  .teamHeading {
    font-size: 4rem;
  }
  
  .teamCircleGraphic {
    width: 300px;
    height: 300px;
  }
  
  .teamMember {
    height: 250px;
  }
  
  .memberName {
    font-size: 2rem;
  }
}

@media (max-width: 900px) {
  .teamMembers {
    flex-direction: column;
  }
  
  .teamMember {
    width: 100%;
    height: 200px;
  }
  
  .teamHeaderRow {
    flex-direction: column;
    margin-bottom: 40px;
  }
  
  .teamDottedLine {
    display: none;
  }
  
  .teamHeading {
    font-size: 3.5rem;
    margin-bottom: 20px;
  }
  
  .teamDescription {
    max-width: 100%;
    text-align: left;
  }
}

@media (max-width: 768px) {
  .teamContainer {
    padding: 30px 20px;
  }
  
  .teamHeading {
    font-size: 3rem;
  }
  
  .teamCircleGraphic {
    display: none;
  }
  
  .memberName {
    font-size: 1.8rem;
  }
}





/* Blog section styles */
.blog {
  min-height: 200vh; /* As requested, make it 200vh tall */
  display: flex;
  flex-direction: column;
  position: relative;
}

.blogContainer {
  width: 100%;
  max-width: 100%;
  padding: 50px;
  color: white;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Blog header with title and navigation */
.blogHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 50px;
  width: 100%;
}

.blogHeading {
  font-size: 3.5rem;
  font-weight: 600;
  line-height: 1.1;
}

.blogHeadingAccent {
  color: #00CC88; /* Green accent color */
}

.blogNavigation {
  display: flex;
  gap: 10px;
}

.blogNavButton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.blogNavArrow {
  font-size: 1.2rem;
  color: #000;
}

/* Featured blog post (top section) */
.blogFeatured {
  display: flex;
  height: calc(100vh - 200px); /* Nearly full height for featured post */
  margin-bottom: 50px;
}

.blogFeaturedLeft {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.blogDotPattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle, #00CC88 2px, transparent 2px);
  background-size: 20px 20px;
  opacity: 0.7;
}

.blogFeaturedRight {
  flex: 1;
  padding: 30px;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Blog post grid for bottom section */
.blogGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  height: calc(100vh - 150px); /* Remaining height for blog grid */
}

.blogPost {
  background-color: #141414;
  border-radius: 10px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Common blog elements */
.blogCategory {
  font-size: 0.9rem;
  color: #999;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 15px;
  border-radius: 30px;
  display: inline-block;
  margin-bottom: 15px;
  align-self: flex-start;
}

.blogTitle {
  font-size: 2rem;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 20px;
  max-width: 90%;
}

.blogImage {
  flex-grow: 1;
  overflow: hidden;
  margin-bottom: 20px;
  position: relative;
}

.blogImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.blogMeta {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  margin-top: auto;
}

.blogTime, .blogAuthor {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.blogTimeIcon, .authorIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  font-size: 0.9rem;
}

.authorName {
  color: #00CC88;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .blogContainer {
    padding: 40px;
  }
  
  .blogHeading {
    font-size: 3rem;
  }
  
  .blogTitle {
    font-size: 1.8rem;
  }
  
  .blogFeatured {
    height: 70vh;
  }
  
  .blogGrid {
    height: auto;
  }
}

@media (max-width: 900px) {
  .blogFeatured {
    flex-direction: column;
    height: auto;
  }
  
  .blogFeaturedLeft {
    height: 200px;
  }
}

@media (max-width: 768px) {
  .blogContainer {
    padding: 30px 20px;
  }
  
  .blogHeading {
    font-size: 2.5rem;
  }
  
  .blogGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .blogTitle {
    font-size: 1.5rem;
  }
}





/* WORK SECTION */
.work {
  min-height: 600vh;
  padding: 10rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.work .projectGallery {
  display: flex;
  flex-direction: column;
  gap: 40rem;
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 10rem;
}

.work .galleryItem {
  width: 100%;
  display: flex;
  position: relative;
}

.work .rightAligned {
  justify-content: flex-end;
  padding-left: 10%;
}

.work .centerLeft {
  justify-content: center;
  padding-right: 10%;
}

.work .projectImage {
  width: 85%;
  padding-bottom: 47.8125%; 
  position: relative;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.05);
  z-index: 5;
  pointer-events: auto;
  border-radius: 8px;
  cursor: pointer;
}

/* Container for project heading elements */
.work .projectHeading {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  pointer-events: none;
  opacity: 0; /* Hide by default */
  transition: opacity 0.3s ease;
  padding: 0 20px 20px 20px; /* Left/right/bottom padding of 20px */
  display: flex;
  justify-content: space-between; /* Space between project name and VIEW PROJECT */
  align-items: flex-end; /* Align items to bottom */
  width: 100%;
}

/* Left section container for project title and category */
.work .projectInfoLeft {
  display: flex;
  flex-direction: row; /* Horizontal layout */
  align-items: baseline; /* Align text baselines */
  gap: 30px; /* Space between title and category */
}

/* Project title - bottom left */
.work .projectHeading h2 {
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  letter-spacing: 2px;
  color: white;
  line-height: 1.2;
  margin: 0;
  transform: translateY(10px);
  transition: transform 0.3s ease, opacity 0.3s ease;
  opacity: 0;
}

/* Category field - right of project title */
.work .projectHeading .projectCategory {
  font-family: 'Telegraf', sans-serif;
  font-weight: 400;
  font-size: 0.7rem;
  letter-spacing: 2px;
  color: rgb(170, 170, 170);
  opacity: 0;
  transform: translateY(10px);
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* VIEW PROJECT text - bottom right */
.projectHeading .viewProject {
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  font-size: 0.75rem;
  color: white;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
  letter-spacing: 2px;
  margin-left: auto; /* Push to right */
  /* No additional padding needed - container handles positioning */
}

.work .projectImage:hover .projectHeading {
  opacity: 1;
}

.work .projectImage:hover .projectHeading h2,
.work .projectImage:hover .projectHeading .projectCategory,
.work .projectImage:hover .projectHeading .viewProject {
  opacity: 1;
  transform: translateY(0);
}

/* Add a subtle gradient overlay to ensure text is readable on any image */
.work .projectImage::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50%; /* Gradient height - covers bottom half */
  background: linear-gradient(to top, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.work .projectImage:hover::after {
  opacity: 1;
}

.work .projectImage img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  pointer-events: auto;
  cursor: pointer;
  transition: transform 0.3s ease;
}

/* Transition Overlay */
.transitionOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  /* background-color is set by inline style */
  /* opacity is set by inline style */
  /* pointer-events is set by inline style */
  /* z-index is set by inline style */
  transition: opacity 0.5s ease-in-out; /* FADE_DURATION should match this in JS */
}

/* Adjust fadein/fadeout for main/project views to depend on overlay state */
.mainContentWrapper,
.projectViewWrapper {
  position: absolute; /* Ensure they can stack if needed, though one is usually hidden */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.5s ease-in-out; /* Standard fade for views */
}

.mainContentWrapper.fadein, 
.projectViewWrapper.fadein {
  opacity: 1;
  pointer-events: auto;
}

.mainContentWrapper.fadeout, 
.projectViewWrapper.fadeout {
  opacity: 0;
  pointer-events: none;
}

/* Grid effect container */
.gridEffectContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.gridEffectContainer canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.itemsWrapper {
  position: relative;
  z-index: 5;
}

.canvasPreloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #e8e8e8;
  z-index: 0;
}

/* General back button style (shared between both pages) */
.backButton {
  background: transparent;
  border: 0px solid #e8e8e8;
  color: black;
  font-family: 'Telegraf', sans-serif;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.75rem 1.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
}

.backButton:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Contact section background transition styles */
.contactSection {
  position: relative;
  min-height: 100vh;
  width: 100%;
  z-index: 5;
  background-color: transparent;
  transition: background-color 0.8s ease-in-out;
}

.contactSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: black;
  opacity: 0;
  transition: opacity 0.8s ease-in-out;
  z-index: 0;
}

.contactSection.fadeToBlack::before {
  opacity: 1;
}

.contact {
  min-height: 100vh;
  width: 100%;
  position: relative;
  z-index: 5;
  background-color: transparent; /* Light background instead of black */
}

.contactContent {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 2rem;
  position: relative;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.6s ease-in-out;
  transition-delay: 0.8s; /* Delay showing content until after background fade */
}

.contactSection.fadeToBlack .contactContent {
  opacity: 1;
}

/* Transition all text elements to white when on black background */
.contactSection.fadeToBlack .contactEmailHeading,
.contactSection.fadeToBlack .contactLinkButton,
.contactSection.fadeToBlack .contactCopyright,
.contactSection.fadeToBlack .contactBuiltBy {
  color: white;
  transition: color 0.8s ease-in-out;
}

.contactSection.fadeToBlack .contactEmailHeading::after,
.contactSection.fadeToBlack .contactLinkButton::after {
  background-color: white;
  transition: background-color 0.8s ease-in-out;
}

.contactEmailContainer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.contactEmailHeading {
  font-size: 4rem;
  font-weight: 500;
  color: #ffffff; /* Dark color for light background */
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.contactEmailHeading:hover {
  transform: scale(0);
  opacity: 0.7;
}

.contactEmailHeading::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #ffffff; /* Dark color for light background */
  transition: transform 0.4s ease;
  transform-origin: left;
  transform: scaleX(0); /* Start with no line */
  color: #ffffff;
}

.contactEmailHeading:hover::after {
  transform: scaleX(1);
  transform-origin: left;
  color: #ffffff;
}

.contactEmailLink {
  text-decoration: none;
  color: inherit; /* Preserves the original text color */
}

.contactEmailLink:hover {
  text-decoration: underline;
  cursor: pointer;
}


/* Contact Bottom */
.contactBottom {
  display: flex;
  justify-content: space-between;
  position: relative;
  align-items: flex-end;
  width: 100%;
  padding-bottom: 0rem;
}

.contactCopyright,
.contactBuiltBy {
  font-size: 0.7rem;
  color: black; /* Dark color for light background */
  opacity: 0.75;
  letter-spacing: 1px;
  font-family: 'Telegraf', sans-serif;
}


/* Footer section layout */
.footerSection {
  display: flex;
  justify-content: space-between;
  width: 40%;
  margin: 0 auto;
  position: absolute;
  bottom: 150px;
  left: 10%;
  right: 10%;
}

/* Footer links container styling */
.footerLinksContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}

/* Social icons styling - now positioned in the middle */
.socialIcons {
  display: flex;
  gap: 10px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 50%;
}

.socialIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: transparent;
  transition: opacity 0.3s ease;
}

.socialIcon:hover {
  opacity: 0.7;
}

.socialIcon img {
  width: 24px;
  height: 24px;
}

/* Footer link text styling */
.footerLinks {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 10px;
}

.footerLink {
  color: #ffffff;
  text-decoration: none;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 1px;
  transition: opacity 0.3s ease;
}

.footerLink:hover {
  opacity: 0.7;
}

/* QR Code container styling */
.qrCodeContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.qrCodeImage {
  width: 100px;
  height: 100px;
  margin-bottom: 0;
}

.qrCodeHeading {
  color: #ffffff;
  opacity: 0.7;
  font-size: 10px;
  font-weight: 400;
}

/* Navigation Button Styles */
.navButton {
  position: fixed;
  top: 20px;
  right: 10px;
  background: transparent;
  border: none;
  color: black;
  font-family: 'Telegraf', sans-serif;
  font-size: 1rem;
  font-weight: 500;
  padding: 10px 20px;
  cursor: pointer;
  z-index: 1500;
  transition: all 0.3s ease;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
}

.navButton .arrowIcon {
  margin-left: 6px;
  width: 24px;
  height: auto;
  opacity: 0;
  rotate: -90deg;
  transform: translateX(-5px);
  transition: all 0.3s ease;
}

.navButton:hover .arrowIcon {
  opacity: 1;
  transform: translateX(0);
}

.navButton:hover {
  opacity: 0.8;
}

/* Explicitly set the button color to white when contact section is active */
:global(body.contact-active) .navButton {
  color: white;
}

:global(body.contact-active) .navButton .arrowIcon {
  filter: brightness(0) invert(1); /* Make arrow white */
}


/* Styling for the slogan content */
.sloganContent {
  width: 100%;
  height: 100%;
}

/* Section content styling */
.sectionContent {
  width: 100%;
  height: 100%;
}

/* Content wrapper global styles */
.allSectionsContent {
  width: 100%;
}

/* MOBILE RESPONSIVENESS */

/* Mobile responsiveness for the home section */
@media screen and (max-width: 767px) {
  .home .homeRowContainer {
    flex-direction: column;
    align-items: flex-start;
  }

  .home .homeLogo {
    max-width: 175px; /* Make logo smaller on mobile */
    margin-right: 0;
    margin-left: -10px;
    margin-top: 30%;
    margin-bottom: 20px;
  }

  .home .homeHeading {
    font-size: 1rem; /* Make heading a bit bigger */
    margin-right: 0;
    margin-bottom: 30px;
  }
  
  .home .scrollContainer {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .home .scrollText {
    font-size: 0.65rem; /* Make scroll text smaller */
    margin-right: 10px;
    order: 1;
  }

  .home .scrollArrow {
    margin-right: 0;
    order: 2;
  }
  
  .home .sectionContent {
    padding: 0 1.5rem;
    margin-top: 15%;
  }
}


/* Mobile responsiveness for the slogan section */
@media screen and (max-width: 767px) {
  .slogan {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  /* Custom height for slogan section */
  .slogan {
    height: 60vh; /* Example: even shorter */
  }
  
  .slogan .sectionContent {
    padding: 0 1.5rem;
  }
  
  .slogan .sloganTextContainer {
    width: 100%;
  }
  
  .slogan h2 {
    font-size: 1.3rem; /* Smaller heading size for mobile */
    line-height: 1.3;
  }
}

/* Mobile responsiveness for the mission section */
@media screen and (max-width: 767px) {
  .mission {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Custom height for mission section */
  .mission {
    height: 70vh; /* Example: medium height */
  }
  
  .mission .sectionContent {
    padding: 0 1.5rem;
  }
  
  .mission .missionTextContainer {
    width: 100%;
  }
  
  .mission h2 {
    font-size: 0.75rem; /* Smaller h2 size for mobile */
    margin-bottom: 2rem;
  }
  
  .mission .missionDescription {
    font-size: 1.3rem; /* Smaller animated mission text for mobile */
    line-height: 1.4;
    margin-bottom: 0rem;
  }
}




/* Mobile responsiveness for the services section */
@media screen and (max-width: 767px) {
  .services {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Custom height for services section */
  .services {
    height: 75vh; /* Example: slightly taller */
  }
  
  .services .sectionContent {
    padding: 0 1.5rem;
  }
  
  .services .sectionTitle {
    font-size: 0.75rem; /* Smaller section title size for mobile */
    margin-bottom: 1.5rem;
  }
  
  .services .servicesBigHeading {
    font-size: 2rem; /* Smaller big heading size for mobile */
    line-height: 1.25;
    margin-bottom: 0rem;
    text-align: left;
  }
}


/* Mobile responsiveness for the work section */
@media screen and (max-width: 767px) {
  .work {
    min-height: 200vh; /* Adjust total height for mobile */
    padding: 5rem 0;
  }
  
  .work .projectGallery {
    gap: 10rem; /* Reduce gap between projects on mobile */
    padding: 0 1.5rem; /* Consistent padding with other sections */
  }
  
  .work .galleryItem {
    justify-content: center; /* Center all gallery items on mobile */
    padding-left: 0;
    padding-right: 0;
  }
  
  .work .rightAligned,
  .work .centerLeft {
    justify-content: center; /* Override desktop positioning */
    padding-left: 0;
    padding-right: 0;
  }
  
  .work .projectImage {
    width: 100%; /* Make images full width on mobile */
    padding-bottom: 56.25%; /* Maintain aspect ratio (16:9) */
  }
  
  /* Adjust the project heading text sizes */
  .work .projectHeading h2 {
    font-size: 0.9rem;
  }
  
  .work .projectHeading .projectCategory {
    font-size: 0.65rem;
  }
  
  .work .projectHeading .viewProject {
    font-size: 0.7rem;
  }
}


/* Mobile responsiveness for the footer/contact section */
@media screen and (max-width: 767px) {
  .contact {
    min-height: 100vh;
    position: relative;
  }
  
  .contactContent {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .contactEmailHeading {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 40px;
  }
  
  /* Additional container centering */
  .contactBottom {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    padding-bottom: 30px;
    padding-left: 0;
    padding-right: 0;
  }
  
  /* Specific centering fix */
  .footerSection {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin-bottom: 50px;
    order: 1;
    padding: 0;
  }
  
/* Social icons - direct flex approach with explicit spacing */
  .socialIcons {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin: 0 auto 80px auto;
    order: 1;
    padding: 0;
    gap: 30px; /* Equal spacing between icons */
  }
  
 .socialIcon {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0; /* Remove any margin */
  }

  /* First icon - align right */
  .socialIcon:nth-child(1) {
    justify-self: end;
    margin-right: 15px;
  }
  
  /* Middle icon - align center */
  .socialIcon:nth-child(2) {
    justify-self: center;
  }
  
  /* Last icon - align left */
  .socialIcon:nth-child(3) {
    justify-self: start;
    margin-left: 15px;
  }
  
  .socialIcon img {
    width: 20px;
    height: 20px;
  }
  

  
  /* Footer links - top */
  .footerLinksContainer {
    width: 100%;
    margin-bottom: 30px;
    order: 2;
  }
  
  .footerLinks {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  
  .footerLink {
    text-align: center;
    font-size: 0.8rem;
  }
  
  /* Built by text - above copyright */
  .contactBuiltBy {
    width: 100%;
    text-align: center;
    font-size: 0.6rem;
    margin-bottom: 15px;
    order: 2;
  }
  
  /* Copyright text - bottom */
  .contactCopyright {
    width: 100%;
    text-align: center;
    font-size: 0.6rem;
    order: 3;
  }
  
  /* Hide the QR code on mobile */
  .qrCodeContainer {
    display: none !important;
  }

  /* Force center with transform method as backup */
  .contact .footerSection .socialIcons {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    width: auto; /* Allow container to size to content */
  }



}

@media screen and (max-width: 767px) {
  /* Other mobile styles... */
  
  /* Adjust navigation "About" text size for mobile */
  .navButton {
    font-size: 0.85rem;  /* Reduced font size for mobile */
    padding: 4px 0px;   /* Optional: slightly reduce padding too */
  }
}

.example {
  height: 300vh;
  width: 100%;
  position: relative;
  background-color: rgba(255, 255, 255, 0.0);
}

.hideOnHover {
  transition: opacity 0.3s;
}

.cardHoverText {
  opacity: 0;
  pointer-events: none;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #222;
  font-size: 1rem;
  font-weight: 400;
  max-width: 90%;
  z-index: 2;
  transition: opacity 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card:hover .hideOnHover {
  opacity: 0;
  pointer-events: none;
}

.card:hover .cardHoverText {
  opacity: 1;
  pointer-events: auto;
}

.cardInitial {
  opacity: 0;
  transform: scale(0.8);
  will-change: opacity, transform;
  transition: opacity 0.6s cubic-bezier(0.4,0,0.2,1), transform 0.6s cubic-bezier(0.4,0,0.2,1);
}

.cardVisible {
  opacity: 1;
  transform: scale(1);
  will-change: opacity, transform;
  transition: opacity 0.6s cubic-bezier(0.4,0,0.2,1), transform 0.6s cubic-bezier(0.4,0,0.2,1);
}

.topLeftLogo {
  position: fixed;
  top: 24px;
  left: 24px;
  z-index: 100;
  width: 100px;
  height: auto;
  display: flex;
  align-items: center;
}

.topLeftLogo img {
  width: 100%;
  height: auto;
  display: block;
}

@media (max-width: 768px) {
  .topLeftLogo {
    top: 12px;
    left: 12px;
    width: 100px;
  }
}

.disabledTicketLink {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}

.hiddenTicketButton {
  display: none !important;
}



