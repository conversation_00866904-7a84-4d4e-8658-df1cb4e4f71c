import React, { Suspense, useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Canvas, useLoader, useThree, useFrame } from '@react-three/fiber';
import WaterMatcapBackground from '../components/WaterMatcap/WaterMatcapBackground';
import * as THREE from 'three';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader';
import { useControls } from 'leva';
import { MeshTransmissionMaterial, Text, useGLTF } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { TypeShuffle } from './TypeShuffle'; // Adjust path if needed
import Block1Overlay from '../components/Block1Overlay';
import Block2Overlay from '../components/Block2Overlay';
import Block3Overlay from '../components/Block3Overlay';
import Block4Overlay from '../components/Block4Overlay';
import styles from './whatSection.module.css';

// Performance optimizations: Memoize expensive material configurations
const TRANSMISSION_MATERIAL_CONFIG = {
  color: '#fff',
  backside: true,
  samples: 2, // Reduced from 4 for better performance
  thickness: 3,
  chromaticAberration: 0.01,
  anisotropy: 0.1,
  distortion: 0,
  distortionScale: 0,
  temporalDistortion: 0,
  iridescence: 1,
  iridescenceIOR: 1,
  iridescenceThicknessRange: [0, 1400],
  side: THREE.DoubleSide,
};

// Optimized shared Block component with memoization - exactly like HeroSection approach
const OptimizedBlock = React.memo(({
  modelPath,
  normalMapPath,
  roughnessMapPath,
  animationOffset = 0,
  animationSpeed = { y: 0.34, rotY: 0.21, rotZ: 0.17 },
  chromaticAberration = 0,
  ...props
}) => {
  const gl = useThree((state) => state.gl);
  const meshRef = useRef();

  // Memoize geometry loading
  const geometry = useLoader(
    DRACOLoader,
    modelPath,
    loader => {
      loader.setDecoderPath('https://www.gstatic.com/draco/v1/decoders/');
    }
  );

  // Memoize texture loading
  const normalMap = useLoader(KTX2Loader, normalMapPath, loader => {
    loader.setTranscoderPath('https://unpkg.com/three@0.152.2/examples/jsm/libs/basis/');
    loader.detectSupport(gl);
  });

  const roughnessMap = useLoader(KTX2Loader, roughnessMapPath, loader => {
    loader.setTranscoderPath('https://unpkg.com/three@0.152.2/examples/jsm/libs/basis/');
    loader.detectSupport(gl);
  });

  // Optimized animation with reduced calculations
  useFrame(({ clock }) => {
    if (!meshRef.current) return;

    const t = clock.getElapsedTime() + animationOffset;
    const baseRotY = props.rotation?.[1] || 0;
    const baseRotZ = props.rotation?.[2] || 0;

    // Use cached sin/cos values for better performance
    // Floating animation should be relative to group position (0), not absolute
    meshRef.current.position.y = Math.sin(t * animationSpeed.y) * 0.04;
    meshRef.current.rotation.y = baseRotY + Math.sin(t * animationSpeed.rotY) * 0.12;
    meshRef.current.rotation.z = baseRotZ + Math.cos(t * animationSpeed.rotZ) * 0.10;
  });

  return (
    <group {...props}>
      {/* Visual mesh with floating animation */}
      <mesh ref={meshRef} geometry={geometry}>
        <MeshTransmissionMaterial
          normalMap={normalMap}
          roughnessMap={roughnessMap}
          color={'#fff'}
          backside
          samples={2} // Reduced from 4 for better performance
          thickness={3}
          chromaticAberration={chromaticAberration}
          anisotropy={0.1}
          distortion={0}
          distortionScale={0}
          temporalDistortion={0}
          iridescence={1}
          iridescenceIOR={1}
          iridescenceThicknessRange={[0, 1400]}
          side={THREE.DoubleSide}
        />
      </mesh>
      {/* Static clickable area that doesn't move with animation */}
      <mesh visible={false}>
        <boxGeometry args={[1, 1, 1]} />
        <meshBasicMaterial transparent opacity={0} />
      </mesh>
    </group>
  );
});

// Individual block components using the optimized shared component
function Block1(props) {
  return (
    <OptimizedBlock
      modelPath="/models/block2.drc"
      normalMapPath="/textures/block2_normal.ktx2"
      roughnessMapPath="/textures/block2_roughness.ktx2"
      animationOffset={0}
      chromaticAberration={0.01}
      {...props}
    />
  );
}

function Block2(props) {
  return (
    <OptimizedBlock
      modelPath="/models/block3.drc"
      normalMapPath="/textures/block3_normal.ktx2"
      roughnessMapPath="/textures/block3_roughness.ktx2"
      animationOffset={0.7}
      animationSpeed={{ y: 0.30, rotY: 0.21, rotZ: 0.17 }}
      chromaticAberration={0}
      {...props}
    />
  );
}

function Block3(props) {
  return (
    <OptimizedBlock
      modelPath="/models/block1.drc"
      normalMapPath="/textures/block1_normal.ktx2"
      roughnessMapPath="/textures/block1_roughness.ktx2"
      animationOffset={1.4}
      animationSpeed={{ y: 0.26, rotY: 0.21, rotZ: 0.17 }}
      chromaticAberration={0}
      {...props}
    />
  );
}

function Block4(props) {
  return (
    <OptimizedBlock
      modelPath="/models/block2.drc"
      normalMapPath="/textures/block2_normal.ktx2"
      roughnessMapPath="/textures/block2_roughness.ktx2"
      animationOffset={2.1}
      animationSpeed={{ y: 0.22, rotY: 0.21, rotZ: 0.17 }}
      chromaticAberration={0}
      {...props}
    />
  );
}

export default function WhatSection() {
  // Consolidated Leva controls for better performance
  const controls = useControls('Block Rotations', {
    rotation1: {
      value: [-1.67, 0.05, -0.10],
      min: -Math.PI,
      max: Math.PI,
      step: 0.01,
      label: 'Block1 Rotation',
    },
    rotation2: {
      value: [-1.67, 0.17, -0.11],
      min: -Math.PI,
      max: Math.PI,
      step: 0.01,
      label: 'Block2 Rotation',
    },
    rotation3: {
      value: [-1.51, 0.20, -0.09],
      min: -Math.PI,
      max: Math.PI,
      step: 0.01,
      label: 'Block3 Rotation',
    },
    rotation4: {
      value: [-1.66, -0.01, -0.07],
      min: -Math.PI,
      max: Math.PI,
      step: 0.01,
      label: 'Block4 Rotation',
    },
    startRotation1: {
      value: [1.40, -2.90, -1.35],
      min: -Math.PI,
      max: Math.PI,
      step: 0.01,
      label: 'Block1 Start Rotation',
    },
    endRotation1: {
      value: [-3.50, 1.77, -1.78],
      min: -Math.PI,
      max: Math.PI,
      step: 0.01,
      label: 'Block1 End Rotation',
    },
    startRotation2: {
      value: [0.45, -0.50, -1.05],
      min: -Math.PI,
      max: Math.PI,
      step: 0.01,
      label: 'Block2 Start Rotation',
    },
    endRotation2: {
      value: [-3.14, -1.51, 0.66],
      min: -Math.PI,
      max: Math.PI,
      step: 0.01,
      label: 'Block2 End Rotation',
    },
    startRotation3: {
      value: [0.5, -0.5, 0.5],
      min: -Math.PI,
      max: Math.PI,
      step: 0.01,
      label: 'Block3 Start Rotation',
    },
    endRotation3: {
      value: [-3.00, 3.14, -0.50],
      min: -Math.PI,
      max: Math.PI,
      step: 0.01,
      label: 'Block3 End Rotation',
    },
    startRotation4: {
      value: [1.40, -2.90, -1.35],
      min: -Math.PI,
      max: Math.PI,
      step: 0.01,
      label: 'Block4 Start Rotation',
    },
    endRotation4: {
      value: [-3.50, 1.77, -1.78],
      min: -Math.PI,
      max: Math.PI,
      step: 0.01,
      label: 'Block4 End Rotation',
    },
  });

  // Destructure all controls at once
  const {
    rotation1, rotation2, rotation3, rotation4,
    startRotation1, endRotation1, startRotation2, endRotation2,
    startRotation3, endRotation3, startRotation4, endRotation4
  } = controls;

  // State declarations first to avoid initialization issues
  const [cubeZ, setCubeZ] = useState(2);

  // Memoize rotation arrays for better performance
  const startRotations = useMemo(() =>
    [startRotation1, startRotation2, startRotation3, startRotation4],
    [startRotation1, startRotation2, startRotation3, startRotation4]
  );

  const endRotations = useMemo(() =>
    [endRotation1, endRotation2, endRotation3, endRotation4],
    [endRotation1, endRotation2, endRotation3, endRotation4]
  );

  // Memoized helper functions for better performance
  const lerpArray = useCallback((a, b, t) => {
    return a.map((v, i) => v + (b[i] - v) * t);
  }, []);

  const getBlockProgress = useCallback((blockIndex) => {
    const blockZ = cubeZ + blockIndex * 2.5;
    const bottom = 2;
    const center = 0;
    const top = -2;

    if (blockZ >= bottom) return 0;
    if (blockZ <= top) return 1;
    if (blockZ > center) {
      return 0.5 * (1 - (blockZ - center) / (bottom - center));
    } else {
      return 0.5 + 0.5 * (1 - (blockZ - top) / (center - top));
    }
  }, [cubeZ]);

  const getBlockRotation = useCallback((blockIndex, centerRotation) => {
    const progress = getBlockProgress(blockIndex);
    if (progress <= 0.5) {
      return lerpArray(startRotations[blockIndex], centerRotation, progress / 0.5);
    } else {
      return lerpArray(centerRotation, endRotations[blockIndex], (progress - 0.5) / 0.5);
    }
  }, [getBlockProgress, lerpArray, startRotations, endRotations]);

  const [openOverlay, setOpenOverlay] = useState(null); // null, 0, 1, 2, 3
  const [movingBlockY, setMovingBlockY] = useState(null); // null or block index
  const [blockY, setBlockY] = useState([0, 0, 0, 0]); // Y position for animation for each block
  const [returningBlockY, setReturningBlockY] = useState(false);
  const cameraRef = useRef();

  useEffect(() => {
    if (openOverlay !== null) return; // Don't scroll when overlay is open!
    const handleWheel = (e) => {
      setCubeZ((prev) => {
        let next = prev - e.deltaY * 0.002;
        next = Math.max(-9, Math.min(2, next));
        return next;
      });
    };
    window.addEventListener('wheel', handleWheel, { passive: false });
    return () => window.removeEventListener('wheel', handleWheel);
  }, [openOverlay]);

  useEffect(() => {
    if (movingBlockY !== null) {
      let start = null;
      const duration = 500; // ms
      const startY = 0;
      const endY = 3; // Move blocks up to bring them toward camera view
      const blockIdx = movingBlockY;
      const animate = (timestamp) => {
        if (!start) start = timestamp;
        const elapsed = timestamp - start;
        const t = Math.min(elapsed / duration, 1);
        // Ease in (quadratic)
        const y = startY + (endY - startY) * (t * t);
        setBlockY(prev => {
          const arr = [...prev];
          arr[blockIdx] = y;
          return arr;
        });
        if (t < 1) {
          requestAnimationFrame(animate);
        } else {
          setBlockY(prev => {
            const arr = [...prev];
            arr[blockIdx] = endY;
            return arr;
          });
          setMovingBlockY(null);
          setOpenOverlay(blockIdx);
        }
      };
      requestAnimationFrame(animate);
    }
    // eslint-disable-next-line
  }, [movingBlockY]);

  useEffect(() => {
    if (returningBlockY !== false && returningBlockY !== null) {
      let start = null;
      const duration = 500; // ms
      const startY = 3;
      const endY = 0;
      const blockIdx = returningBlockY;
      const animate = (timestamp) => {
        if (!start) start = timestamp;
        const elapsed = timestamp - start;
        const t = Math.min(elapsed / duration, 1);
        // Ease out (quadratic)
        const y = startY + (endY - startY) * (t * t);
        setBlockY(prev => {
          const arr = [...prev];
          arr[blockIdx] = y;
          return arr;
        });
        if (t < 1) {
          requestAnimationFrame(animate);
        } else {
          setBlockY(prev => {
            const arr = [...prev];
            arr[blockIdx] = endY;
            return arr;
          });
          setReturningBlockY(false);
          setOpenOverlay(null);
        }
      };
      requestAnimationFrame(animate);
    }
    // eslint-disable-next-line
  }, [returningBlockY]);

  // Memoized active block calculation
  const activeBlock = useMemo(() => {
    const zPositions = [cubeZ, cubeZ + 2.5, cubeZ + 5, cubeZ + 7.5];
    let minDist = Infinity, idx = -1;
    zPositions.forEach((z, i) => {
      if (Math.abs(z) < minDist) {
        minDist = Math.abs(z);
        idx = i;
      }
    });
    return minDist > 1.5 ? -1 : idx;
  }, [cubeZ]);

  // Optimized cursor handlers
  const handlePointerOver = useCallback((blockIdx) => {
    if (activeBlock === blockIdx) document.body.style.cursor = 'pointer';
  }, [activeBlock]);

  const handlePointerOut = useCallback(() => {
    document.body.style.cursor = '';
  }, []);

  // Simple mouse tracking - exactly like HeroSection (no throttling)
  const [lerpedMouse, setLerpedMouse] = useState({ x: 0, y: 0 });

  useEffect(() => {
    let frame;

    const handleMouseMove = (e) => {
      const x = (e.clientX / window.innerWidth) * 2 - 1;
      const y = (e.clientY / window.innerHeight) * 2 - 1;
      setLerpedMouse(prev => {
        const lerp = (a, b, t) => a + (b - a) * t;
        return {
          x: lerp(prev.x, x, 0.035),
          y: lerp(prev.y, y, 0.035)
        };
      });
    };

    const animate = () => {
      setLerpedMouse(prev => {
        const lerp = (a, b, t) => a + (b - a) * t;
        return {
          x: lerp(prev.x, prev.x, 0.035),
          y: lerp(prev.y, prev.y, 0.035)
        };
      });
      frame = requestAnimationFrame(animate);
    };

    window.addEventListener('mousemove', handleMouseMove);
    animate();

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      cancelAnimationFrame(frame);
    };
  }, []);

  // Memoized orbit rotation function
  const getOrbitRotation = useCallback((baseRotation, mouse, strength = 0.25) => {
    const maxAngle = Math.PI / 6;
    return [
      baseRotation[0] - mouse.y * maxAngle * strength,
      baseRotation[1] - mouse.x * maxAngle * strength,
      baseRotation[2]
    ];
  }, []);

  const handleCloseOverlay = (blockIdx) => {
    setReturningBlockY(blockIdx);
  };

  return (
    <div style={{ position: 'relative', width: '100vw', height: '100vh' }}>
      {/* Overlays for animated block popups */}
      {openOverlay === 0 && <Block1Overlay onClose={() => handleCloseOverlay(0)} />}
      {openOverlay === 1 && <Block2Overlay onClose={() => handleCloseOverlay(1)} />}
      {openOverlay === 2 && <Block3Overlay onClose={() => handleCloseOverlay(2)} />}
      {openOverlay === 3 && <Block4Overlay onClose={() => handleCloseOverlay(3)} />}

      {/* Fixed 3D background - exactly like HeroSection */}
      <Canvas
        camera={{ position: [0, 0, 3], fov: 45 }}
        onCreated={({ camera }) => { cameraRef.current = camera; }}
        gl={{
          alpha: true,
          antialias: true, // Re-enabled for better visual quality
          powerPreference: 'high-performance',
          clearColor: [0, 0, 0, 1],
          pixelRatio: Math.min(window.devicePixelRatio, 2), // Limit pixel ratio for performance
          stencil: false, // Disable stencil buffer
          depth: true,
          logarithmicDepthBuffer: false, // Disable for better performance
        }}
        dpr={[1, 2]} // Limit device pixel ratio
        performance={{ min: 0.5 }} // Enable automatic performance scaling
        style={{ position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', zIndex: 0 }}
      >
        <Suspense fallback={null}>
          <Block1
            position={[0, blockY[0], cubeZ]}
            scale={[0.4, 0.4, 0.4]}
            rotation={activeBlock === 0 ? getOrbitRotation(getBlockRotation(0, rotation1), lerpedMouse) : getBlockRotation(0, rotation1)}
            onClick={() => {
              if (activeBlock === 0 && movingBlockY === null) {
                setMovingBlockY(0);
              }
            }}
            onPointerOver={() => handlePointerOver(0)}
            onPointerOut={handlePointerOut}
          />
          <Block2
            position={[0, blockY[1], cubeZ + 2.5]}
            scale={[0.4, 0.4, 0.4]}
            rotation={activeBlock === 1 ? getOrbitRotation(getBlockRotation(1, rotation2), lerpedMouse) : getBlockRotation(1, rotation2)}
            onClick={() => {
              if (activeBlock === 1 && movingBlockY === null) {
                setMovingBlockY(1);
              }
            }}
            onPointerOver={() => handlePointerOver(1)}
            onPointerOut={handlePointerOut}
          />
          <Block3
            position={[0, blockY[2], cubeZ + 5]}
            scale={[0.4, 0.4, 0.4]}
            rotation={activeBlock === 2 ? getOrbitRotation(getBlockRotation(2, rotation3), lerpedMouse) : getBlockRotation(2, rotation3)}
            onClick={() => {
              if (activeBlock === 2 && movingBlockY === null) {
                setMovingBlockY(2);
              }
            }}
            onPointerOver={() => handlePointerOver(2)}
            onPointerOut={handlePointerOut}
          />
          <Block4
            position={[0, blockY[3], cubeZ + 7.5]}
            scale={[0.4, 0.4, 0.4]}
            rotation={activeBlock === 3 ? getOrbitRotation(getBlockRotation(3, rotation4), lerpedMouse) : getBlockRotation(3, rotation4)}
            onClick={() => {
              if (activeBlock === 3 && movingBlockY === null) {
                setMovingBlockY(3);
              }
            }}
            onPointerOver={() => handlePointerOver(3)}
            onPointerOut={handlePointerOut}
          />
          {/* Always render background - exactly like HeroSection */}
          <WaterMatcapBackground position={[0, 0, 0]} />
        </Suspense>
      </Canvas>

      {/* Overlay content (not interactive) - exactly like HeroSection */}
      <div style={{ position: 'relative', zIndex: 2, pointerEvents: 'none' }}>
        <div className={styles.overlayContainer}>
        {/* Section 1 floating texts, absolutely positioned relative to overlayContainer */}
        {activeBlock === 0 && (
          <>
            <span className={`${styles.block1CenterText} ${styles.block1CenterText1}`}>WEG ZU AI<br/>BLCK #1</span>
            <span className={`${styles.block1CenterText} ${styles.block1CenterText2}`}>#BUILD #001<br/>KLICKE FÜR DETAILS</span>
            <span className={`${styles.block1CenterText} ${styles.block1CenterText3}`}>POSITION<br/>X: 32.52<br/>Y: 15.07</span>
          </>
        )}
        {activeBlock === 1 && (
          <>
            <span className={`${styles.block1CenterText} ${styles.block1CenterText1}`}>WEG ZU AI<br/>BLCK #2</span>
            <span className={`${styles.block1CenterText} ${styles.block1CenterText2}`}>#BUILD #002<br/>KLICKE FÜR DETAILS</span>
            <span className={`${styles.block1CenterText} ${styles.block1CenterText3}`}>POSITION<br/>X: 40.00<br/>Y: 20.00</span>
          </>
        )}
        {activeBlock === 2 && (
          <>
            <span className={`${styles.block1CenterText} ${styles.block1CenterText1}`}>WEG ZU AI<br/>BLCK #3</span>
            <span className={`${styles.block1CenterText} ${styles.block1CenterText2}`}>#BUILD #003<br/>KLICKE FÜR DETAILS</span>
            <span className={`${styles.block1CenterText} ${styles.block1CenterText3}`}>POSITION<br/>X: 50.00<br/>Y: 25.00</span>
          </>
        )}
        {activeBlock === 3 && (
          <>
            <span className={`${styles.block1CenterText} ${styles.block1CenterText1}`}>WEG ZU AI<br/>BLCK #4</span>
            <span className={`${styles.block1CenterText} ${styles.block1CenterText2}`}>#BUILD #004<br/>KLICKE FÜR DETAILS</span>
            <span className={`${styles.block1CenterText} ${styles.block1CenterText3}`}>POSITION<br/>X: 60.00<br/>Y: 30.00</span>
          </>
        )}

        {/* Section 1: Block1 in view */}
        <div className={activeBlock === 0 ? styles.block1CardWrapper : styles.hidden} style={{ pointerEvents: 'none' }}>
          <div className={styles.block1Container}>
            <span className={styles.block1TopLabel}>01 BLOCK</span>
            <div className={styles.block1Card}>
              <h2 className={styles.block1Headline}>WAS IST KI-AUTOMATION?</h2>
              <div className={styles.block1HeaderRow}>
                <div className={styles.block1HeaderLeft}>
                  <span>SYSTEME, DIE DENKEN,<br/>LERNEN - UND<br/>AUTOMATISCH HANDELN.</span>
                </div>
                <div className={styles.block1HeaderRight}>
                  <span>#1 KI-EINFÜHRUNG</span>
                </div>
              </div>
              <div className={styles.block1DottedDivider}>
                {Array.from({length: 30}).map((_,i) => <span key={i}>.</span>)}
              </div>
              <div className={styles.block1BodyRow}>
                <div className={styles.block1BodyLeft}>
                  <div className={styles.block1ProcessType}>PROCESS TYPE: AUTONOMOUS</div>
                  <svg className={styles.block1Barcode} width="100" height="60" viewBox="0 0 100 90">
                    <rect x="0" y="0" width="2" height="125" fill="#000"/>
                    <rect x="4" y="0" width="5" height="90" fill="#000"/>
                    <rect x="12" y="0" width="4" height="90" fill="#000"/>
                    <rect x="19" y="0" width="4" height="90" fill="#000"/>
                    <rect x="26" y="0" width="4" height="90" fill="#000"/>
                    <rect x="29" y="0" width="1" height="90" fill="#000"/>
                    <rect x="33" y="0" width="3" height="90" fill="#000"/>
                    <rect x="40" y="0" width="2" height="90" fill="#000"/>
                    <rect x="46" y="0" width="1" height="90" fill="#000"/>
                    <rect x="50" y="0" width="2" height="90" fill="#000"/>
                    <rect x="57" y="0" width="2" height="90" fill="#000"/>
                    <rect x="62" y="0" width="1" height="90" fill="#000"/>
                    <rect x="66" y="0" width="2" height="90" fill="#000"/>
                    <rect x="73" y="0" width="2" height="90" fill="#000"/>
                    <rect x="79" y="0" width="1" height="90" fill="#000"/>
                    <rect x="83" y="0" width="2" height="90" fill="#000"/>
                    <rect x="90" y="0" width="1" height="90" fill="#000"/>
                  </svg>
                </div>
                <div className={styles.block1BodyRight}>
                  <div className={styles.block1LogoRow}>
                    <span className={styles.block1Logo}>BLCKS<sup className={styles.superscript}>®</sup></span>
                  </div>
                  <div className={styles.block1RefId}>BLCKS KI SYSTEMS<br/>REFERENCE ID 2023241</div>
                  <div className={styles.block1Principle}>PRINZIP:<br/>ERKENNEN<br/>ENTSCHEIDEN<br/>AUTOMATISIEREN</div>
                  <div className={styles.block1Goal}>ZIEL:<br/>ROUTINE AUF NULL.<br/>KREATIVITÄT AUF MAX.</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Section 2: Block2 in view */}
        <div className={activeBlock === 1 ? styles.block1CardWrapper : styles.hidden} style={{ pointerEvents: 'none' }}>
          <div className={styles.block1Container}>
            <span className={styles.block1TopLabel}>02 BLOCK</span>
            <div className={styles.block1Card}>
              <h2 className={styles.block1Headline}>KI REALITY-CHECK</h2>
              <div className={styles.block1HeaderRow}>
                <div className={styles.block1HeaderLeft}>
                  <span>WAS KI BEREITS KANN<br/>UND WAS SIE DAFÜR<br/>MITBRINGEN MÜSSEN.</span>
                </div>
                <div className={styles.block1HeaderRight}>
                  <span>#2 KI-EINFÜHRUNG</span>
                </div>
              </div>
              <div className={styles.block1DottedDivider}>
                {Array.from({length: 30}).map((_,i) => <span key={i}>.</span>)}
              </div>
              <div className={styles.block1BodyRow}>
                <div className={styles.block1BodyLeft}>
                  <div className={styles.block1ProcessType}>PROCESS TYPE: AUTONOMOUS</div>
                  <svg className={styles.block1Barcode} width="100" height="60" viewBox="0 0 100 90">
                    <rect x="0" y="0" width="2" height="125" fill="#000"/>
                    <rect x="4" y="0" width="5" height="90" fill="#000"/>
                    <rect x="12" y="0" width="4" height="90" fill="#000"/>
                    <rect x="19" y="0" width="4" height="90" fill="#000"/>
                    <rect x="26" y="0" width="4" height="90" fill="#000"/>
                    <rect x="29" y="0" width="1" height="90" fill="#000"/>
                    <rect x="33" y="0" width="3" height="90" fill="#000"/>
                    <rect x="40" y="0" width="2" height="90" fill="#000"/>
                    <rect x="46" y="0" width="1" height="90" fill="#000"/>
                    <rect x="50" y="0" width="2" height="90" fill="#000"/>
                    <rect x="57" y="0" width="2" height="90" fill="#000"/>
                    <rect x="62" y="0" width="1" height="90" fill="#000"/>
                    <rect x="66" y="0" width="2" height="90" fill="#000"/>
                    <rect x="73" y="0" width="2" height="90" fill="#000"/>
                    <rect x="79" y="0" width="1" height="90" fill="#000"/>
                    <rect x="83" y="0" width="2" height="90" fill="#000"/>
                    <rect x="90" y="0" width="1" height="90" fill="#000"/>
                  </svg>
                </div>
                <div className={styles.block1BodyRight}>
                  <div className={styles.block1LogoRow}>
                    <span className={styles.block1Logo}>BLCKS<sup className={styles.superscript}>®</sup></span>
                  </div>
                  <div className={styles.block1RefId}>BLCKS KI SYSTEMS<br/>REFERENCE ID 2023241</div>
                  <div className={styles.block1Principle}>PRINZIP:<br/>ERKENNEN<br/>ENTSCHEIDEN<br/>AUTOMATISIEREN</div>
                  <div className={styles.block1Goal}>ZIEL:<br/>ROUTINE AUF NULL.<br/>KREATIVITÄT AUF MAX.</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Section 3: Block3 in view */}
        <div className={activeBlock === 2 ? styles.block1CardWrapper : styles.hidden} style={{ pointerEvents: 'none' }}>
          <div className={styles.block1Container}>
            <span className={styles.block1TopLabel}>03 BLOCK</span>
            <div className={styles.block1Card}>
              <h2 className={styles.block1Headline}>WEG ZUR KI-AUTOMATION</h2>
              <div className={styles.block1HeaderRow}>
                <div className={styles.block1HeaderLeft}>
                  <span>SO WIRD KI ZUR<br/>TREIBENDEN KRAFT IHRER<br/>DIGITALEN PROZESSE.</span>
                </div>
                <div className={styles.block1HeaderRight}>
                  <span>#3 KI-EINFÜHRUNG</span>
                </div>
              </div>
              <div className={styles.block1DottedDivider}>
                {Array.from({length: 30}).map((_,i) => <span key={i}>.</span>)}
              </div>
              <div className={styles.block1BodyRow}>
                <div className={styles.block1BodyLeft}>
                  <div className={styles.block1ProcessType}>PROCESS TYPE: AUTONOMOUS</div>
                  <svg className={styles.block1Barcode} width="100" height="60" viewBox="0 0 100 90">
                    <rect x="0" y="0" width="2" height="125" fill="#000"/>
                    <rect x="4" y="0" width="5" height="90" fill="#000"/>
                    <rect x="12" y="0" width="4" height="90" fill="#000"/>
                    <rect x="19" y="0" width="4" height="90" fill="#000"/>
                    <rect x="26" y="0" width="4" height="90" fill="#000"/>
                    <rect x="29" y="0" width="1" height="90" fill="#000"/>
                    <rect x="33" y="0" width="3" height="90" fill="#000"/>
                    <rect x="40" y="0" width="2" height="90" fill="#000"/>
                    <rect x="46" y="0" width="1" height="90" fill="#000"/>
                    <rect x="50" y="0" width="2" height="90" fill="#000"/>
                    <rect x="57" y="0" width="2" height="90" fill="#000"/>
                    <rect x="62" y="0" width="1" height="90" fill="#000"/>
                    <rect x="66" y="0" width="2" height="90" fill="#000"/>
                    <rect x="73" y="0" width="2" height="90" fill="#000"/>
                    <rect x="79" y="0" width="1" height="90" fill="#000"/>
                    <rect x="83" y="0" width="2" height="90" fill="#000"/>
                    <rect x="90" y="0" width="1" height="90" fill="#000"/>
                  </svg>
                </div>
                <div className={styles.block1BodyRight}>
                  <div className={styles.block1LogoRow}>
                    <span className={styles.block1Logo}>BLCKS<sup className={styles.superscript}>®</sup></span>
                  </div>
                  <div className={styles.block1RefId}>BLCKS KI SYSTEMS<br/>REFERENCE ID 2023241</div>
                  <div className={styles.block1Principle}>PRINZIP:<br/>ERKENNEN<br/>ENTSCHEIDEN<br/>AUTOMATISIEREN</div>
                  <div className={styles.block1Goal}>ZIEL:<br/>ROUTINE AUF NULL.<br/>KREATIVITÄT AUF MAX.</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Section 4: Block4 in view */}
        <div className={activeBlock === 3 ? styles.block1CardWrapper : styles.hidden} style={{ pointerEvents: 'none' }}>
          <div className={styles.block1Container}>
            <span className={styles.block1TopLabel}>04 BLOCK</span>
            <div className={styles.block1Card}>
              <h2 className={styles.block1Headline}>ROI DER ZUKUNFT</h2>
              <div className={styles.block1HeaderRow}>
                <div className={styles.block1HeaderLeft}>
                  <span>WARUM SICH KI-AUTOMATION<br/>SCHNELLER BEZAHLT MACHT,<br/>ALS SIE DENKEN.</span>
                </div>
                <div className={styles.block1HeaderRight}>
                  <span>#4 KI-EINFÜHRUNG</span>
                </div>
              </div>
              <div className={styles.block1DottedDivider}>
                {Array.from({length: 30}).map((_,i) => <span key={i}>.</span>)}
              </div>
              <div className={styles.block1BodyRow}>
                <div className={styles.block1BodyLeft}>
                  <div className={styles.block1ProcessType}>PROCESS TYPE: AUTONOMOUS</div>
                  <svg className={styles.block1Barcode} width="100" height="60" viewBox="0 0 100 90">
                    <rect x="0" y="0" width="2" height="125" fill="#000"/>
                    <rect x="4" y="0" width="5" height="90" fill="#000"/>
                    <rect x="12" y="0" width="4" height="90" fill="#000"/>
                    <rect x="19" y="0" width="4" height="90" fill="#000"/>
                    <rect x="26" y="0" width="4" height="90" fill="#000"/>
                    <rect x="29" y="0" width="1" height="90" fill="#000"/>
                    <rect x="33" y="0" width="3" height="90" fill="#000"/>
                    <rect x="40" y="0" width="2" height="90" fill="#000"/>
                    <rect x="46" y="0" width="1" height="90" fill="#000"/>
                    <rect x="50" y="0" width="2" height="90" fill="#000"/>
                    <rect x="57" y="0" width="2" height="90" fill="#000"/>
                    <rect x="62" y="0" width="1" height="90" fill="#000"/>
                    <rect x="66" y="0" width="2" height="90" fill="#000"/>
                    <rect x="73" y="0" width="2" height="90" fill="#000"/>
                    <rect x="79" y="0" width="1" height="90" fill="#000"/>
                    <rect x="83" y="0" width="2" height="90" fill="#000"/>
                    <rect x="90" y="0" width="1" height="90" fill="#000"/>
                  </svg>
                </div>
                <div className={styles.block1BodyRight}>
                  <div className={styles.block1LogoRow}>
                    <span className={styles.block1Logo}>BLCKS<sup className={styles.superscript}>®</sup></span>
                  </div>
                  <div className={styles.block1RefId}>BLCKS KI SYSTEMS<br/>REFERENCE ID 2023241</div>
                  <div className={styles.block1Principle}>PRINZIP:<br/>ERKENNEN<br/>ENTSCHEIDEN<br/>AUTOMATISIEREN</div>
                  <div className={styles.block1Goal}>ZIEL:<br/>ROUTINE AUF NULL.<br/>KREATIVITÄT AUF MAX.</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div> {/* Close overlayContainer */}
      </div> {/* Close overlay content div */}
    </div> 
  );
}
