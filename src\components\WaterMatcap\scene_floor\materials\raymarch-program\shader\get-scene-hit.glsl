const float PI = 3.14159265359;

// --- <PERSON><PERSON> inlined snoise2 (2D simplex noise, Ashima Arts) ---
vec3 mod289(vec3 x) {
  return x - floor(x * (1.0 / 289.0)) * 289.0;
}
vec2 mod289(vec2 x) {
  return x - floor(x * (1.0 / 289.0)) * 289.0;
}
vec3 permute(vec3 x) {
  return mod289(((x*34.0)+1.0)*x);
}
float snoise2(vec2 v) {
  const vec4 C = vec4(0.211324865405187,  // (3.0-sqrt(3.0))/6.0
                      0.366025403784439,  // 0.5*(sqrt(3.0)-1.0)
                     -0.577350269189626,  // -1.0 + 2.0 * C.x
                      0.024390243902439); // 1.0 / 41.0
  vec2 i  = floor(v + dot(v, C.yy) );
  vec2 x0 = v -   i + dot(i, C.xx);
  vec2 i1;
  i1 = (x0.x > x0.y) ? vec2(1.0, 0.0) : vec2(0.0, 1.0);
  vec4 x12 = x0.xyxy + C.xxzz;
  x12.xy -= i1;
  i = mod289(i);
  vec3 p = permute( permute( i.y + vec3(0.0, i1.y, 1.0 ))
    + i.x + vec3(0.0, i1.x, 1.0 ));
  vec3 m = max(0.5 - vec3(dot(x0,x0), dot(x12.xy,x12.xy), dot(x12.zw,x12.zw)), 0.0);
  m = m*m ;
  m = m*m ;
  vec3 x = 2.0 * fract(p * C.www) - 1.0;
  vec3 h = abs(x) - 0.5;
  vec3 ox = floor(x + 0.5);
  vec3 a0 = x - ox;
  m *= 1.79284291400159 - 0.85373472095314 * ( a0*a0 + h*h );
  vec3 g;
  g.x  = a0.x  * x0.x  + h.x  * x0.y;
  g.yz = a0.yz * x12.xz + h.yz * x12.yw;
  return 130.0 * dot(m, g);
}
// --- End inlined snoise2 ---

// --- Begin inlined snoise3 (3D simplex noise, Ashima Arts) ---
vec4 mod289(vec4 x) {
  return x - floor(x * (1.0 / 289.0)) * 289.0;
}
vec4 permute(vec4 x) {
  return mod289(((x*34.0)+1.0)*x);
}
vec4 taylorInvSqrt(vec4 r) {
  return 1.79284291400159 - 0.85373472095314 * r;
}
float snoise3(vec3 v) {
  const vec2  C = vec2(1.0/6.0, 1.0/3.0) ;
  const vec4  D = vec4(0.0, 0.5, 1.0, 2.0);
  vec3 i  = floor(v + dot(v, C.yyy) );
  vec3 x0 =   v - i + dot(i, C.xxx) ;
  vec3 g = step(x0.yzx, x0.xyz);
  vec3 l = 1.0 - g;
  vec3 i1 = min( g.xyz, l.zxy );
  vec3 i2 = max( g.xyz, l.zxy );
  vec3 x1 = x0 - i1 + C.xxx;
  vec3 x2 = x0 - i2 + C.yyy;
  vec3 x3 = x0 - D.yyy;
  i = mod289(i);
  vec4 p = permute(
    permute(
      permute(
        i.z + vec4(0.0, i1.z, i2.z, 1.0))
      + i.y + vec4(0.0, i1.y, i2.y, 1.0))
    + i.x + vec4(0.0, i1.x, i2.x, 1.0)
  );
  float n_ = 0.142857142857; // 1.0/7.0
  vec3  ns = n_ * D.wyz - D.xzx;
  vec4 j = p - 49.0 * floor(p * ns.z * ns.z);
  vec4 x_ = floor(j * ns.z);
  vec4 y_ = floor(j - 7.0 * x_ );
  vec4 x = x_ *ns.x + ns.yyyy;
  vec4 y = y_ *ns.x + ns.yyyy;
  vec4 h = 1.0 - abs(x) - abs(y);
  vec4 b0 = vec4( x.xy, y.xy );
  vec4 b1 = vec4( x.zw, y.zw );
  vec4 s0 = floor(b0)*2.0 + 1.0;
  vec4 s1 = floor(b1)*2.0 + 1.0;
  vec4 sh = -step(h, vec4(0.0));
  vec4 a0 = b0.xzyw + s0.xzyw*sh.xxyy ;
  vec4 a1 = b1.xzyw + s1.xzyw*sh.zzww ;
  vec3 p0 = vec3(a0.xy,h.x);
  vec3 p1 = vec3(a0.zw,h.y);
  vec3 p2 = vec3(a1.xy,h.z);
  vec3 p3 = vec3(a1.zw,h.w);
  vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));
  p0 *= norm.x;
  p1 *= norm.y;
  p2 *= norm.z;
  p3 *= norm.w;
  vec4 m = max(0.6 - vec4(dot(x0,x0), dot(x1,x1), dot(x2,x2), dot(x3,x3)), 0.0);
  m = m * m;
  return 42.0 * dot( m*m, vec4( dot(p0,x0), dot(p1,x1),
                                dot(p2,x2), dot(p3,x3) ) );
}
// --- End inlined snoise3 ---

// --- Begin inlined valueRemap ---
float valueRemap(
  float value,
  float min,
  float max,
  float newMin,
  float newMax
) {
  return newMin + (value - min) * (newMax - newMin) / (max - min);
}
// --- End inlined valueRemap ---

uniform vec3 uHitPosition;
uniform float noiseScale;
uniform float noiseLength;
uniform sampler2D uFlowTexture;
uniform float pyramidReveal;
uniform sampler2D uNoiseTexture;
uniform float mouseSpeed;
uniform mat4 uPyramidMatrix;
uniform float uFlowSize;
// AVAILABLE: uniform float time;

float sdSphere(vec3 position, float radius) {
  return length(position) - radius;
}

float sdPlane(vec3 position) {
  return position.y;
}

float tetrahedron(vec3 p, float size) {
  p = (uPyramidMatrix * vec4(p, 1.0)).xyz;
  p /= size;
  float d = (max(abs(p.x + p.y) - p.z, abs(p.x - p.y) + p.z) - 1.0) / sqrt(3.0);
  return d * size;
}

float plane(vec3 p, vec3 c, vec3 n) {
  return dot(p - c, n);
}

// sdf functions

float opUnion(float d1, float d2) {
  return min(d1, d2);
}

float opIntersection(float d1, float d2) {
  return max(d1, d2);
}

float opSmoothUnion(float d1, float d2, float k) {
  float h = clamp(0.5 + 0.5 * (d2 - d1) / k, 0.0, 1.0);
  return mix(d2, d1, h) - k * h * (1.0 - h);
}

// remap functions

float gain(float x, float k) {
  float a = 0.5 * pow(2.0 * (x < 0.5 ? x : 1.0 - x), k);
  return x < 0.5
    ? a
    : 1.0 - a;
}

float expStep(float x, float n) {
  return exp2(-exp2(n) * pow(x, n));
}

// a when t = 0
// b when t = 0.5
// c when t = 1
float mix3(float a, float b, float c, float t) {
  if (t < 0.5) {
    return mix(a, b, t * 2.0);
  } else {
    return mix(b, c, (t - 0.5) * 2.0);
  }
}

float almostUnitIdentity(float x) {
  return x * x * (2.0 - x);
}

// blur

vec4 blurTexture(sampler2D sam, vec2 uv) {
  vec2 e = vec2(1.0) / vec2(textureSize(sam, 0));
  vec4 sum = vec4(0.0);
  float weight = 0.0;

  // Gaussian kernel weights
  float kernel[9] = float[](
    0.077847,
    0.123317,
    0.077847,
    0.123317,
    0.195346,
    0.123317,
    0.077847,
    0.123317,
    0.077847
  );

  // 3x3 kernel
  for (int i = -1; i <= 1; i++) {
    for (int j = -1; j <= 1; j++) {
      vec2 offset = vec2(float(i), float(j)) * e;
      float w = kernel[(i + 1) * 3 + (j + 1)];
      sum += texture(sam, uv + offset) * w;
      weight += w;
    }
  }

  return sum / weight;
}

// noise functions
vec3 getNoise(vec2 uv) {
  vec3 noise = texture(uNoiseTexture, uv).xyz;
  return noise;
}

float getCircleSin(vec3 p) {
  float d = distance(p, uHitPosition * 0.5);
  float s = sin(d * 30.0);
  return s * 0.5 + 0.5;
}

// objects
float getOrbeHit(vec3 p) {
  vec3 orbeP = p;
  float pyramidMinP = -0.8;
  float pyramidMaxP = 0.5;
  float orbeYPos = mix(pyramidMinP, pyramidMaxP, pyramidReveal);

  float noiseAmmount = 1.0 - pyramidReveal;

  if (noiseAmmount > 0.0) {
    float noise = snoise3(
      (orbeP.xyz + vec3(0.0, time * 0.1 - orbeYPos * 0.9, time * 0.2)) * 5.0
    );

    float noiseMult = 1.0 - expStep(noiseAmmount, 2.0);

    // hard noise
    float noise1 = noise;
    // noise1 = pow(noise1, 0.5);
    noise1 *= noiseMult;

    // orbeP.x += noise1 * 0.01;
    orbeP.z += noise1 * 0.06;
    orbeP.y += noise * 0.1 * noiseMult;
  }

  orbeP -= vec3(0.0, orbeYPos, 0.0);
  float scale = 0.2;
  return tetrahedron(orbeP, 0.2);
}

float getSpikesHit(vec3 pos, float flow) {
  vec3 p = pos;

  float shiftInlfuence = p.y * 10.0;

  p -= uHitPosition;
  float dist = length(p) * 2.0;
  vec3 direction = normalize(p);

  float dist2 = dist - 0.01;
  dist2 = max(dist2, 0.0);
  p.xz -= direction.xz * shiftInlfuence * dist2 * 0.1;

  p += uHitPosition;

  p += uHitPosition * 0.2;
  float sinCos = sin(p.x * 60.0) * cos(p.z * 60.0);
  return sinCos;
}

float flowEdge = 0.4;

float getFlowHit(vec3 p) {
  vec2 uv = p.xz;
  uv = vec2(
    valueRemap(uv.x, -uFlowSize, uFlowSize, 0.0, 1.0),
    valueRemap(uv.y, -uFlowSize, uFlowSize, 0.0, 1.0)
  );
  uv = clamp(uv, 0.0, 1.0);
  uv.y = 1.0 - uv.y;
  float flow = texture(uFlowTexture, uv).x;
  // flow = smoothstep(0.0, 1.0, flow);
  // remap from 0-1 to -1-1
  flow *= 2.0;
  flow -= 1.0;

  // flow = almostUnitIdentity(flow);
  // flow = -0.2;

  flow *= 0.2;

  // smoot out to edges
  float edge = smoothstep(0.0, flowEdge, uv.x);
  edge *= smoothstep(0.0, flowEdge, uv.y);
  edge *= smoothstep(1.0, 1.0 - flowEdge, uv.x);
  edge *= smoothstep(1.0, 1.0 - flowEdge, uv.y);
  // edge = 1.0 - edge;

  flow *= edge;

  return flow;
}

float getFloorHit(vec3 p) {
  // plane with flow
  float flow = getFlowHit(p);
  float planeY = 0.0;
  planeY += flow;
  vec3 pPlane = p - vec3(0.0, planeY, 0.0);
  float plane = sdPlane(pPlane);
  return plane;
}

float getSceneHit(vec3 p) {
  float floorHit = getFloorHit(p);
  return floorHit * 0.3;

  float noise =
    snoise3((p.xyz * 1.5 + vec3(0.0, time * 2.1, time * 0.2)) * 5.0) * 0.5 +
    0.5;

  float orbeHit = getOrbeHit(p);

  float sdf = orbeHit;

  float smoothFactor = smoothstep(0.0, 0.1, pyramidReveal);
  float inverseFactor = 1.0 - smoothstep(0.8, 1.0, pyramidReveal);

  smoothFactor *= inverseFactor;
  smoothFactor *= noise;

  if (pyramidReveal < 1.95) {
    float floorHit = getFloorHit(p);
    sdf = opSmoothUnion(sdf, floorHit, 0.3 * smoothFactor);
  }

  return sdf * 0.3;
}

// float getSceneHitOld(vec3 p) {
//   float planeY = 0.0;
//   float flow = getFlowHit(p);
//   float clampFlow = clamp(flow, 0.0, 1.0);

//   float ferroFlow = smoothstep(0.7, 1.0, flow);

//   float spikes = getSpikesHit(p, flow);
//   spikes = ferroFlow * spikes * 0.5;
//   spikes *= 0.1;

//   float bubble = smoothstep(0.7, 1.0, flow) * 0.05;
//   planeY += spikes + bubble;

//   float circleSin = getCircleSin(p);

//   float noise = 0.0;
//   noise += getNoise2(p);
//   // remap flow from 0 to 1 to 0-1-0
//   noise *= cos(clampFlow * PI * 2.0 + PI) * 0.5 + 0.5;
//   noise *= circleSin;
//   noise *= mouseSpeed;
//   planeY += noise * 0.1;

//   vec3 pPlane = p - vec3(0.0, planeY, 0.0);
//   float plane = sdPlane(pPlane);

//   // return plane;

//   float normalMixer = cos(time * 5.0) * 0.5 + 0.5;
//   normalMixer = gain(normalMixer, 3.0);

//   float orbeHit = getOrbeHit(p);

//   float hit;

//   // hit = orbeHit;

//   hit = mix3(
//     plane,
//     opSmoothUnion(plane, orbeHit, 0.5) + getNoise2(p) * 0.05,
//     orbeHit,
//     pyramidReveal
//   );

//   return hit;

// }
