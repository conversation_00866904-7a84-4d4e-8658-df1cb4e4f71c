import { useEffect, useRef, useState } from "react";
import * as THREE from "three";

const vertexShader = `
    varying vec2 v_Uv;

    void main() {
        v_Uv = uv;
        gl_Position = vec4(position, 1.0);
    }
`;

const fragmentShader = `
    precision highp float;

    uniform sampler2D u_Texture;
    uniform vec2 u_mouse;
    uniform float u_time;
    uniform vec2 u_resolution;
    uniform float u_radius;
    uniform float u_speed;
    uniform float u_imageAspect;
    uniform float u_turbulenceIntensity;

    varying vec2 v_Uv;

    float hash(vec2 p) {
        return fract(sin(dot(p, vec2(127.1, 311.7))) * 43758.5453);
    }

    float noise(vec2 p) {
        vec2 i = floor(p);
        vec2 f = fract(p);
        vec2 u = f * f * (3.0 - 2.0 * f);
        return mix(
            mix(hash(i + vec2(0.0, 0.0)), hash(i + vec2(1.0, 0.0)), u.x),
            mix(hash(i + vec2(0.0, 1.0)), hash(i + vec2(1.0, 1.0)), u.x),
            u.y
        );
    }

    float turbulence(vec2 p) {
        float t = 0.0;
        float w = 0.5;
        for (int i = 0; i < 8; i++) {
            t += abs(noise(p)) * w;
            p *= 2.0;
            w *= 0.5;
        }
        return t;
    }

    void main() {
        vec2 uv = v_Uv;
        float screenAspect = u_resolution.x / u_resolution.y;
        float ratio = u_imageAspect / screenAspect;

        // Adjust based on aspect ratio to make sure image isn't distorted
        vec2 texCoord = uv;
        if (ratio > 1.0) {
            texCoord.x = mix(0.5 - 0.5 / ratio, 0.5 + 0.5 / ratio, uv.x);
        } else {
            texCoord.y = mix(0.5 - 0.5 * ratio, 0.5 + 0.5 * ratio, uv.y);
        }

        vec4 tex = texture2D(u_Texture, texCoord);
        float gray = dot(tex.rgb, vec3(0.299, 0.587, 0.114));
        vec3 invertedGray = vec3(1.0 - gray);

        vec2 correctedUV = uv;
        correctedUV.x *= screenAspect;
        vec2 correctedMouse = u_mouse;
        correctedMouse.x *= screenAspect;

        float dist = distance(correctedUV, correctedMouse);
        float jaggedDist = dist + (turbulence(uv * 25.0 + u_time * u_speed) - 0.5) * u_turbulenceIntensity;
        float mask = step(jaggedDist, u_radius);

        vec3 finalColor = mix(invertedGray, tex.rgb, 1.0 - mask);
        gl_FragColor = vec4(finalColor, 1.0);
    }
`;

const InversionLens = ({ src, className }) => {
    const containerRef = useRef(null);
    const [loaded, setLoaded] = useState(false);
    const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
    
    // For Three.js
    const rendererRef = useRef(null);
    const sceneRef = useRef(null);
    const cameraRef = useRef(null);
    const uniformsRef = useRef(null);
    const textureRef = useRef(null);
    
    // Mouse tracking
    const mousePos = useRef({ x: 0.5, y: 0.5 });
    const lerpedMousePos = useRef({ x: 0.5, y: 0.5 });
    const targetRadius = useRef(0);
    const currentRadius = useRef(0);
    const isHovering = useRef(false);
    
    // Animation frame
    const animationFrameId = useRef(null);

    // Config
    const config = {
        maskRadius: 0.2,          // Size of the effect circle
        lerpSpeed: 0.1,           // How quickly mouse movement is smoothed
        radiusSpeed: 0.15,        // How quickly the effect fades in/out
        turbulence: 0.075         // Amount of edge distortion
    };

    // Initialize image and get dimensions
    useEffect(() => {
        // Pre-load the image to get its dimensions
        const img = new Image();
        img.src = src;
        img.onload = () => {
            setImageSize({
                width: img.width,
                height: img.height
            });
            setLoaded(true);
        };
        
        return () => {
            img.onload = null;
        };
    }, [src]);

    // Set up Three.js scene once image is loaded
    useEffect(() => {
        if (!loaded || !containerRef.current) return;
        
        let renderer, scene, camera, uniforms, texture, material, mesh;
        
        const init = () => {
            // Get container dimensions
            const containerWidth = containerRef.current.clientWidth;
            const containerHeight = containerRef.current.clientHeight;
            
            if (containerWidth === 0 || containerHeight === 0) {
                console.error("Container has zero width or height");
                return false;
            }
            
            // Create scene
            scene = new THREE.Scene();
            sceneRef.current = scene;
            
            // Orthographic camera for 2D rendering
            camera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);
            cameraRef.current = camera;
            
            // Load texture
            const textureLoader = new THREE.TextureLoader();
            texture = textureLoader.load(
                src,
                (loadedTexture) => {
                    // Everything loaded successfully
                    const imageAspect = imageSize.width / imageSize.height;
                    
                    uniforms = {
                        u_Texture: { value: loadedTexture },
                        u_mouse: { value: new THREE.Vector2(0.5, 0.5) },
                        u_time: { value: 0 },
                        u_resolution: { value: new THREE.Vector2(containerWidth, containerHeight) },
                        u_radius: { value: 0 },
                        u_speed: { value: 0.75 },
                        u_imageAspect: { value: imageAspect },
                        u_turbulenceIntensity: { value: config.turbulence }
                    };
                    uniformsRef.current = uniforms;
                    
                    // Create material with shaders
                    material = new THREE.ShaderMaterial({
                        uniforms,
                        vertexShader,
                        fragmentShader
                    });
                    
                    // Create mesh (plane)
                    mesh = new THREE.Mesh(new THREE.PlaneGeometry(2, 2), material);
                    scene.add(mesh);
                },
                undefined,
                (error) => {
                    console.error("Error loading texture:", error);
                }
            );
            textureRef.current = texture;
            
            // Create renderer
            renderer = new THREE.WebGLRenderer({ 
                antialias: true,
                alpha: true
            });
            renderer.setSize(containerWidth, containerHeight);
            renderer.setPixelRatio(window.devicePixelRatio);
            
            // Append to DOM
            if (containerRef.current.querySelector('canvas')) {
                containerRef.current.removeChild(containerRef.current.querySelector('canvas'));
            }
            containerRef.current.appendChild(renderer.domElement);
            rendererRef.current = renderer;
            
            return true;
        };
        
        const success = init();
        
        if (success) {
            // Add mouse event listeners
            const handleMouseMove = (e) => {
                const rect = containerRef.current.getBoundingClientRect();
                
                // Check if mouse is inside container
                const inside = 
                    e.clientX >= rect.left && 
                    e.clientX <= rect.right && 
                    e.clientY >= rect.top && 
                    e.clientY <= rect.bottom;
                
                isHovering.current = inside;
                
                if (inside) {
                    mousePos.current = {
                        x: (e.clientX - rect.left) / rect.width,
                        y: 1.0 - (e.clientY - rect.top) / rect.height
                    };
                    targetRadius.current = config.maskRadius;
                } else {
                    targetRadius.current = 0;
                }
            };
            
            // Add window resize handler
            const handleResize = () => {
                if (!containerRef.current || !rendererRef.current) return;
                
                const width = containerRef.current.clientWidth;
                const height = containerRef.current.clientHeight;
                
                rendererRef.current.setSize(width, height);
                
                if (uniformsRef.current) {
                    uniformsRef.current.u_resolution.value.set(width, height);
                }
            };
            
            // Set up animation loop
            const animate = () => {
                if (!uniformsRef.current || !rendererRef.current || !sceneRef.current || !cameraRef.current) {
                    animationFrameId.current = requestAnimationFrame(animate);
                    return;
                }
                
                // Smoothly interpolate mouse position
                lerpedMousePos.current.x += (mousePos.current.x - lerpedMousePos.current.x) * config.lerpSpeed;
                lerpedMousePos.current.y += (mousePos.current.y - lerpedMousePos.current.y) * config.lerpSpeed;
                
                // Smoothly interpolate radius
                currentRadius.current += (targetRadius.current - currentRadius.current) * config.radiusSpeed;
                
                // Update uniforms
                uniformsRef.current.u_mouse.value.set(
                    lerpedMousePos.current.x,
                    lerpedMousePos.current.y
                );
                uniformsRef.current.u_time.value += 0.01;
                uniformsRef.current.u_radius.value = currentRadius.current;
                
                // Render
                rendererRef.current.render(sceneRef.current, cameraRef.current);
                
                // Continue animation loop
                animationFrameId.current = requestAnimationFrame(animate);
            };
            
            // Start animation
            animate();
            
            // Add event listeners
            document.addEventListener('mousemove', handleMouseMove);
            window.addEventListener('resize', handleResize);
            
            // Clean up
            return () => {
                document.removeEventListener('mousemove', handleMouseMove);
                window.addEventListener('resize', handleResize);
                
                if (animationFrameId.current) {
                    cancelAnimationFrame(animationFrameId.current);
                }
                
                if (rendererRef.current) {
                    rendererRef.current.dispose();
                    rendererRef.current = null;
                }
                
                if (textureRef.current) {
                    textureRef.current.dispose();
                    textureRef.current = null;
                }
            };
        }
    }, [loaded, src]);

    return (
        <div 
            ref={containerRef} 
            className={`inversion-lens ${className || ""}`}
            style={{ 
                position: "relative",
                width: "100%", 
                height: "100%",
                overflow: "hidden"
            }}
        >
            {/* Fallback/placeholder image */}
            <img 
                src={src} 
                alt="" 
                style={{ 
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                    opacity: loaded ? 0 : 1
                }} 
            />
        </div>
    );
};

export default InversionLens;