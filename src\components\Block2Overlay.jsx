import React from 'react';
import styles from './Block2Overlay.module.css';

export default function Block2Overlay({ onClose }) {
  return (
    <>
      <div className={styles.arrowRight}>
        <img src="/arrow_long.svg" alt="Arrow Right" width={40} height={40} />
      </div>
      <div className={styles.arrowLeft}>
        <img src="/arrow_long.svg" alt="Arrow Left" width={40} height={40} className={styles.arrowLeftImg} />
      </div>
      <button onClick={onClose} className={styles.closeButton} aria-label="Close overlay">CLOSE</button>
      <div className={styles.overlay}>
        <div className={styles.contentWrapper}>
          <h1 className={styles.title}>Bereit für KI Automation? <br/>Das braucht es wirklich.</h1>
          <p className={styles.description}>
            KI Automation lässt sich in fast jedem Unternehmen einsetzen – entscheidend ist, die richtigen Voraussetzungen zu schaffen. Der wichtigste Schritt ist, sich einen Überblick über die eigenen Prozesse zu verschaffen: Wo entstehen Engpässe, wo wiederholen sich Aufgaben, wo geht Zeit verloren?
          </p>
          <h2 className={styles.infoHeading}>Typische Anzeichen für Automatisierungspotenzial sind etwa:</h2>
          <div className={styles.statsGrid}>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statTag}>VERWALTUNG</div>
              <div className={styles.statDesc}>Datensynchronisation zwischen unterschiedlichen Tools oder Systemen</div>
            </div>
            <div className={styles.statCard}>&nbsp;</div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statTag}>KUNDENSUPPORT</div>
              <div className={styles.statDesc}>Standardisierte Antworten auf häufige Kundenfragen (z. B. zu Öffnungszeiten, Preisen, Lieferstatus)</div>
            </div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statTag}>KUNDENSUPPORT</div>
              <div className={styles.statDesc}>Eingehende Kontaktformulare oder Chat-Nachrichten werden manuell bearbeitet</div>
            </div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statTag}>VERWALTUNG</div>
              <div className={styles.statDesc}>Kein zentrales System für Kundendaten, wodurch Informationen mehrfach abgefragt werden müssen</div>
            </div>
            <div className={styles.statCard}>&nbsp;</div>
            <div className={styles.statCard}>&nbsp;</div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statTag}>SALES</div>
              <div className={styles.statDesc}>Leadqualifizierung und Nachverfolgung erfolgt händisch</div>
            </div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statTag}>MARKETING</div>
              <div className={styles.statDesc}>Manuelle Erstellung von Marketing-Content – z. B. Texte, Bilder oder Social-Media-Posts ohne Automatisierung oder KI-Unterstützung.</div>
            </div>
            
          </div>
          <div className={styles.divider}></div>
          <h2 className={styles.bigTitle}>Klare Ziele als Erfolgsfaktor</h2>
          <p className={styles.bigDescription}>
          Bevor AI Automation sinnvoll eingesetzt werden kann, braucht es ein klares Verständnis der Ziele: Geht es darum, Zeit zu sparen? Die Fehlerquote zu senken? Mitarbeitende zu entlasten? Oder die Kundenzufriedenheit zu steigern? Je präziser das Ziel, desto gezielter kann die passende Lösung ausgewählt und integriert werden. Eine gute Zieldefinition bildet das Fundament für messbare Erfolge und eine nachhaltige Automatisierungsstrategie.
          </p>
          <div className={styles.statsGrid}>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statTag}>ZEITERSPARNIS</div>
              <div className={styles.statDesc}>Routineaufgaben schneller abwickeln und Ressourcen effizienter nutzen</div>
            </div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statTag}>KOSTEN SENKEN</div>
              <div className={styles.statDesc}>Durch automatisierte Abläufe interne Kostenstrukturen optimieren.</div>
            </div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statTag}>WACHSTUM</div>
              <div className={styles.statDesc}>Skalierbare Prozesse schaffen, um mit weniger Aufwand mehr erreichen zu können.</div>
            </div>
            
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statTag}>FEHLERREDUKTION</div>
              <div className={styles.statDesc}>Manuelle Eingabefehler vermeiden – besonders bei Datenübertragungen und wiederkehrenden Prozessen.</div>
            </div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statTag}>ENTLASTUNG MITARBEITER</div>
              <div className={styles.statDesc}>Mitarbeitende sollen sich auf strategische und kreative Aufgaben konzentrieren können.</div>
            </div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statTag}>UMSATZ</div>
              <div className={styles.statDesc}>Durch schnellere Reaktionszeiten, personalisierte Angebote und automatisierte Verkaufsprozesse können mehr Leads konvertiert und zusätzliche Umsätze generiert werden – rund um die Uhr.</div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 