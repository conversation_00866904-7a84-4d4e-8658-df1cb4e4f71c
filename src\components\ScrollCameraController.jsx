import { useEffect, useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';

export default function ScrollCameraController({ onScrollProgress }) {
  const [scrollProgress, setScrollProgress] = useState(0); // 0 to 1
  const targetProgress = useRef(0);

  // Wheel event for virtual scroll
  useEffect(() => {
    function onWheel(e) {
      e.preventDefault();
      // Normalize wheel delta (positive = scroll down)
      const delta = e.deltaY || e.detail || e.wheelDelta;
      // Adjust sensitivity as needed
      targetProgress.current = Math.min(1, Math.max(0, targetProgress.current + delta * 0.0003));
    }
    window.addEventListener('wheel', onWheel, { passive: false });
    return () => window.removeEventListener('wheel', onWheel);
  }, []);

  // Touch support for mobile
  useEffect(() => {
    let lastY = null;
    function onTouchStart(e) {
      if (e.touches.length === 1) lastY = e.touches[0].clientY;
    }
    function onTouchMove(e) {
      if (e.touches.length === 1 && lastY !== null) {
        const delta = lastY - e.touches[0].clientY;
        targetProgress.current = Math.min(1, Math.max(0, targetProgress.current + delta * 0.01));
        lastY = e.touches[0].clientY;
      }
    }
    function onTouchEnd() { lastY = null; }
    window.addEventListener('touchstart', onTouchStart, { passive: false });
    window.addEventListener('touchmove', onTouchMove, { passive: false });
    window.addEventListener('touchend', onTouchEnd, { passive: false });
    return () => {
      window.removeEventListener('touchstart', onTouchStart);
      window.removeEventListener('touchmove', onTouchMove);
      window.removeEventListener('touchend', onTouchEnd);
    };
  }, []);

  // Smoothly animate scrollProgress towards targetProgress
  useFrame(() => {
    setScrollProgress(prev => {
      const lerped = prev + (targetProgress.current - prev) * 0.02;
      return Math.abs(lerped - targetProgress.current) < 0.001 ? targetProgress.current : lerped;
    });
    if (onScrollProgress) onScrollProgress(scrollProgress);
  });

  return null;
} 