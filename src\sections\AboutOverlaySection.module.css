.overlayBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.25);
  backdrop-filter: blur(16px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlayContent {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  padding: 2rem;
  overflow-y: auto;
  color: #fff;
  /* Hide scrollbar for Chrome, Safari and Opera */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;  /* IE and Edge */
}
.overlayContent::-webkit-scrollbar {
  display: none;
}

@media (min-width: 1200px) {
  .overlayContent {
    padding-left: 0;
    padding-right: 0;
  }
}

.closeButton {
  position: absolute;
  top: 16px;
  right: 16px;
  background: #eee;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  font-size: 20px;
  cursor: pointer;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
  pointer-events: auto;
  background: rgba(209, 15, 15, 0.2);
}
.closeButton:hover, .closeButton:focus {
  background: #ddd;
}

.section1Custom {
  min-height: 100vh;
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  position: relative;
}

.section1ContentWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  margin-left: 5rem;
  margin-bottom: 10rem;
  z-index: 2;
}

.section1SmallTexts {
  display: grid;
  width: 1000px;
  grid-template-columns: 80px 80px 240px 1fr;
  column-gap: 2rem;
  font-family: 'Supply', sans-serif;
  font-weight: 300;
  font-size: 0.80rem;
  letter-spacing: 0.08em;
  color: #fff;
  margin-bottom: 2.5rem;
}

.section1SmallTexts span {
  text-align: left;
}

.section1SmallTexts span:nth-child(2) {
  margin-right: 2rem; /* default gap */
}

.section1SmallTexts span:nth-child(3) {
  margin-left: 2rem; /* add extra gap before 3rd column */
}

.section1Subheading {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 2rem;
  color: #fff;
  margin-bottom: 1rem;
  letter-spacing: 0.01em;
}

.section1AboutHeading {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 5rem;
  color: #fff;
  line-height: 1.30;
  text-align: left;
  letter-spacing: 0;
  max-width: 1600px;
}

.section2Custom {
  min-height: 100vh;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.section2Left {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding-left: 4vw;
}

.section2Heading {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 4rem;
  color: #fff;
  text-align: left;
  max-width: 1600px;
  line-height: 1.25;
}

.section2Subheading {
  display: block;
  font-family: 'Supply', sans-serif;
  font-weight: 300;
  font-size: 1rem;
  color: #fff;
  margin-bottom: 0rem;
  margin-left: 0;
  text-align: left;
  letter-spacing: 0.05em;
}

.section2Right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 4vw;
}

.section2Paragraph {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 1.15rem;
  color: #fff;
  text-align: right;
  max-width: 400px;
  line-height: 1.5;
  opacity: 0.95;
}


.section3Custom {
  min-height: 125vh;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  position: relative;
  padding-top: 200px;
}

.section3ContentWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  margin-left: 5rem;
  margin-bottom: 0rem;
  z-index: 2;
}

.section3CardWrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  pointer-events: none;
  width: 1240px;
  max-width: 100vw;
  margin-top: 80px;
}

.section3CardRow {
  display: flex;
  flex-direction: row;
  gap: 20px;
  width: auto;
  align-items: flex-start;
  justify-content: center;
  pointer-events: none;
}

.section3Card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 1rem;
  padding: 1.5rem;
  width: 400px;
  height: 350px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  pointer-events: auto;
  flex: 0 0 400px;
  box-sizing: border-box;
}

.section3CardBarGraph img {
  width: 100%;
  height: 100px;
  object-fit: contain;
  display: block;
  margin-top: 1rem;
}

.section3Subheading {
  display: block;
  font-family: 'Supply', sans-serif;
  font-weight: 300;
  font-size: 1rem;
  color: #fff;
  margin-bottom: 0rem;
  margin-left: 0;
  text-align: left;
  letter-spacing: 0.05em;
}

.section3Heading {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 3.5rem;
  color: #fff;
  text-align: left;
  max-width: 100%;
  line-height: 1.25;
}

.section3Left {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding-left: 4vw;
}

.section3CenterSection {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.section3CardBarGraph {
  width: 100%;
  margin-bottom: 2rem;
}

.section3CardSmallTexts {
  display: grid;
  width: 100%;
  grid-template-columns: 60px 70px 100px 0px;
  column-gap: 2rem;
  font-family: 'Supply', sans-serif;
  font-weight: 200;
  font-size: 0.7rem;
  letter-spacing: 0.08em;
  color: #fff;
  margin-bottom: 1.5rem;
}

.section3CardSmallTexts span {
  text-align: left;
}

.section3CardHeading {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 1.3rem;
  color: #fff;
  margin-bottom: 0.5rem;
  letter-spacing: 0.05em;
}

.section3CardParagraph {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 0.85rem;
  color: #fff;
  line-height: 1.30;
  margin-top: 0rem;
}

.section4CardWrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: center;
  pointer-events: auto;
  height: 636px;
  width: fit-content;
  gap: 7rem;
  margin-left: auto;
  margin-right: auto;
}

.section4Card {
  background: rgba(255, 255, 255, 1);
  border-radius: 16px;
  width: 100%;
  height: 55%;
  padding: 0px 18px 18px 18px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  margin: 0;
  box-sizing: border-box;
  position: relative;
}

.section4Headline {
  font-size: 1.40rem;
  font-weight: 700;
  margin-bottom: 12px;
  color: #000000;
  font-family: 'Inter', 'Arial Black', Arial, sans-serif;
  letter-spacing: 0.01em;
}

.section4HeaderRow {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-size: 0.75rem;
  color: #181818;
  margin-bottom: 4px;
}
.section4HeaderLeft {
  flex: 2;
  text-align: left;
  font-size: 0.65rem;
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-weight: 300;
}
.section4HeaderRight {
  flex: 1;
  text-align: right;
  font-size: 0.65rem;
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-weight: 300;
}

.section4HeaderCenter {
  flex: 1;
  text-align: center;
  font-size: 0.65rem;
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-weight: 300;
  color: #181818;
}

.section4DottedDivider {
  width: 100%;
  text-align: center;
  color: #000000;
  font-size: 1.15rem;
  font-weight: 300;
  letter-spacing: 0.050em;
  margin: 4px 0 8px 0;
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  user-select: none;
}

.section4BodyRow {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  gap: 32px;
}

.section4BodyLeft {
  flex: 1.2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 6px;
}

.section4ProcessType {
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-size: 0.65rem;
  font-weight: 300;
  color: #000000;
  margin-bottom: 14px;
}

.section4Barcode {
  margin-top: 0px;
  width: 160px;
  height: 125px;
  display: block;
}

.section4BodyRight {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 4px;
  min-width: 100px;
  text-align: left;
  margin-top: 40px;
}

.section4LogoRow {
  width: 100%;
  display: flex;
  justify-content: flex-start;
}

.section4Logo {
  font-size: 1.5rem;
  font-weight: 900;
  font-family: 'Archivo Black', 'Arial Black', Arial, sans-serif;
  color: #000000;
  letter-spacing: 0.00em;
}

.section4Superscript {
  font-size: 0.6rem;
  vertical-align: super;
  margin-left: 2px;
}

.section4RefId {
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-size: 0.7rem;
  font-weight: 300;
  color: #000000;
  text-align: left;
  margin-bottom: 2px;
  margin-top: 6px;
}

.section4Principle, .section4Goal {
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-size: 0.7rem;
  font-weight: 300;
  color: #000000;
  text-align: left;
  margin-bottom: 2px;
}

.section4Container {
  width: 400px;
  height: 636px;
  background: rgba(255, 255, 255, 0.10);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  position: relative;
  padding: 0 16px 16px 16px;
  box-sizing: border-box;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.section4TopLabel {
  font-size: 2.1rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  font-family: 'Inter', 'Arial Black', Arial, sans-serif;
  letter-spacing: 0.01em;
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 2;
}

.section4Left {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding-left: 4vw;
}

.section4Subheading {
  display: block;
  font-family: 'Supply', sans-serif;
  font-weight: 300;
  font-size: 1rem;
  color: #fff;
  margin-bottom: 0rem;
  margin-left: 0;
  text-align: left;
  letter-spacing: 0.05em;
}

.section4Heading {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 3.5rem;
  color: #fff;
  text-align: left;
  max-width: 100%;
  line-height: 1.25;
}

