.scrollIndicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500px;
  height: 2px;
  background-color: rgba(70, 70, 70, 0.5); /* Dark grey background */
  display: block;
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 1000;
  border-radius: 2.5px; /* Slightly rounded edges */
  overflow: hidden;
}

.scrollIndicatorFill {
  position: absolute;
  height: 100%;
  width: 0%; /* Width will be set dynamically based on progress */
  background-color: white;
  transition: width 0.2s ease-out;
  left: 0; /* Always start from left */
}

.fadeOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000;
  opacity: 0;
  pointer-events: none; /* Allow clicks to pass through */
  transition: opacity 0.3s ease-out;
  z-index: 999; /* Just below the loading bar but above content */
}

.active {
  opacity: 1;
}