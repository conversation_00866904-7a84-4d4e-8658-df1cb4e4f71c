import React, { Suspense, useRef, useState, useEffect, useMemo } from 'react';
import { Canvas, use<PERSON>rame, useThree } from '@react-three/fiber';
import { useGLTF, /* OrbitControls, */ MeshTransmissionMaterial, Html, Text } from '@react-three/drei';
import WaterMatcapBackground from '../components/WaterMatcap/WaterMatcapBackground';
import ExplainOrbitCamera from '../components/WaterMatcap/scene_copy/ExplainOrbitCamera';
import { useControls } from 'leva';
import * as THREE from 'three';
import { createPortal } from 'react-dom';
import { MeshBVH, acceleratedRaycast } from 'three-mesh-bvh';

// Patch THREE.Mesh to use the accelerated raycast
THREE.Mesh.prototype.raycast = acceleratedRaycast;

function Block1(props) {
  const { nodes, materials } = useGLTF('/models/cube_high_comp.glb');
  return (
    <group {...props} dispose={null}>
      {/* Render all meshes in the GLB, or adjust as needed */}
      {Object.values(nodes).map((node, i) =>
        node.type === 'Mesh' ? (
          <mesh
            key={i}
            geometry={node.geometry}
            castShadow
            receiveShadow
          >
            <MeshTransmissionMaterial
              backside
              samples={4}
              thickness={3}
              chromaticAberration={0}
              anisotropy={0.1}
              distortion={0}
              distortionScale={0}
              temporalDistortion={0}
              iridescence={1}
              iridescenceIOR={1}
              iridescenceThicknessRange={[0, 1400]}
            />
          </mesh>
        ) : null
      )}
      {/* 3D Text label for Block1, only when Block1 is in view */}
      {props.showLabel && (
        <Text
          position={[0, 0.3, 0]}
          fontSize={0.13}
          color="#fff"
          anchorX="center"
          anchorY="bottom"
          font="/fonts/Inter_24pt-Regular.ttf"
          outlineColor="#000"
          outlineWidth={0.008}
        >
          {`MATERIAL_01\nALUMINIUM`}
        </Text>
      )}
    </group>
  );
}

// Block2 is a copy of Block1
function Block2(props) {
  const { nodes, materials } = useGLTF('/models/cube_high_comp.glb');
  return (
    <group {...props} dispose={null}>
      {Object.values(nodes).map((node, i) =>
        node.type === 'Mesh' ? (
          <mesh
            key={i}
            geometry={node.geometry}
            castShadow
            receiveShadow
          >
            <MeshTransmissionMaterial
              backside
              samples={4}
              thickness={3}
              chromaticAberration={0}
              anisotropy={0.1}
              distortion={0}
              distortionScale={0}
              temporalDistortion={0}
              iridescence={1}
              iridescenceIOR={1}
              iridescenceThicknessRange={[0, 1400]}
            />
          </mesh>
        ) : null
      )}
      {props.showLabel && (
        <Text
          position={[0, 0.3, 0]}
          fontSize={0.13}
          color="#fff"
          anchorX="center"
          anchorY="bottom"
          font="/fonts/Inter_24pt-Regular.ttf"
          outlineColor="#000"
          outlineWidth={0.008}
        >
          {`ACOUSTIC_02\nRESONANCE`}
        </Text>
      )}
    </group>
  );
}

// Block3 is a copy of Block1
function Block3(props) {
  const { nodes, materials } = useGLTF('/models/cube_high_comp.glb');
  return (
    <group {...props} dispose={null}>
      {Object.values(nodes).map((node, i) =>
        node.type === 'Mesh' ? (
          <mesh
            key={i}
            geometry={node.geometry}
            castShadow
            receiveShadow
          >
            <MeshTransmissionMaterial
              backside
              samples={4}
              thickness={3}
              chromaticAberration={0}
              anisotropy={0.1}
              distortion={0}
              distortionScale={0}
              temporalDistortion={0}
              iridescence={1}
              iridescenceIOR={1}
              iridescenceThicknessRange={[0, 1400]}
            />
          </mesh>
        ) : null
      )}
      {props.showLabel && (
        <Text
          position={[0, 0.3, 0]}
          fontSize={0.13}
          color="#fff"
          anchorX="center"
          anchorY="bottom"
          font="/fonts/Inter_24pt-Regular.ttf"
          outlineColor="#000"
          outlineWidth={0.008}
        >
          {`SUSTAIN_03\nRECYCLED`}
        </Text>
      )}
    </group>
  );
}

// Block4 is a copy of Block1
function Block4(props) {
  const { nodes, materials } = useGLTF('/models/cube_high_comp.glb');
  return (
    <group {...props} dispose={null}>
      {Object.values(nodes).map((node, i) =>
        node.type === 'Mesh' ? (
          <mesh
            key={i}
            geometry={node.geometry}
            castShadow
            receiveShadow
          >
            <MeshTransmissionMaterial
              backside
              samples={4}
              thickness={3}
              chromaticAberration={0}
              anisotropy={0.1}
              distortion={0}
              distortionScale={0}
              temporalDistortion={0}
              iridescence={1}
              iridescenceIOR={1}
              iridescenceThicknessRange={[0, 1400]}
            />
          </mesh>
        ) : null
      )}
      {props.showLabel && (
        <Text
          position={[0, 0.3, 0]}
          fontSize={0.13}
          color="#fff"
          anchorX="center"
          anchorY="bottom"
          font="/fonts/Inter_24pt-Regular.ttf"
          outlineColor="#000"
          outlineWidth={0.008}
        >
          {`CUSTOM_04\nOPTIONS`}
        </Text>
      )}
    </group>
  );
}

useGLTF.preload('/models/extruded-try-soft-cube-round-whole.glb');

const CAMERA_POSITIONS = [
  { name: 'Front', position: [1.27, 0.81, -0.18], lookAt: [0, 0.50, 0] },
];

// Define initial rotations for each block (in radians)
const INITIAL_ROTATIONS = [
  [Math.PI, 0, 0],                // Block1: upside down
  [Math.PI / 2, Math.PI, 0],      // Block2: 90° X, 180° Y
  [Math.PI, Math.PI / 2, Math.PI / 2], // Block3: upside down, 90° Y, 90° Z
  [Math.PI * 1.5, Math.PI, Math.PI],   // Block4: 270° X, 180° Y, 180° Z
];
const FINAL_ROTATION = [0, 2, 0]; // Standard front view rotation
const END_ROTATIONS = [
  [0, 4, 0],                // Block1: rotate further on Y
  [0, 2, Math.PI],          // Block2: flip on Z
  [Math.PI / 2, 2, 0],      // Block3: 90° X, standard Y
  [0, 2, Math.PI / 2],      // Block4: 90° Z, standard Y
];

// Helper to lerp between two arrays
function lerpArray(a, b, t) {
  return a.map((v, i) => v + (b[i] - v) * t);
}

// Quartic ease-out function (even faster)
function easeOutQuart(t) {
  return 1 - Math.pow(1 - t, 5);
}

function BlackBackground() {
  const { scene } = useThree();
  React.useEffect(() => {
    scene.background = new THREE.Color('#000000');
    return () => {
      scene.background = null;
    };
  }, [scene]);
  return null;
}

function SubtleNetwork({ count = 60, radius = 0.44, blockMesh }) {
  const group = useRef();
  const pointsRef = useRef();
  const linesRef = useRef();

  // Initial positions and velocities
  const { positions, velocities } = useMemo(() => {
    const positions = [];
    const velocities = [];
    for (let i = 0; i < count; i++) {
      const pos = new THREE.Vector3(
        (Math.random() - 0.5) * radius * 2,
        (Math.random() - 0.5) * radius * 2,
        (Math.random() - 0.5) * radius * 2
      );
      positions.push(pos);
      velocities.push(
        new THREE.Vector3(
          (Math.random() - 0.5) * 0.003,
          (Math.random() - 0.5) * 0.003,
          (Math.random() - 0.5) * 0.003
        )
      );
    }
    return { positions, velocities };
  }, [count, radius]);

  // Buffer attributes for points and lines
  const pointsPositions = useMemo(() => new Float32Array(count * 3), [count]);
  const maxConnections = 3;
  const minDistance = radius * 0.6;
  const maxSegments = count * maxConnections;
  const linesPositions = useMemo(() => new Float32Array(maxSegments * 2 * 3), [maxSegments]);

  useFrame(() => {
    // Animate points
    for (let i = 0; i < count; i++) {
      positions[i].add(velocities[i]);
      // Bounce inside a sphere
      if (positions[i].length() > radius) {
        positions[i].normalize().multiplyScalar(radius);
        velocities[i].reflect(positions[i].clone().normalize());
        velocities[i].multiplyScalar(0.8);
      }
      pointsPositions[i * 3] = positions[i].x;
      pointsPositions[i * 3 + 1] = positions[i].y;
      pointsPositions[i * 3 + 2] = positions[i].z;
    }
    pointsRef.current.geometry.attributes.position.needsUpdate = true;

    // Build lines between close points, but clip against blockMesh
    let segmentIdx = 0;
    if (blockMesh && blockMesh.geometry) {
      // Ensure BVH is built
      if (!blockMesh.geometry.boundsTree) {
        MeshBVH.assignBVH(blockMesh.geometry);
      }
    }
    for (let i = 0; i < count; i++) {
      let connections = 0;
      for (let j = i + 1; j < count; j++) {
        if (connections >= maxConnections) break;
        const dist = positions[i].distanceTo(positions[j]);
        if (dist < minDistance) {
          let visible = true;
          if (blockMesh && blockMesh.geometry && blockMesh.geometry.boundsTree) {
            // Ray from i to j
            const ray = new THREE.Ray(positions[i], positions[j].clone().sub(positions[i]).normalize());
            const distance = positions[i].distanceTo(positions[j]);
            const intersects = raycastBVH(blockMesh, ray, distance);
            if (intersects) visible = false;
          }
          if (visible) {
            linesPositions[segmentIdx * 6 + 0] = positions[i].x;
            linesPositions[segmentIdx * 6 + 1] = positions[i].y;
            linesPositions[segmentIdx * 6 + 2] = positions[i].z;
            linesPositions[segmentIdx * 6 + 3] = positions[j].x;
            linesPositions[segmentIdx * 6 + 4] = positions[j].y;
            linesPositions[segmentIdx * 6 + 5] = positions[j].z;
            segmentIdx++;
            connections++;
          }
        }
      }
    }
    linesRef.current.geometry.setDrawRange(0, segmentIdx * 2);
    linesRef.current.geometry.attributes.position.needsUpdate = true;
  });

  // Helper for BVH raycast
  function raycastBVH(mesh, ray, maxDist) {
    const intersects = [];
    mesh.raycast({ ray, near: 0, far: maxDist }, intersects);
    return intersects.length > 0;
  }

  return (
    <group ref={group} position={[0, 0.6, 0]}>
      {/* Dots */}
      <points ref={pointsRef}>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={count}
            array={pointsPositions}
            itemSize={3}
          />
        </bufferGeometry>
        <pointsMaterial color="#fff" size={0.015} sizeAttenuation opacity={0.5} transparent />
      </points>
      {/* Lines */}
      <lineSegments ref={linesRef}>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={maxSegments * 2}
            array={linesPositions}
            itemSize={3}
          />
        </bufferGeometry>
        <lineBasicMaterial color="#fff" opacity={0.18} transparent />
      </lineSegments>
    </group>
  );
}

export default function ExplainSection() {
  const [cubeY, setCubeY] = useState(0);

  // --- Ripple state ---
  const [ripples, setRipples] = useState([]); // {x, z, startTime}
  const prevBlockTopYs = useRef([null, null, null, null]);
  const prevBlockAbove = useRef([false, false, false, false]);
  const prevBlockBottomYs = useRef([null, null, null, null]);

  // Leva controls for interactive camera tuning and block rotations
  const [{ 
    startRot1, centerRot1, endRot1,
    startRot2, centerRot2, endRot2,
    startRot3, centerRot3, endRot3,
    startRot4, centerRot4, endRot4,
  }, set] = useControls(() => ({
    startRot1: { value: [-0.38, 0.10,-2.22], label: 'Block1 Start' },
    centerRot1: { value: [0, 1.50, 0.15], label: 'Block1 Center' },
    endRot1: { value: [0, 0, 0], label: 'Block1 End' },
    startRot2: { value: [2.34, -0.56, 2.06], label: 'Block2 Start' },
    centerRot2: { value: [-0.13, 2.28, 0.10], label: 'Block2 Center' },
    endRot2: { value: [0, 0, 0], label: 'Block2 End' },
    startRot3: { value: [0.49, 8, 8], label: 'Block3 Start' },
    centerRot3: { value: [0.02, 1.29, -0.06], label: 'Block3 Center' },
    endRot3: { value: [0, 0, 0], label: 'Block3 End' },
    startRot4: { value: [0, 0, 0], label: 'Block4 Start' },
    centerRot4: { value: [0.05, 3.13, 0.05], label: 'Block4 Center' },
    endRot4: { value: [0, 0, 0], label: 'Block4 End' },
  }), []);

  // Helper arrays for easier access
  const START_ROTATIONS = [startRot1, startRot2, startRot3, startRot4];
  const CENTER_ROTATIONS = [centerRot1, centerRot2, centerRot3, centerRot4];
  const END_ROTATIONS = [endRot1, endRot2, endRot3, endRot4];

  // State to track Block1 progress for content visibility
  const [block1Progress, setBlock1Progress] = useState(0);

  // State to track which block is most in view
  const [mainBlockIdx, setMainBlockIdx] = useState(0);

  // Scroll to move the cube up and down
  useEffect(() => {
    const handleWheel = (e) => {
      setCubeY((prev) => {
        let next = prev + e.deltaY * 0.002; // Invert scroll direction, slower movement
        next = Math.max(0, Math.min(7.5, next)); // Clamp so 0 is the minimum (can't scroll cubes further down)
        return next;
      });
    };
    window.addEventListener('wheel', handleWheel, { passive: false });
    return () => window.removeEventListener('wheel', handleWheel);
  }, []);

  // Calculate Block1 progress for content overlay
  useEffect(() => {
    // Block1 is idx 0
    const idx = 0;
    const y = cubeY - idx * 2.0;
    const startY = -1;
    const endY = 7.5 - idx * 2.0;
    const progress = Math.max(0, Math.min(1, (y - startY) / (endY - startY)));
    setBlock1Progress(progress);

    // Determine which block is most in view (closest to y=0)
    let closestIdx = 0;
    let closestDist = Math.abs(cubeY - 0);
    for (let i = 1; i < 4; i++) {
      const blockY = cubeY - i * 2.0;
      const dist = Math.abs(blockY - 0);
      if (dist < closestDist) {
        closestDist = dist;
        closestIdx = i;
      }
    }
    setMainBlockIdx(closestIdx);
  }, [cubeY]);

  // Block1 ref for dynamic label
  const block1Ref = useRef();

  // Keep a ref to the main block mesh and pass it to SubtleNetwork
  const blockRefs = [useRef(), useRef(), useRef(), useRef()];

  // Detect block crossing water surface (y=0) with top of block, both ways
  useEffect(() => {
    const now = performance.now();
    [0, 1, 2, 3].forEach((idx) => {
      const y = cubeY - idx * 2.0;
      const blockTopY = y + 0.15; // block scaleY=0.30, so half-height is 0.15
      const prevTop = prevBlockTopYs.current[idx];
      if (prevTop !== null) {
        // Crossed upwards (entering water)
        if (prevTop < 0 && blockTopY >= 0) {
          setRipples((r) => {
            const newRipples = [
              ...r,
              { x: 0, z: 0, startTime: now, blockIdx: idx },
            ];
            return newRipples.slice(-5);
          });
        }
        // Crossed downwards (leaving water)
        if (prevTop >= 0 && blockTopY < 0) {
          setRipples((r) => {
            const newRipples = [
              ...r,
              { x: 0, z: 0, startTime: now, blockIdx: idx },
            ];
            return newRipples.slice(-5);
          });
        }
      }
      prevBlockTopYs.current[idx] = blockTopY;
    });
  }, [cubeY]);

  // Set camera to always use the 'Front' position
  const camPos = CAMERA_POSITIONS[0].position;
  const camLookAt = CAMERA_POSITIONS[0].lookAt;

  return (
    <div style={{ position: 'relative', width: '100vw', height: '100vh', overflow: 'hidden' }}>
      <Canvas
        camera={{ position: camPos, fov: 45 }}
        gl={{ alpha: true, antialias: true, powerPreference: 'high-performance', clearColor: [0, 0, 0, 1] }}
        style={{ position: 'absolute', inset: 0, width: '100vw', height: '100vh', display: 'block', zIndex: 0 }}
      >
        <Suspense fallback={null}>
          <BlackBackground />
          {/* Pass ripples to WaterMatcapBackground */}
          <WaterMatcapBackground position={[0, 0, 0]} ripples={ripples} />
          {/* Subtle animated network effect in the center, clipped by main block in view */}
          {/* <SubtleNetwork count={60} radius={0.44} blockMesh={blockRefs[mainBlockIdx].current} /> */}
          {/* Only render blocks (no cameraIndex check needed) */}
          {(() => {
            // Each block is 2 units apart
            const blocks = [0, 1, 2, 3];
            return blocks.map((idx) => {
              const y = cubeY - idx * 2.0;
              // Progress: 0 when y == -1 (start), 1 when y == (final y, e.g. 7.5 - idx*2.0)
              // We'll use y from -1 to (finalY), but clamp progress between 0 and 1
              const startY = -1;
              const endY = 7.5 - idx * 2.0;
              const progress = Math.max(0, Math.min(1, (y - startY) / (endY - startY)));
              let rotation;
              if (progress < 0.5) {
                // 0 to 0.5: start to center, eased
                rotation = lerpArray(START_ROTATIONS[idx], CENTER_ROTATIONS[idx], easeOutQuart(progress * 2));
              } else {
                // 0.5 to 1: center to end
                rotation = lerpArray(CENTER_ROTATIONS[idx], END_ROTATIONS[idx], (progress - 0.5) * 2);
              }
              const Block = [Block1, Block2, Block3, Block4][idx];
              // Attach a ref to each block
              const refProp = { ref: blockRefs[idx] };
              const showLabel = mainBlockIdx === idx && progress > 0;
              return (
                <React.Fragment key={idx}>
                  <Block
                    position={[0, y, 0]}
                    scale={[0.20, 0.30, 0.20]}
                    rotation={rotation}
                    {...refProp}
                    showLabel={showLabel}
                  />
                </React.Fragment>
              );
            });
          })()}
          <ExplainOrbitCamera position={camPos} lookAt={camLookAt} />
        </Suspense>
      </Canvas>
      {/* Content for ExplainSection will go here */}
      <div style={{ position: 'relative', zIndex: 1 }}>
        {/* Block1 Content Overlay: Only visible when Block1 is the main block in view */}
        {mainBlockIdx === 0 && block1Progress > 0 && (
          <>
            <div
              style={{ position: 'fixed', top: '50%', left: '5vw', transform: 'translateY(-50%)', color: '#fff', maxWidth: '500px', zIndex: 10, pointerEvents: 'none', textAlign: 'left' }}
            >
              <div style={{ fontSize: '3.5rem', fontWeight: 600, letterSpacing: '-0.04em', lineHeight: 1.05 }}>
                ALUMINIUM<br />UNIBODY
              </div>
              <div style={{ fontSize: '1.2rem', fontWeight: 400, margin: '1.5rem 0 0.5rem 0', letterSpacing: '0.04em' }}>
                PRECISION-CRAFTED STRUCTURE
              </div>
              <div style={{ fontSize: '1rem', fontWeight: 300, marginTop: '0.5rem', color: '#ccc', lineHeight: 1.4 }}>
                Aluminium is the ideal material for louder speaker cabinets thanks to its outstanding acoustic quality and elegant look.
              </div>
            </div>
          </>
        )}
        {/* Block2 Content Overlay */}
        {mainBlockIdx === 1 && (
          <>
            <div
              style={{ position: 'fixed', top: '50%', left: '5vw', transform: 'translateY(-50%)', color: '#fff', maxWidth: '500px', zIndex: 10, pointerEvents: 'none', textAlign: 'left' }}
            >
              <div style={{ fontSize: '3.5rem', fontWeight: 600, letterSpacing: '-0.04em', lineHeight: 1.05 }}>
                ADVANCED<br />ACOUSTICS
              </div>
              <div style={{ fontSize: '1.2rem', fontWeight: 400, margin: '1.5rem 0 0.5rem 0', letterSpacing: '0.04em' }}>
                ENGINEERED FOR SOUND
              </div>
              <div style={{ fontSize: '1rem', fontWeight: 300, marginTop: '0.5rem', color: '#ccc', lineHeight: 1.4 }}>
                Our unique design ensures optimal resonance and clarity, delivering an immersive audio experience for every listener.
              </div>
            </div>
          </>
        )}
        {/* Block3 Content Overlay */}
        {mainBlockIdx === 2 && (
          <>
            <div
              style={{ position: 'fixed', top: '50%', left: '5vw', transform: 'translateY(-50%)', color: '#fff', maxWidth: '500px', zIndex: 10, pointerEvents: 'none', textAlign: 'left' }}
            >
              <div style={{ fontSize: '3.5rem', fontWeight: 600, letterSpacing: '-0.04em', lineHeight: 1.05 }}>
                SUSTAINABLE<br />MATERIALS
              </div>
              <div style={{ fontSize: '1.2rem', fontWeight: 400, margin: '1.5rem 0 0.5rem 0', letterSpacing: '0.04em' }}>
                ECO-FRIENDLY DESIGN
              </div>
              <div style={{ fontSize: '1rem', fontWeight: 300, marginTop: '0.5rem', color: '#ccc', lineHeight: 1.4 }}>
                Built with sustainability in mind, our products use recyclable materials and energy-efficient processes.
              </div>
            </div>
          </>
        )}
        {/* Block4 Content Overlay */}
        {mainBlockIdx === 3 && (
          <>
            <div
              style={{ position: 'fixed', top: '50%', left: '5vw', transform: 'translateY(-50%)', color: '#fff', maxWidth: '500px', zIndex: 10, pointerEvents: 'none', textAlign: 'left' }}
            >
              <div style={{ fontSize: '3.5rem', fontWeight: 600, letterSpacing: '-0.04em', lineHeight: 1.05 }}>
                CUSTOMIZATION<br />OPTIONS
              </div>
              <div style={{ fontSize: '1.2rem', fontWeight: 400, margin: '1.5rem 0 0.5rem 0', letterSpacing: '0.04em' }}>
                TAILORED TO YOU
              </div>
              <div style={{ fontSize: '1rem', fontWeight: 300, marginTop: '0.5rem', color: '#ccc', lineHeight: 1.4 }}>
                Choose from a range of finishes and features to create a product that fits your unique style and needs.
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
} 