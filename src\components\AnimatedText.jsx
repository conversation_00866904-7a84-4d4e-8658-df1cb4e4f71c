import React, { useRef, useEffect, useState } from 'react';
import { gsap } from 'gsap';
import SplitText from 'gsap/SplitText';
import { useInView } from 'react-intersection-observer';
import './AnimatedText.css';

// Register GSAP plugins
gsap.registerPlugin(SplitText);

const AnimatedText = ({
  children,
  effect = 'randomLetters', // Now supports 'randomLetters', 'revealLines', 'fadeLines'
  threshold = 0.5,
  duration = 3,
  staggerAmount = 0.3,
  className = '',
  elementType = 'h2',
  outDuration = 0.7,
  outStaggerAmount = 0.4,
  style = {}
}) => {
  const textRef = useRef(null);
  const splitTextRef = useRef(null);
  const [animationStarted, setAnimationStarted] = useState(false);
  const previousInViewRef = useRef(false);
  
  // Use react-intersection-observer
  const { ref: viewRef, inView } = useInView({
    threshold: threshold,
    triggerOnce: false
  });
  
  // Set up the animation effect when visibility changes
  useEffect(() => {
    if (!textRef.current) return;
    
    // Initialize split text
    const initSplitText = () => {
      // Clean up previous split if it exists
      if (splitTextRef.current) {
        splitTextRef.current.revert();
      }
      
      // Create new split text - different splitting technique based on effect
      try {
        if (effect === 'revealLines' || effect === 'fadeLines') {
          splitTextRef.current = new SplitText(textRef.current, {
            type: 'lines',
            linesClass: 'split-line'
          });
          
          // Set initial state for all lines
          if (effect === 'fadeLines') {
            // For fadeLines, only set opacity without moving
            gsap.set(splitTextRef.current.lines, { 
              autoAlpha: 0.2
            });
          } else if (effect === 'revealLines') {
            // For revealLines, set both opacity and position
            gsap.set(splitTextRef.current.lines, { 
              autoAlpha: 0.2,
              yPercent: 70
            });
          }
        } else {
          // Default randomLetters effect
          splitTextRef.current = new SplitText(textRef.current, {
            type: 'words,chars',
            wordsClass: 'word',
            charsClass: 'char'
          });
          
          // Set initial state for all characters
          gsap.set(splitTextRef.current.chars, { 
            autoAlpha: 0,
            display: 'inline-block'
          });
        }
        
        return true;
      } catch (error) {
        console.error('Error initializing SplitText:', error);
        return false;
      }
    };
    
    // Initialize on first render
    if (!animationStarted) {
      const initialized = initSplitText();
      if (initialized) {
        setAnimationStarted(true);
      }
    }
    
    // Apply animations based on visibility and effect type
    if (inView && splitTextRef.current) {
      // Animation when element becomes visible
      if (effect === 'fadeLines') {
        // Only animate opacity for fadeLines effect
        gsap.killTweensOf(splitTextRef.current.lines);
        gsap.to(splitTextRef.current.lines, {
          autoAlpha: 1,
          duration: 2,
          stagger: 0.2,
          ease: 'power1.out'
        });
      } else if (effect === 'revealLines') {
        // Full revealLines animation with position and opacity
        gsap.killTweensOf(splitTextRef.current.lines);
        
        // Create a timeline for sequential animations
        const tl = gsap.timeline();
        
        // First animation - opacity
        tl.to(splitTextRef.current.lines, {
          autoAlpha: 1,
          duration: 2,
          stagger: 0.2
        });
        
        // Second animation - position
        tl.to(splitTextRef.current.lines, {
          yPercent: 0,
          duration: 1,
          stagger: 0.1,
          ease: 'power1.out'
        }, "<0.5"); // Start slightly before first animation completes
        
      } else {
        // Default randomLetters effect
        gsap.killTweensOf(splitTextRef.current.chars);
        gsap.to(splitTextRef.current.chars, {
          autoAlpha: 1,
          duration: duration,
          stagger: {
            from: effect === 'randomLetters' ? 'random' : 'start',
            amount: staggerAmount,
            ease: 'power1.out'
          }
        });
      }
      
      previousInViewRef.current = true;
      
    } else if (!inView && splitTextRef.current && previousInViewRef.current) {
      // Out animation when element is scrolled out of view
      if (effect === 'fadeLines') {
        gsap.killTweensOf(splitTextRef.current.lines);
        gsap.to(splitTextRef.current.lines, {
          autoAlpha: 0.2,
          duration: outDuration,
          stagger: {
            amount: outStaggerAmount,
            ease: 'power2.out'
          }
        });
      } else if (effect === 'revealLines') {
        gsap.killTweensOf(splitTextRef.current.lines);
        gsap.to(splitTextRef.current.lines, {
          autoAlpha: 0.2,
          yPercent: 70,
          duration: outDuration,
          stagger: {
            amount: outStaggerAmount,
            ease: 'power2.out'
          }
        });
      } else {
        gsap.killTweensOf(splitTextRef.current.chars);
        gsap.to(splitTextRef.current.chars, {
          autoAlpha: 0,
          duration: outDuration,
          stagger: {
            from: effect === 'randomLetters' ? 'random' : 'start',
            amount: outStaggerAmount,
            ease: 'power2.out'
          }
        });
      }
      
      previousInViewRef.current = false;
    }
    
    // Cleanup function
    return () => {
      if (splitTextRef.current) {
        if (effect === 'revealLines' || effect === 'fadeLines') {
          gsap.killTweensOf(splitTextRef.current.lines);
        } else {
          gsap.killTweensOf(splitTextRef.current.chars);
        }
      }
    };
  }, [inView, effect, duration, staggerAmount, animationStarted, outDuration, outStaggerAmount]);
  
  // Clean up split text on unmount
  useEffect(() => {
    return () => {
      if (splitTextRef.current) {
        splitTextRef.current.revert();
      }
    };
  }, []);
  
  // Add CSS class based on effect type
  const effectClass = `${effect}-text`;
  
  // Create the element dynamically based on elementType prop
  const Element = elementType;
  
  return (
    <div ref={viewRef} className={`animated-text-container ${className}`}>
      <Element 
        ref={textRef} 
        className={`animated-text ${effectClass}`}
        style={style}
      >
        {children}
      </Element>
    </div>
  );
};

export default AnimatedText;