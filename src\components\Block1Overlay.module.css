@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter_24pt-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter_24pt-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter_24pt-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter_24pt-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

.overlay, .contentWrapper, .infoHeading, .infoRow, .infoBlock, .infoLabel, .infoValue, .statsGrid, .statCard, .statNumber, .statUnit, .statDesc, .statIcon, .divider, .bigTitle, .bigDescription, .cardRow, .card, .cardTitle, .cardText, .newsGrid, .newsCard, .newsTag, .newsTitle, .newsDate, .newsButton, .newsButtonPrimary, .newsTitleCenter {
  font-family: 'Inter', sans-serif;
}

.bigTitle, .statNumber, .cardTitle, .newsTitleCenter {
  font-weight: 700;
}

.infoHeading, .newsButton, .newsButtonPrimary {
  font-weight: 600;
}

.infoLabel, .statDesc, .newsTag {
  font-weight: 500;
}

.infoValue, .statUnit, .bigDescription, .newsDate, .newsTitle, .cardText, .newsCard {
  font-weight: 400;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(20,20,20,0.75);
  z-index: 1000;
  overflow-y: auto;
  display: block;
  scrollbar-width: none; /* Firefox */
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}
.overlay::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

.closeButton {
  position: fixed;
  top: 24px;
  right: 24px;
  z-index: 2000;
  background: none;
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  margin: 0;
  border-radius: 0 0 0 8px;
  box-shadow: none;
}

.content {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.contentWrapper {
  max-width: 1000px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  overflow-x: hidden;
  padding-bottom: 40px;
  padding-top: 20vh;
  width: 100%;
  scrollbar-width: none; /* Firefox */
  position: relative;
}
.contentWrapper::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

.title {
  font-size: 48px;
  font-family: Inter;
  font-weight: 600;
  margin-bottom: 24px;
  color: #fff;
  letter-spacing: 1px;
}

.description {
  font-size: 20px;
  font-family: Inter;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.75);
  margin-bottom: 100px;
  line-height: 1.5;
  max-width: 1000px;
}

.infoRow {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  width: 1000px;
  max-width: 1000px;
  margin-top: 36px;
  margin-left: auto;
  margin-right: auto;
  align-items: end;
  text-align: left;
}

.infoBlock {
  min-width: 0;
  text-align: left;
}

.infoLabel {
  font-size: 11px;
  font-family: Supply;
  color: #ffffff;
  letter-spacing: 1.5px;
  margin-bottom: 6px;
  font-weight: 400;
}

.infoValue {
  font-size: 15px;
  color: #fff;
  font-weight: 400;
  line-height: 1.4;
  max-width: 350px;
  word-break: break-word;
}

.launchSiteWrapper {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.launchSiteButton {
  display: flex;
  align-items: center;
  background: none;
  border: 1px solid #444;
  color: #fff;
  font-size: 13px;
  letter-spacing: 1.5px;
  padding: 10px 32px 10px 18px;
  border-radius: 2px;
  text-decoration: none;
  transition: border 0.2s, color 0.2s;
  position: relative;
}
.launchSiteButton:hover {
  border: 1px solid #fff;
  color: #fff;
}

.arrow {
  margin-left: 16px;
  font-size: 18px;
  transition: transform 0.2s;
}
.launchSiteButton:hover .arrow {
  transform: translateX(6px);
}

.infoHeading {
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  margin-bottom: 18px;
  margin-left: 0;
  text-align: left;
  letter-spacing: 1px;
}

.infoHeadingExample {
  font-size: 26px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 18px;
  margin-left: 0;
  text-align: left;
  letter-spacing: 1px;
}

.cardRow {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  width: 100%;
  max-width: 1000px;
  margin: 56px 0 0 0;
}

.card {
  background: rgba(40, 40, 40, 0.95);
  border-radius: 12px;
  padding: 28px 22px 24px 22px;
  box-shadow: 0 2px 16px 0 rgba(0,0,0,0.10);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
}

.cardTitle {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #fff;
}

.cardText {
  font-size: 15px;
  color: #d0d0d0;
  line-height: 1.5;
}

.bigTitle {
  font-size: 48px;
  font-weight: 600;
  color: #fff;
  margin-top: 0px;
  margin-bottom: 24px;
  letter-spacing: 1px;
  text-align: left;
}

.bigDescription {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.75);
  font-weight: 400;
  line-height: 1.5;
  margin-bottom: 0;
  max-width: 1000px;
  text-align: left;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(3, 300px);
  grid-template-rows: repeat(3, 300px);
  gap: 20px;
  width: 940px;
  max-width: 1000px;
  margin: 150px 0 0 0;
  border: none;
  background: none;
  justify-content: center;
}

.statCard {
  position: relative;
  background: rgba(255,255,255,0.10);
  min-height: 300px;
  min-width: 300px;
  max-width: 300px;
  max-height: 300px;
  width: 300px;
  height: 300px;
  border: 1px solid #222;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 1rem;
  transition: background 0.2s;
  overflow: hidden;
  aspect-ratio: 1 / 1;
  border-radius: 12px;
}

.statCard:not(:empty):hover {
  background: rgba(255,255,255,0.16);
}

.statLabel {
  font-family: 'Supply', sans-serif;
  font-weight: 300;
  font-size: 0.50rem;
  color: rgba(255, 255, 255, 0.75);
  letter-spacing: 1.5px;
  margin-bottom: 0;
  margin-top: 0;
  width: 100%;
  text-align: left;
  line-height: 1.2;
}

.statLabel:last-child {
  margin-bottom: 0;
}

.statLabelColumn {
  display: flex;
  flex-direction: row;
  position: absolute;
  top: 16px;
  left: 16px;
  right: 0px;
  width: calc(100% - 32px);
  gap: 12px;
  align-items: flex-start;
}

.statNumber {
  font-size: 64px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8px;
  letter-spacing: 1px;
}

.statUnit {
  font-size: 64px;
  font-weight: 300;
  color: #ffffff;
  margin-left: 2px;
}

.statDesc {
  font-size: 14px;
  color: #ffffff;
  font-weight: 300;
  margin-bottom: 0.5rem;
}

.statIcon {
  display: none;
}

.divider {
  width: 1000px;
  max-width: 1000px;
  height: 0;
  border-bottom: 1px solid #222;
  margin: 150px 0;
}

.newsGrid {
  display: grid;
  grid-template-columns: repeat(3, 300px);
  grid-template-rows: repeat(2, 300px);
  gap: 20px;
  width: 940px;
  max-width: 1000px;
  margin: 150px 0 150px 0;
  border: none;
  background: none;
  justify-content: center;
}

.newsCard {
  background: rgba(255,255,255,0.10);
  min-width: 300px;
  min-height: 300px;
  max-width: 300px;
  max-height: 300px;
  width: 300px;
  height: 300px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 1rem;
  position: relative;
  border-radius: 12px;
}

.newsTag {
  display: inline-block;
  background: #ffffff;
  color: #000000;
  font-size: 12px;
  font-weight: 400;
  border-radius: 6px;
  padding: 4px 12px;
  margin-bottom: 20px;
  position: static;
  top: auto;
  left: auto;
}

.newsTitle {
  font-size: 20px;
  color: #fff;
  font-weight: 400;
  margin-bottom: 24px;
  line-height: 1.3;
  position: static;
  top: auto;
  left: auto;
  right: auto;
}

.newsDate {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.75);
  margin-bottom: 12px;
}

.newsButton {
  background: #ffffff;
  color: #000000;
  font-size: 14px;
  font-weight: 600;
  border: none;
  border-radius: 6px;
  padding: 14px 0;
  width: 100%;
  cursor: pointer;
  margin-top: 8px;
  transition: background 0.2s, color 0.2s;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.newsButton:hover {
  background: #222;
  color: #fff;
}

.newsButtonPrimary {
  background: #fff;
  color: #111;
  font-size: 15px;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  padding: 14px 0;
  width: 120px;
  margin: 0 auto;
  display: block;
  cursor: pointer;
  margin-top: 32px;
  transition: background 0.2s, color 0.2s;
}
.newsButtonPrimary:hover {
  background: #eaeaea;
  color: #111;
}

.newsTitleCenter {
  font-size: 22px;
  color: #fff;
  font-weight: 400;
  text-align: center;
  margin-top: 40px;
}

.newsTop {
  margin-bottom: auto;
}

.arrowRight {
  position: fixed;
  top: 50%;
  right: 100px;
  transform: translateY(-50%);
  z-index: 2000;
}

.arrowLeft {
  position: fixed;
  top: 50%;
  left: 100px;
  transform: translateY(-50%);
  z-index: 2000;
}

.arrowLeftImg {
  transform: rotate(180deg);
}

@keyframes arrowWiggle {
  0% { transform: translateY(0); }
  15% { transform: translateY(-4px); }
  30% { transform: translateY(3px); }
  45% { transform: translateY(-2px); }
  60% { transform: translateY(2px); }
  75% { transform: translateY(-1px); }
  100% { transform: translateY(0); }
}

@keyframes arrowWiggleLeft {
  0% { transform: rotate(180deg) translateY(0); }
  15% { transform: rotate(180deg) translateY(-4px); }
  30% { transform: rotate(180deg) translateY(3px); }
  45% { transform: rotate(180deg) translateY(-2px); }
  60% { transform: rotate(180deg) translateY(2px); }
  75% { transform: rotate(180deg) translateY(-1px); }
  100% { transform: rotate(180deg) translateY(0); }
}

.arrowRight img,
.arrowLeftImg {
  transition: filter 0.2s cubic-bezier(0.4,0,0.2,1);
  cursor: pointer;
}
.arrowRight img:hover {
  animation: arrowWiggle 0.9s cubic-bezier(0.4,0,0.2,1) infinite;
}
.arrowLeftImg:hover {
  animation: arrowWiggleLeft 0.9s cubic-bezier(0.4,0,0.2,1) infinite;
  transform: rotate(180deg);
}

/* Expanding Flex Cards Styles */
.flexCardsRow {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: center;
  min-width: 600px;
  max-width: 900px;
  width: calc(100% - 100px);
  height: 400px;
  margin: 48px auto 150px auto;
  gap: 0;
}
.flexCard {
  position: relative;
  overflow: hidden;
  min-width: 60px;
  margin: 10px;
  background: rgba(255,255,255,0.10);
  background-size: auto 120%;
  background-position: center;
  cursor: pointer;
  display: flex;
  align-items: flex-end;
  transition: flex-grow 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95),
    max-width 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95),
    margin 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95),
    border-radius 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95),
    background-size 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95);
  height: 100%;
  border-radius: 12px;
  box-sizing: border-box;
}
.flexCard.active {
  flex-grow: 0 !important;
  width: 600px;
  min-width: 600px;
  max-width: 600px;
  height: 400px;
  min-height: 400px;
  max-height: 400px;
  margin: 10px;
  border-radius: 12px;
  background-size: auto 100%;
  z-index: 2;
}
.flexCard.inactive {
  flex-grow: 1;
  border-radius: 12px;
  z-index: 1;
}

.flexCardLabel {
  display: flex;
  position: absolute;
  left: 10px;
  bottom: 10px;
  right: 0px;
  height: 40px;
  align-items: center;
  transition: left 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95), bottom 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95);
}
.flexCard.active .flexCardLabel {
  left: 20px;
  bottom: 20px;
}
.flexCardInfo {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 10px;
  color: #fff;
  white-space: pre;
}
.flexCardMain {
  font-weight: bold;
  font-size: 1.2rem;
  position: relative;
  left: 20px;
  opacity: 0;
  transition: left 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95), opacity 0.5s ease-out;
}
.flexCardSub {
  font-size: 1rem;
  position: relative;
  left: 20px;
  opacity: 0;
  transition: left 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95), opacity 0.5s ease-out;
  transition-delay: .1s;
}
.flexCard.active .flexCardMain,
.flexCard.active .flexCardSub {
  left: 0px;
  opacity: 1;
}
.flexCardSVG {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 0;
  pointer-events: none;
  user-select: none;
}
.flexCardIconWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.flexCardIcon {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 0;
  display: block;
}