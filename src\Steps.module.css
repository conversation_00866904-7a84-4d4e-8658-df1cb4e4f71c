/* Import Satoshi font */
@import url('https://api.fontshare.com/v2/css?f[]=satoshi@900,700,500,301,300,400&display=swap');

/* Import local Telegraf fonts */
@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Telegraf';
  src: url('/fonts/telegraf-semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Archivo';
  src: url('/fonts/archivo-black-regular.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Lenis smooth scrolling styles */
html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}

canvas {
  touch-action: none;
}

/* Scroll Overlay Styles */
.scrollOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  z-index: 10;
  pointer-events: auto;
  /* Hide scrollbar but keep functionality */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollOverlay::-webkit-scrollbar {
  display: none;
}

.contentSection {
  position: relative;
  min-height: 375vh; /* Increased to accommodate larger spacing */
  width: 100%;
}

/* Base paragraph styling */
.paragraph {
  position: absolute;
  width: 40%;
  max-width: 750px;
}

/* Individual paragraph positioning - vertical spacing and horizontal positioning */
.paragraph1 {
  top: 110vh; /* Slightly below the first 100vh */
  left: 10%; /* Custom left positioning */
}

.paragraph2 {
  top: calc(110vh + 50vh); /* 50vh below the first paragraph */
  right: 5%; /* Custom right positioning */
}

.paragraph3 {
  top: calc(110vh + 50vh + 40vh); /* 40vh below the second paragraph */
  left: 20%; /* Custom left positioning - different from paragraph-1 */
}

.paragraph4 {
  top: calc(110vh + 50vh + 40vh + 50vh); /* Same height as paragraph-3 */
  right: 20%; /* Custom right positioning - different from paragraph-2 */
}

.paragraphContainer {
  padding: 2rem;
  border-radius: 8px;
  color: white;
}

.paragraphContainer h2 {
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  font-size: 3rem;
  margin-bottom: 1rem;
}

.paragraphContainer p {
  font-family: 'Telegraf', sans-serif;
  font-weight: 400;
  font-size: 1.1rem;
  line-height: 1.5;
  max-width: 500px;
}

/* Make the overlay responsive */
@media (max-width: 768px) {
  .paragraph {
    width: 85%;
  }
  
  /* Individual mobile positioning - override desktop positioning */
  .paragraph1,
  .paragraph2,
  .paragraph3,
  .paragraph4 {
    left: 5%;
    right: auto;
  }
  
  /* Adjust spacing for mobile */
  .paragraph2 {
    top: calc(110vh + 45vh);
  }
  
  .paragraph3 {
    top: calc(110vh + 45vh + 45vh);
  }
  
  /* Update paragraph-4 to be below paragraph-3 on mobile */
  .paragraph4 {
    top: calc(110vh + 45vh + 45vh + 45vh);
  }
}

/* Logo container styling */
.logoContainer {
  display: flex;
  justify-content: left;
  margin-bottom: 1.5rem;
}

.sectionLogo {
  max-width: 400px; /* Adjust the size as needed */
  height: auto;
}

/* New styles for image containers and small subheadings */
.imageContainer {
  width: 100%;
  margin-bottom: 1rem;
  overflow: hidden;
  border-radius: 4px;
}

.sectionImage {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.sectionImage:hover {
  transform: scale(1.03);
}

.smallHeading {
  margin-bottom: 0.75rem;
}

.smallHeading h3 {
  font-family: 'Telegraf', sans-serif;
  font-weight: 500;
  font-size: 1.5rem; /* Slightly bigger than paragraph text (1.2rem) */
  line-height: 1.3;
  margin: 0;
  color: white;
}




/* Add these styles to your Steps.module.css file */

/* AnimatedText container styling within the steps module */
.paragraphContainer .animated-text-container {
  width: 100%;
  overflow: visible;
  margin-bottom: 0.5rem;
}

/* Logo container AnimatedText specific styling */
.logoContainer .animated-text-container h2 {
  font-weight: 700;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  line-height: 1.3;
}

/* Small heading AnimatedText styling */
.smallHeading .animated-text-container h3 {
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

/* Paragraph AnimatedText styling */
.paragraphContainer .animated-text-container p {
  font-size: 1rem;
  line-height: 1.6;
  font-weight: 400;
}




/* Mobile optimizations for Steps.module.css */
@media (max-width: 768px) {
  .scrollOverlay {
    z-index: 10;
    position: relative;
    background-color: transparent !important;
  }
  
  .contentSection {
    padding: 20px 15px;
  }
  
  /* Remove backgrounds from containers */
  .mobileContainer {
    background-color: transparent !important;
    padding: 15px;
    border-radius: 0;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
  }
  
  /* Text styling optimizations */
  .paragraphContainer h2 {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
    max-width: 100%; /* Allow full width on mobile */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
  }
  
  .paragraphContainer p {
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
    max-width: 100%; /* Allow full width on mobile */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
  }
  
  .smallHeading h3 {
    font-size: 1.1rem !important;
    line-height: 1.2 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
  }
  
  /* Adjust spacing for process steps on mobile */
  .paragraph4 {
    top: calc(110vh + 45vh + 45vh + 45vh);
  }
}