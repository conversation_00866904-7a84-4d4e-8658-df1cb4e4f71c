import React, { Suspense, useRef, useState, useEffect } from 'react';
import { Can<PERSON>, useFrame, useThree } from '@react-three/fiber';
import { useGLTF, OrbitControls, MeshTransmissionMaterial } from '@react-three/drei';
import * as THREE from 'three';
import WaterMatcapBackground from '../components/WaterMatcap/WaterMatcapBackground';
import ExplainOrbitCamera from '../components/WaterMatcap/scene_copy/ExplainOrbitCamera';
import { useControls } from 'leva';

function CenterModel(props) {
  const { nodes, materials } = useGLTF('/models/cube_high_comp.glb');
  const icon = useGLTF('/models/icon_ai_comp.glb');
  return (
    <group {...props} dispose={null}>
      {/* Render all meshes in the GLB, or adjust as needed */}
      {Object.values(nodes).map((node, i) =>
        node.type === 'Mesh' ? (
          <mesh
            key={i}
            geometry={node.geometry}
            castShadow
            receiveShadow
          >
            <MeshTransmissionMaterial
              backside
              samples={4}
              thickness={3}
              chromaticAberration={0}
              anisotropy={0.1}
              distortion={0}
              distortionScale={0}
              temporalDistortion={0}
              iridescence={1}
              iridescenceIOR={1}
              iridescenceThicknessRange={[0, 1400]}
            />
          </mesh>
        ) : null
      )}
      {/* Place the icon_ai_comp.glb model inside the CenterModel */}
      {icon.scene && (
        <primitive
          object={icon.scene}
          position={[0, 0, 0]}
          scale={[0.75, 0.75, 0.75]}
        />
      )}
    </group>
  );
}

useGLTF.preload('/models/extruded-try-soft-cube-round-whole.glb');

const CAMERA_POSITIONS = [
  { name: 'Top', position: [0, 1.5, 0], lookAt: [0, 0, 0] },
  { name: 'Front', position: [-0.16, 0.38, 1.02], lookAt: [0, 0.05, 0] },
  { name: 'Left', position: [1.37, 0.79, 0.86], lookAt: [0, 0, 0] },
  { name: 'Back', position: [0.77, 1.68, 3], lookAt: [0.03, 0.64, -0.04] },
  { name: 'Right', position: [0.78, 0.31, 0.75], lookAt: [0.52, 0.28, 0.02] },
];

function ExplainCameraController({ cameraIndex }) {
  // Define the two positions
  const positions = [
    [0, 3, 0], // Top
    [3, 1, 0], // Side
  ];
  return null; // No camera logic here anymore
}

function BlackBackground() {
  const { scene } = useThree();
  React.useEffect(() => {
    scene.background = new THREE.Color('#000000');
    return () => {
      scene.background = null;
    };
  }, [scene]);
  return null;
}

export default function ExplainSection() {
  const [cameraIndex, setCameraIndex] = useState(0);

  // Leva controls for interactive camera tuning
  const [{ camPos, camLookAt }, set] = useControls(() => ({
    camPos: { value: CAMERA_POSITIONS[0].position, label: 'Camera Position' },
    camLookAt: { value: CAMERA_POSITIONS[0].lookAt, label: 'Look At' },
  }), []);

  // OrbitControls ref
  const controlsRef = useRef();

  // Reset OrbitControls when switching to Top
  useEffect(() => {
    if (cameraIndex === 0 && controlsRef.current) {
      controlsRef.current.target.set(0, 0, 0);
      controlsRef.current.update();
      if (controlsRef.current.setAzimuthalAngle) controlsRef.current.setAzimuthalAngle(0);
      if (controlsRef.current.setPolarAngle) controlsRef.current.setPolarAngle(0);
    }
  }, [cameraIndex]);

  // Handler to update both cameraIndex and Leva controls
  const handleCameraButton = (idx) => {
    setCameraIndex(idx);
    set({
      camPos: CAMERA_POSITIONS[idx].position,
      camLookAt: CAMERA_POSITIONS[idx].lookAt,
    });
  };

  // Overlay content for each camera position
  const overlays = [
    // Top
    (
      <div style={{
        position: 'absolute',
        top: 0, left: 0, width: '100vw', height: '100vh',
        display: 'flex', alignItems: 'center', justifyContent: 'center',
        pointerEvents: 'none', zIndex: 10
      }}>
        <h1 style={{
          color: '#fff',
          fontSize: '4vw',
          fontWeight: 700,
          letterSpacing: '0.02em',
          textAlign: 'center',
          textShadow: '0 2px 16px #000a'
        }}>
          TOP VIEW
        </h1>
      </div>
    ),
    // Front (Lusion Labs style)
    (
      <>
        {/* Left text block */}
        <div style={{
          position: 'absolute',
          left: 0, top: 0, height: '100vh', width: '50vw',
          display: 'flex', flexDirection: 'column', justifyContent: 'center',
          alignItems: 'flex-start', paddingLeft: '6vw', zIndex: 10,
          pointerEvents: 'none'
        }}>
          <h1 style={{ color: '#fff', fontSize: '3vw', fontWeight: 700, margin: 0, letterSpacing: '0.01em' }}>
            ALUMINIUM<br />UNIBODY
          </h1>
          <h2 style={{ color: '#fff', fontSize: '1.2vw', fontWeight: 400, margin: '2vw 0 0.5vw 0', letterSpacing: '0.08em', opacity: 0.8 }}>
            PRECISION-CRAFTED STRUCTURE
          </h2>
          <p style={{ color: '#fff', fontSize: '1vw', fontWeight: 300, maxWidth: '30vw', opacity: 0.7, margin: 0 }}>
            Aluminium is the ideal material for louder speaker cabinets thanks to its outstanding acoustic quality and elegant look.
          </p>
        </div>
        {/* Right vertical navigation */}
        <div style={{
          position: 'absolute',
          right: 30, top: 0, height: '100vh', width: '120px',
          display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-end',
          zIndex: 10, pointerEvents: 'none'
        }}>
          <div style={{ position: 'relative', height: `${CAMERA_POSITIONS.length * 60}px`, width: '100%' }}>
            {/* Progress bar background */}
            <div style={{
              position: 'absolute',
              right: 0,
              top: 0,
              width: '1px',
              height: '100%',
              background: '#222',
              borderRadius: '1px',
              zIndex: 1,
            }} />
            {/* Progress bar fill */}
            {(() => {
              const total = CAMERA_POSITIONS.length;
              const navHeight = total * 60;
              const y = (cameraIndex / (total - 1)) * (navHeight - 1);
              return (
                <div style={{
                  position: 'absolute',
                  right: 0,
                  top: 0,
                  width: '1px',
                  height: `${y + 1}px`,
                  background: '#fff',
                  borderRadius: '1px',
                  zIndex: 2,
                  transition: 'height 0.4s cubic-bezier(.4,2,.6,1)',
                }} />
              );
            })()}
            {/* Progress bar cubes/dots */}
            {CAMERA_POSITIONS.map((pos, idx) => {
              const total = CAMERA_POSITIONS.length;
              const navHeight = total * 60;
              const y = (idx / (total - 1)) * (navHeight - 1);
              const filled = idx <= cameraIndex;
              return (
                <div key={pos.name + '-cube'} style={{
                  position: 'absolute',
                  right: '-2.5px',
                  top: `${y - 3}px`,
                  width: '8px',
                  height: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 4,
                }}>
                  <div style={{
                    width: '5px',
                    height: '5px',
                    borderRadius: '1.5px',
                    background: filled ? '#fff' : '#444',
                    border: filled ? 'none' : '1px solid #444',
                    transition: 'background 0.3s, border 0.3s',
                  }} />
                </div>
              );
            })}
            {/* Navigation labels */}
            <div style={{
              position: 'absolute',
              right: 16,
              top: 0,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-end',
              gap: '32px',
              height: '100%',
              zIndex: 3,
              width: '90px',
              pointerEvents: 'none',
            }}>
              {CAMERA_POSITIONS.map((pos, idx) => {
                const total = CAMERA_POSITIONS.length;
                const navHeight = total * 60;
                const y = (idx / (total - 1)) * (navHeight - 1);
                return (
                  <div key={pos.name} style={{
                    color: idx === cameraIndex ? '#fff' : '#888',
                    fontWeight: 400,
                    fontSize: '0.7vw',
                    letterSpacing: '0.08em',
                    opacity: idx === cameraIndex ? 1 : 0.5,
                    transition: 'color 0.3s, opacity 0.3s, font-weight 0.3s',
                    lineHeight: '1',
                    height: '8px',
                    display: 'flex', alignItems: 'center',
                    position: 'absolute',
                    right: 0,
                    top: `calc(${y}px - 3px)`,
                  }}>
                    {pos.name.toUpperCase()}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </>
    ),
    // Left (Lusion Labs style)
    (
      <>
        {/* Left text block for Left camera */}
        <div style={{
          position: 'absolute',
          left: 0, top: 0, height: '100vh', width: '50vw',
          display: 'flex', flexDirection: 'column', justifyContent: 'center',
          alignItems: 'flex-start', paddingLeft: '6vw', zIndex: 10,
          pointerEvents: 'none'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1.5vw', marginBottom: '2vw' }}>
            {/* Icon 1: Elegant Design */}
            <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="4" y="4" width="32" height="32" rx="8" stroke="#fff" strokeWidth="2"/>
              <text x="50%" y="55%" textAnchor="middle" fill="#fff" fontSize="10" fontFamily="monospace" dy=".3em">YYY</text>
            </svg>
            <div>
              <h1 style={{ color: '#fff', fontSize: '1.5vw', fontWeight: 700, margin: 0, letterSpacing: '0.01em' }}>
                ELEGANT DESIGN
              </h1>
              <p style={{ color: '#fff', fontSize: '1vw', fontWeight: 300, maxWidth: '30vw', opacity: 0.7, margin: 0 }}>
                The highly elegant design with the rounded cabinet shape at the back is timeless and offers a stunning visual highlight in any living room.
              </p>
            </div>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1.5vw' }}>
            {/* Icon 2: Woofer */}
            <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="20" cy="20" r="16" stroke="#fff" strokeWidth="2"/>
              <circle cx="20" cy="20" r="8" stroke="#fff" strokeWidth="2"/>
              <circle cx="20" cy="20" r="3" fill="#fff"/>
            </svg>
            <div>
              <h1 style={{ color: '#fff', fontSize: '1.5vw', fontWeight: 700, margin: 0, letterSpacing: '0.01em' }}>
                ONE WOOFER
              </h1>
              <p style={{ color: '#fff', fontSize: '1vw', fontWeight: 300, maxWidth: '30vw', opacity: 0.7, margin: 0 }}>
                A large membrane area with a 20 cm diameter enables the coaxial ribbon to deliver a powerful bass.
              </p>
            </div>
          </div>
        </div>
        {/* Right vertical navigation (same as Front, but Left highlighted) */}
        <div style={{
          position: 'absolute',
          right: 30, top: 0, height: '100vh', width: '120px',
          display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-end',
          zIndex: 10, pointerEvents: 'none'
        }}>
          <div style={{ position: 'relative', height: `${CAMERA_POSITIONS.length * 60}px`, width: '100%' }}>
            {/* Progress bar background */}
            <div style={{
              position: 'absolute',
              right: 0,
              top: 0,
              width: '1px',
              height: '100%',
              background: '#222',
              borderRadius: '1px',
              zIndex: 1,
            }} />
            {/* Progress bar fill */}
            {(() => {
              const total = CAMERA_POSITIONS.length;
              const navHeight = total * 60;
              const y = (2 / (total - 1)) * (navHeight - 1); // 2 = Left index
              return (
                <div style={{
                  position: 'absolute',
                  right: 0,
                  top: 0,
                  width: '1px',
                  height: `${y + 1}px`,
                  background: '#fff',
                  borderRadius: '1px',
                  zIndex: 2,
                  transition: 'height 0.4s cubic-bezier(.4,2,.6,1)',
                }} />
              );
            })()}
            {/* Progress bar cubes/dots */}
            {CAMERA_POSITIONS.map((pos, idx) => {
              const total = CAMERA_POSITIONS.length;
              const navHeight = total * 60;
              const y = (idx / (total - 1)) * (navHeight - 1);
              const filled = idx <= 2; // 2 = Left index
              return (
                <div key={pos.name + '-cube'} style={{
                  position: 'absolute',
                  right: '-2.5px',
                  top: `${y - 3}px`,
                  width: '8px',
                  height: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 4,
                }}>
                  <div style={{
                    width: '5px',
                    height: '5px',
                    borderRadius: '1.5px',
                    background: filled ? '#fff' : '#444',
                    border: filled ? 'none' : '1px solid #444',
                    transition: 'background 0.3s, border 0.3s',
                  }} />
                </div>
              );
            })}
            {/* Navigation labels */}
            <div style={{
              position: 'absolute',
              right: 16,
              top: 0,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-end',
              gap: '32px',
              height: '100%',
              zIndex: 3,
              width: '90px',
              pointerEvents: 'none',
            }}>
              {CAMERA_POSITIONS.map((pos, idx) => {
                const total = CAMERA_POSITIONS.length;
                const navHeight = total * 60;
                const y = (idx / (total - 1)) * (navHeight - 1);
                return (
                  <div key={pos.name} style={{
                    color: idx === 2 ? '#fff' : '#888',
                    fontWeight: 400,
                    fontSize: '0.7vw',
                    letterSpacing: '0.08em',
                    opacity: idx === 2 ? 1 : 0.5,
                    transition: 'color 0.3s, opacity 0.3s, font-weight 0.3s',
                    lineHeight: '1',
                    height: '8px',
                    display: 'flex', alignItems: 'center',
                    position: 'absolute',
                    right: 0,
                    top: `calc(${y}px - 3px)`,
                  }}>
                    {pos.name.toUpperCase()}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </>
    ),
    // Back
    (
      <>
        {/* Centered content for Back camera */}
        <div style={{
          position: 'absolute',
          top: 0, left: 0, width: '100vw', height: '100vh',
          display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center',
          zIndex: 10, pointerEvents: 'none',
        }}>
          <h1 style={{
            color: '#fff',
            fontSize: '4vw',
            fontWeight: 400,
            letterSpacing: '0.03em',
            textAlign: 'center',
            margin: 0,
            textShadow: '0 2px 16px #000a',
            fontFamily: 'inherit',
          }}>
            HEAR THE DIFFERENCE
          </h1>
          <div style={{ height: '3vw' }} />
          <div style={{
            color: '#fff',
            fontSize: '1.1vw',
            fontWeight: 400,
            letterSpacing: '0.04em',
            textAlign: 'center',
            opacity: 0.85,
            maxWidth: '40vw',
            margin: '0 auto',
            lineHeight: 1.4,
          }}>
            THE PERFECT AUDIO – RE-ENGINEERED.<br />
            POWERED BY OUR IN-HOUSE CHIP FOR NEXT<br />
            GENERATION AUDIO PROCESSING.
          </div>
          <div style={{ height: '2vw' }} />
          <div style={{ display: 'flex', gap: '1vw', justifyContent: 'center' }}>
            {['AMBIENT', 'ELECTRONIC', 'ACOUSTIC'].map((label, i) => (
              <div key={label} style={{
                padding: '0.6vw 2vw',
                border: '2px solid #fff',
                borderRadius: '2vw',
                color: '#fff',
                fontSize: '1vw',
                fontWeight: 500,
                letterSpacing: '0.08em',
                background: i === 2 ? '#fff1' : 'transparent',
                boxShadow: i === 2 ? '0 0 8px #fff2' : 'none',
                margin: 0,
                pointerEvents: 'auto',
                cursor: 'pointer',
                transition: 'background 0.2s, color 0.2s',
              }}>
                {label}
              </div>
            ))}
          </div>
        </div>
        {/* Right vertical navigation (same as Left, but Back highlighted) */}
        <div style={{
          position: 'absolute',
          right: 30, top: 0, height: '100vh', width: '120px',
          display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-end',
          zIndex: 10, pointerEvents: 'none'
        }}>
          <div style={{ position: 'relative', height: `${CAMERA_POSITIONS.length * 60}px`, width: '100%' }}>
            {/* Progress bar background */}
            <div style={{
              position: 'absolute',
              right: 0,
              top: 0,
              width: '1px',
              height: '100%',
              background: '#222',
              borderRadius: '1px',
              zIndex: 1,
            }} />
            {/* Progress bar fill */}
            {(() => {
              const total = CAMERA_POSITIONS.length;
              const navHeight = total * 60;
              const y = (3 / (total - 1)) * (navHeight - 1); // 3 = Back index
              return (
                <div style={{
                  position: 'absolute',
                  right: 0,
                  top: 0,
                  width: '1px',
                  height: `${y + 1}px`,
                  background: '#fff',
                  borderRadius: '1px',
                  zIndex: 2,
                  transition: 'height 0.4s cubic-bezier(.4,2,.6,1)',
                }} />
              );
            })()}
            {/* Progress bar cubes/dots */}
            {CAMERA_POSITIONS.map((pos, idx) => {
              const total = CAMERA_POSITIONS.length;
              const navHeight = total * 60;
              const y = (idx / (total - 1)) * (navHeight - 1);
              const filled = idx <= 3; // 3 = Back index
              return (
                <div key={pos.name + '-cube'} style={{
                  position: 'absolute',
                  right: '-2.5px',
                  top: `${y - 3}px`,
                  width: '8px',
                  height: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 4,
                }}>
                  <div style={{
                    width: '5px',
                    height: '5px',
                    borderRadius: '1.5px',
                    background: filled ? '#fff' : '#444',
                    border: filled ? 'none' : '1px solid #444',
                    transition: 'background 0.3s, border 0.3s',
                  }} />
                </div>
              );
            })}
            {/* Navigation labels */}
            <div style={{
              position: 'absolute',
              right: 16,
              top: 0,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-end',
              gap: '32px',
              height: '100%',
              zIndex: 3,
              width: '90px',
              pointerEvents: 'none',
            }}>
              {CAMERA_POSITIONS.map((pos, idx) => {
                const total = CAMERA_POSITIONS.length;
                const navHeight = total * 60;
                const y = (idx / (total - 1)) * (navHeight - 1);
                return (
                  <div key={pos.name} style={{
                    color: idx === 3 ? '#fff' : '#888',
                    fontWeight: 400,
                    fontSize: '0.7vw',
                    letterSpacing: '0.08em',
                    opacity: idx === 3 ? 1 : 0.5,
                    transition: 'color 0.3s, opacity 0.3s, font-weight 0.3s',
                    lineHeight: '1',
                    height: '8px',
                    display: 'flex', alignItems: 'center',
                    position: 'absolute',
                    right: 0,
                    top: `calc(${y}px - 3px)`,
                  }}>
                    {pos.name.toUpperCase()}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </>
    ),
    // Right
    (
      <>
        {/* Right-aligned content for Right camera */}
        <div style={{
          position: 'absolute',
          top: 0, left: 0, width: '100vw', height: '100vh',
          display: 'flex', flexDirection: 'column', alignItems: 'flex-end', justifyContent: 'center',
          zIndex: 10, pointerEvents: 'none',
        }}>
          <div style={{
            width: '48vw',
            marginRight: '6vw',
            display: 'flex', flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'center',
          }}>
            <h1 style={{
              color: '#fff',
              fontSize: '3.5vw',
              fontWeight: 400,
              letterSpacing: '0.03em',
              textAlign: 'left',
              margin: 0,
              lineHeight: 1.05,
              textShadow: '0 2px 16px #000a',
              fontFamily: 'inherit',
            }}>
              CUSTOMIZABLE<br />FINISH
            </h1>
            <div style={{ height: '2vw' }} />
            <div style={{
              color: '#fff',
              fontSize: '1vw',
              fontWeight: 400,
              letterSpacing: '0.04em',
              textAlign: 'left',
              opacity: 0.85,
              maxWidth: '32vw',
              lineHeight: 1.4,
              marginBottom: '2vw',
            }}>
              Aluminium is one of the lightest and strongest materials on earth, providing portability and rigidity to our products. Both saturation and lightness can be used to enhance the depth of anodisation and paint process.
            </div>
            <div style={{ color: '#fff', fontSize: '0.95vw', fontWeight: 400, opacity: 0.8, marginBottom: '1vw', letterSpacing: '0.08em' }}>
              CHANGE MATERIAL
            </div>
            <div style={{ display: 'flex', gap: '1vw', alignItems: 'center' }}>
              {/* Material buttons */}
              {[0,1,2,3].map((idx) => (
                <div key={idx} style={{
                  width: '3vw',
                  height: '3vw',
                  borderRadius: '50%',
                  border: '2px solid #fff',
                  background: idx === 0 ? '#fff' : 'transparent',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: 0,
                  boxShadow: idx === 0 ? '0 0 8px #fff2' : 'none',
                  pointerEvents: 'auto',
                  cursor: 'pointer',
                  transition: 'background 0.2s, border 0.2s',
                }}>
                  {/* Simple SVG pattern for demo purposes */}
                  {idx === 0 && (
                    <svg width="28" height="28" viewBox="0 0 28 28" fill="none"><g><circle cx="14" cy="14" r="10" fill="#222" /><circle cx="14" cy="14" r="6" fill="#fff" /></g></svg>
                  )}
                  {idx === 1 && (
                    <svg width="28" height="28" viewBox="0 0 28 28" fill="none"><g><circle cx="14" cy="14" r="10" stroke="#fff" strokeWidth="2" /><circle cx="14" cy="14" r="4" stroke="#fff" strokeWidth="2" /></g></svg>
                  )}
                  {idx === 2 && (
                    <svg width="28" height="28" viewBox="0 0 28 28" fill="none"><g><circle cx="14" cy="14" r="10" stroke="#fff" strokeWidth="2" /><circle cx="14" cy="14" r="10" stroke="#fff" strokeWidth="2" strokeDasharray="2 4" /></g></svg>
                  )}
                  {idx === 3 && (
                    <svg width="28" height="28" viewBox="0 0 28 28" fill="none"><g><rect x="6" y="6" width="16" height="16" stroke="#fff" strokeWidth="2" /><line x1="8" y1="20" x2="20" y2="8" stroke="#fff" strokeWidth="2" /></g></svg>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
        {/* Right vertical navigation (same as before, but Right highlighted) */}
        <div style={{
          position: 'absolute',
          right: 30, top: 0, height: '100vh', width: '120px',
          display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-end',
          zIndex: 10, pointerEvents: 'none'
        }}>
          <div style={{ position: 'relative', height: `${CAMERA_POSITIONS.length * 60}px`, width: '100%' }}>
            {/* Progress bar background */}
            <div style={{
              position: 'absolute',
              right: 0,
              top: 0,
              width: '1px',
              height: '100%',
              background: '#222',
              borderRadius: '1px',
              zIndex: 1,
            }} />
            {/* Progress bar fill */}
            {(() => {
              const total = CAMERA_POSITIONS.length;
              const navHeight = total * 60;
              const y = (4 / (total - 1)) * (navHeight - 1); // 4 = Right index
              return (
                <div style={{
                  position: 'absolute',
                  right: 0,
                  top: 0,
                  width: '1px',
                  height: `${y + 1}px`,
                  background: '#fff',
                  borderRadius: '1px',
                  zIndex: 2,
                  transition: 'height 0.4s cubic-bezier(.4,2,.6,1)',
                }} />
              );
            })()}
            {/* Progress bar cubes/dots */}
            {CAMERA_POSITIONS.map((pos, idx) => {
              const total = CAMERA_POSITIONS.length;
              const navHeight = total * 60;
              const y = (idx / (total - 1)) * (navHeight - 1);
              const filled = idx <= 4; // 4 = Right index
              return (
                <div key={pos.name + '-cube'} style={{
                  position: 'absolute',
                  right: '-2.5px',
                  top: `${y - 3}px`,
                  width: '8px',
                  height: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 4,
                }}>
                  <div style={{
                    width: '5px',
                    height: '5px',
                    borderRadius: '1.5px',
                    background: filled ? '#fff' : '#444',
                    border: filled ? 'none' : '1px solid #444',
                    transition: 'background 0.3s, border 0.3s',
                  }} />
                </div>
              );
            })}
            {/* Navigation labels */}
            <div style={{
              position: 'absolute',
              right: 16,
              top: 0,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-end',
              gap: '32px',
              height: '100%',
              zIndex: 3,
              width: '90px',
              pointerEvents: 'none',
            }}>
              {CAMERA_POSITIONS.map((pos, idx) => {
                const total = CAMERA_POSITIONS.length;
                const navHeight = total * 60;
                const y = (idx / (total - 1)) * (navHeight - 1);
                return (
                  <div key={pos.name} style={{
                    color: idx === 4 ? '#fff' : '#888',
                    fontWeight: 400,
                    fontSize: '0.7vw',
                    letterSpacing: '0.08em',
                    opacity: idx === 4 ? 1 : 0.5,
                    transition: 'color 0.3s, opacity 0.3s, font-weight 0.3s',
                    lineHeight: '1',
                    height: '8px',
                    display: 'flex', alignItems: 'center',
                    position: 'absolute',
                    right: 0,
                    top: `calc(${y}px - 3px)`,
                  }}>
                    {pos.name.toUpperCase()}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </>
    ),
  ];

  return (
    <div style={{ position: 'relative', width: '100vw', height: '100vh', overflow: 'hidden' }}>
      <Canvas
        camera={{ position: camPos, fov: 45 }} // initial, will be overridden
        gl={{ alpha: true, antialias: true, powerPreference: 'high-performance', clearColor: [0, 0, 0, 1] }}
        style={{ position: 'absolute', inset: 0, width: '100vw', height: '100vh', display: 'block', zIndex: 0 }}
      >
        <Suspense fallback={null}>
          <BlackBackground />
          <WaterMatcapBackground position={[0, 0, 0]} />
          <CenterModel position={[0, 0.32, 0]} scale={[0.20, 0.30, 0.20]} />
          <ExplainOrbitCamera position={camPos} lookAt={camLookAt} />
          <OrbitControls ref={controlsRef} />
        </Suspense>
      </Canvas>
      {/* Camera position buttons */}
      <div style={{ position: 'absolute', top: 20, left: 20, zIndex: 2, display: 'flex', gap: 8 }}>
        {CAMERA_POSITIONS.map((pos, idx) => (
          <button
            key={pos.name}
            onClick={() => handleCameraButton(idx)}
            style={{
              padding: '8px 16px',
              background: cameraIndex === idx ? '#222' : '#fff',
              color: cameraIndex === idx ? '#fff' : '#222',
              border: '1px solid #222',
              borderRadius: 4,
              cursor: 'pointer',
              fontWeight: 'bold',
            }}
          >
            {pos.name}
          </button>
        ))}
      </div>
      {/* Overlay for current camera position */}
      {overlays[cameraIndex]}
      {/* Content for ExplainSection will go here */}
      <div style={{ position: 'relative', zIndex: 1 }}>
        {/* TODO: Add ExplainSection content here */}
      </div>
    </div>
  );
} 