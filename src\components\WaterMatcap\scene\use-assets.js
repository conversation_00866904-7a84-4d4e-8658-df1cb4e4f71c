import { useGLTF, useTexture } from "@react-three/drei";
import { useMemo } from "react";
import * as THREE from "three";

export function useAssets() {
  // Load the GLB and extract the mesh
  const { nodes } = useGLTF("/models/extruded-try-soft-cube-round.glb");

  const pyramid = nodes["Cylinder"];

  const envMap = useTexture("/textures/hdri4.png");
  const matcap = useTexture("/textures/matcap-daniel.jpg");

  envMap.magFilter = THREE.NearestFilter;
  envMap.minFilter = THREE.NearestFilter;
  envMap.wrapS = THREE.RepeatWrapping;
  envMap.wrapT = THREE.RepeatWrapping;

  const noiseMap = useTexture("/textures/noise-LDR_RGBA_63.png");

  noiseMap.wrapS = THREE.RepeatWrapping;
  noiseMap.wrapT = THREE.RepeatWrapping;

  const assets = useMemo(() => {
    return {
      envMap,
      noiseMap,
      matcap,
      pyramid
    };
  }, [envMap, noiseMap, matcap, pyramid]);

  return assets;
}