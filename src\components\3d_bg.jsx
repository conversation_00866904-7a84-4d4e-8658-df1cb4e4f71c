import React from 'react'
import { useGLTF } from '@react-three/drei'
import * as THREE from 'three'

export default function Model(props) {
  const { nodes, materials } = useGLTF('/3d_bg.glb')
  
  // Create a new standard material with customized properties
  const standardMaterial = new THREE.MeshStandardMaterial({
    color: 0xCCCCCC,       // Light gray color for better visibility
    roughness: 0.3,        // Lower roughness for more reflections
    metalness: 0.7,        // Higher metalness for better reflections
    envMapIntensity: 1.0,  // Environment map intensity for reflections
    side: THREE.DoubleSide // Render both sides of the geometry
  })
  
  return (
    <group {...props} dispose={null}>
      {/* Use the original geometry but with our custom material */}
      <mesh
        geometry={nodes.panel_Material_0.geometry}
        material={standardMaterial}
        castShadow
        receiveShadow
      />
    </group>
  )
}

useGLTF.preload('/3d_bg.glb')