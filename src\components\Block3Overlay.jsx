import React from 'react';
import styles from './Block3Overlay.module.css';

export default function Block3Overlay({ onClose }) {
  return (
    <>
      <div className={styles.arrowRight}>
        <img src="/arrow_long.svg" alt="Arrow Right" width={40} height={40} />
      </div>
      <div className={styles.arrowLeft}>
        <img src="/arrow_long.svg" alt="Arrow Left" width={40} height={40} className={styles.arrowLeftImg} />
      </div>
      <button onClick={onClose} className={styles.closeButton} aria-label="Close overlay">CLOSE</button>
      <div className={styles.overlay}>
        <div className={styles.contentWrapper}>
          <h1 className={styles.title}>So läuft die Einführung von KI Automation ab</h1>
          <p className={styles.description}>
            Die Einführung von KI Automation verläuft in klaren Schritten – von der Analyse bestehender Abläufe bis hin zur laufenden Optimierung im Betrieb. Dabei werden Prozesse zuerst digital erfasst, anschließend durch passende Tools automatisiert und schrittweise in den Arbeitsalltag integriert. Das Besondere: Die KI lernt kontinuierlich mit, wird immer präziser und übernimmt zunehmend komplexere Aufgaben.
          </p>
          <h2 className={styles.infoHeading}>In 6 Schritten zur smarten Automatisierung:</h2>
          <div className={styles.statsGrid}>
            <div className={styles.statCard}></div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statNumber}>01</div>
              <div className={styles.statTag}>PROZESSANALYSE</div>
              <div className={styles.statDesc}>Zuerst werden gemeinsam die Prozesse identifiziert, die besonders viel Zeit kosten, häufig Fehler verursachen oder regelmäßig wiederkehren. Diese bilden das Fundament für die Automatisierung.</div>
            </div>

            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statNumber}>02</div>
              <div className={styles.statTag}>ENTWICKLUNG</div>
              <div className={styles.statDesc}>Auf Basis deiner Anforderungen wird eine individuelle KI- oder Automatisierungslösung konzipiert, gestaltet und exakt auf deine Prozesse zugeschnitten.</div>
            </div>
            <div className={styles.statCard}></div>

            <div className={styles.statCard}></div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statNumber}>03</div>
              <div className={styles.statTag}>INTEGRATION</div>
              <div className={styles.statDesc}>Die Lösung wird reibungslos in deine bestehende Systemlandschaft eingebunden – egal ob CRM, ERP oder andere Plattformen. Aufwändige IT-Eingriffe? Meist unnötig.</div>
            </div>

            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statNumber}>04</div>
              <div className={styles.statTag}>TESTPHASE</div>
              <div className={styles.statDesc}>In einer sicheren Umgebung wird alles auf Herz und Nieren geprüft. Was noch nicht ganz rund läuft, wird gezielt nachgebessert – bis alles sitzt.</div>
            </div>
            <div className={styles.statCard}></div>

            <div className={styles.statCard}></div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statNumber}>05</div>
              <div className={styles.statTag}>LIVE-BETRIEB</div>
              <div className={styles.statDesc}>Jetzt übernimmt die KI. Sie arbeitet verlässlich im Hintergrund, lernt mit jeder neuen Situation dazu und wird mit der Zeit immer treffsicherer – ganz automatisch.</div>
            </div>
            <div className={styles.statCard}>
              <span className={styles.statIcon}>
                <img src="/Blcks_Icon.svg" alt="Logo" width={20} height={20} />
              </span>
              <div className={styles.statNumber}>06</div>
              <div className={styles.statTag}>OPTIMIERUNG</div>
              <div className={styles.statDesc}>Auch nach dem Go-Live lassen wir dich nicht allein. Wir begleiten dich mit technischem Support, regelmäßigen Optimierungen und helfen dir, neue Potenziale zu erkennen und zu nutzen.</div>
            </div>
          </div>
          <div className={styles.divider}></div>
        </div>
      </div>
    </>
  );
} 