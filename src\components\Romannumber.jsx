import React from 'react'
import { useGLTF } from '@react-three/drei'
import { MeshStandardMaterial } from 'three'
import * as THREE from 'three'

  // Create materials with refs to update opacity later
  const whiteMaterial = new MeshStandardMaterial({
    color: '#fff',
    emissive: '#fff',
    roughness: 0.3,
    metalness: 0.8,
    emissiveIntensity: 2,
  })

export default function Model(props) {
  const { nodes, materials } = useGLTF('/romannumber.glb')
  return (
    <group {...props} dispose={null}>
      <group scale={0.01}>
        <mesh geometry={nodes.Text.geometry} material={whiteMaterial} position={[-11.7, 0, 0]} />
      </group>
    </group>
  )
}

useGLTF.preload('/romannumber.glb')
