import React, { useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { Draggable } from 'gsap/Draggable';
import styles from './ServicesOverlaySection.module.css';

const aiSolutionsBullets = [
  [
    'Agentic Orchestrations',
    'Augmented LLMs',
    'Advanced Tool Calling',
  ],
  [
    'AI-Driven Data Management',
    'Retrieval-Augmented Generation (RAG)',
    'Memory Persistence',
  ],
  [
    'Dynamic Prompting',
    'Choice of Base Language Model',
  ],
];

gsap.registerPlugin(Draggable);

// Helper to scroll to a section with a 50px offset from the top
function scrollToSectionWithOffset(ref, offset = 50) {
  if (!ref.current) return;
  const scrollContainer = document.querySelector(`.${styles.contentRightScrollable}`);
  if (!scrollContainer) return;
  const elementTop = ref.current.getBoundingClientRect().top;
  const containerTop = scrollContainer.getBoundingClientRect().top;
  const scrollOffset = elementTop - containerTop + scrollContainer.scrollTop - offset;
  scrollContainer.scrollTo({ top: scrollOffset, behavior: 'smooth' });
}

export default function ServicesOverlaySection({ open, onClose, initialSection }) {
  const [activeSection, setActiveSection] = useState('AI SOLUTIONS');
  const [activeStatsCard, setActiveStatsCard] = useState(0);
  const aiSolutionsRef = useRef(null);
  const xaasRef = useRef(null);
  const professionalRef = useRef(null);
  const productCardsGridRef = useRef(null);
  const [productCardIndex, setProductCardIndex] = useState(0);
  const draggableInstance = useRef(null);
  const statsCardsRowRef = useRef(null);
  const statsDraggableInstance = useRef(null);
  const [statsCardIndex, setStatsCardIndex] = useState(0);
  const xaasFeatureCardsRowRef = useRef(null);
  const xaasDraggableInstance = useRef(null);
  const [xaasCardIndex, setXaasCardIndex] = useState(0);
  const profServicesFeatureCardsRowRef = useRef(null);
  const profDraggableInstance = useRef(null);
  const [profCardIndex, setProfCardIndex] = useState(0);
  const profServicesProcessRowRef = useRef(null);
  const profProcessDraggableInstance = useRef(null);
  const [profProcessCardIndex, setProfProcessCardIndex] = useState(0);
  const profFeatureCardBottomRowRef = useRef(null);
  const profFeatureCardBottomDraggableInstance = useRef(null);
  const [profFeatureCardBottomIndex, setProfFeatureCardBottomIndex] = useState(0);

  const statsCards = [
    {
      percent: 14,
      desc: <>Nur 14 % der Unternehmen weltweit sind vollständig darauf vorbereitet, KI-Technologien einzusetzen und effektiv zu nutzen.</>,
      source: 'Source: Cisco',
      visual: (
        <div className={styles.statsCardVisualLayout}>
          <div className={styles.statsCardDotGrid}>
            {[...Array(10)].map((_, row) => (
              <div key={row} className={styles.statsCardDotGridRow}>
                {[...Array(10)].map((_, col) => {
                  // Only last 4 dots in the second row from below are black, bottom row is all black
                  const isBottomRow = row === 9;
                  const isSecondFromBottom = row === 8;
                  const isFilled = isBottomRow || (isSecondFromBottom && col >= 6);
                  return <span key={col} className={isFilled ? styles.statsCardDotFilled : styles.statsCardDot} />;
                })}
              </div>
            ))}
          </div>
        </div>
      )
    },
    {
      percent: 92,
      desc: <>92 % der CEOs sind überzeugt, dass KI-Tools Unternehmen dabei helfen werden, effizienter zu arbeiten, die Produktivität zu steigern und die Profitabilität zu erhöhen.</>,
      source: 'Source: Forbes',
      visual: (
        <div className={styles.statsCardVisualLayout}>
          <div className={styles.statsCardDonutWrapper}>
            <svg viewBox="0 0 240 240" className={styles.statsCardDonutSvg}>
              <circle cx="120" cy="120" r="100" fill="#eaeaea" />
              <circle cx="120" cy="120" r="100" fill="none" stroke="#111" strokeWidth="20" strokeDasharray="628" strokeDashoffset="50" />
            </svg>
          </div>
        </div>
      )
    },
    {
      percent: 27,
      desc: <>Nur 27 % der CFOs sind sich mit ihren CIOs über strategische Prioritäten einig.</>,
      source: 'Source: Wakefield Research',
      visual: (
        <div className={styles.statsCardVisualLayout}>
          <div className={styles.statsCardVisualWhite}>
            <div className={styles.statsCardVisualGray}>
              <div className={styles.statsCardVisualBlack}></div>
            </div>
          </div>
        </div>
      )
    }
  ];

  const productCards = [
    {
      heading: 'KI-Agent für Websites',
      desc: '24/7 Chat-Support auf eigener Wissensbasis, mehrsprachig, integriert in Website, WhatsApp oder eigener App.'
    },
    {
      heading: 'KI Smarte E-Mail Inbox',
      desc: 'Automatisches Vorsortieren, Beantworten und Zusammenfassen von Mails – ideal für Vertrieb, Support & Admin.'
    },
    {
      heading: 'E-Mail-Outreach Automatisierung',
      desc: 'Personalisierte Lead-Akquise mit GPT, Auto-Follow-Ups & CRM-Integration.'
    },
    {
      heading: 'LinkedIn-Outreach Automatisierung',
      desc: 'Personalisierte Lead-Akquise mit GPT, Auto-Follow-Ups & CRM-Integration.'
    },
    {
      heading: (
        <>
          KI-generierter<br />
          Social Media Content
        </>
      ),
      desc: 'Automatisierte Erstellung von Texten, Hashtags und Bildern für LinkedIn, Instagram oder TikTok.'
    },
    {
      heading: (
        <>
          Blogartikel &<br />
          SEO-Content per KI
        </>
      ),
      desc: 'Recherche, Texterstellung und Formatierung für Google-optimierte Blogposts – in wenigen Minuten.'
    },
    {
      heading: (
        <>
          Dokumenten- &<br />
          Angebotsautomatisierung
        </>
      ),
      desc: 'Verträge, Angebote, Protokolle oder Präsentationen automatisch erstellen lassen – individuell & markenkonform.'
    },
    {
      heading: 'Rechnungs- & Beleg- verarbeitung (OCR + GPT)',
      desc: 'Belege hochladen → automatisch auslesen → direkt ins Buchhaltungstool übertragen.'
    },
    {
      heading: (
        <>
          KI-Feedback- &<br />
          Umfrageauswertung
        </>
      ),
      desc: 'Kundenzufriedenheit, Kommentare & Formulare automatisiert analysieren – inkl. Insights & Handlungsempfehlungen.'
    }
  ];

  // Remove loopCards and use productCards directly
  // const loopCards = [productCards[productCards.length - 1], ...productCards, productCards[0]];

  // Helper to get card width
  const getCardWidth = () => {
    if (!productCardsGridRef.current || !productCardsGridRef.current.children[0]) return 0;
    return productCardsGridRef.current.children[0].offsetWidth + 32; // 32px = gap (adjust if needed)
  };

  // Helper to get visible width of the grid wrapper
  const getGridWrapperWidth = () => {
    if (!productCardsGridRef.current || !productCardsGridRef.current.parentElement) return 0;
    return productCardsGridRef.current.parentElement.offsetWidth;
  };

  // Setup GSAP Draggable without loop and with correct bounds
  useEffect(() => {
    if (!open || !productCardsGridRef.current) return;
    if (draggableInstance.current) {
      draggableInstance.current[0].kill();
    }
    const cardWidth = getCardWidth();
    const totalCards = productCards.length;
    const gridWrapperWidth = getGridWrapperWidth();
    // Calculate the maxX and minX so the last card aligns with the right edge
    const totalWidth = cardWidth * totalCards;
    const maxX = 0;
    let minX = gridWrapperWidth - totalWidth;
    // Prevent positive minX (if cards fit in wrapper)
    if (minX > 0) minX = 0;
    draggableInstance.current = Draggable.create(productCardsGridRef.current, {
      type: 'x',
      inertia: true,
      edgeResistance: 0.85,
      bounds: { minX, maxX },
      cursor: 'grab',
      activeCursor: 'grabbing',
      snap: (endValue) => {
        // Snap to nearest card, but clamp so last card can't go past right edge
        let idx = Math.round(endValue / -cardWidth);
        idx = Math.max(0, Math.min(idx, totalCards - 1));
        let snapped = -idx * cardWidth;
        // Clamp snapped so last card aligns with right edge
        if (snapped < minX) snapped = minX;
        return snapped;
      },
      onDragEnd: function() {
        const idx = Math.round(this.x / -cardWidth);
        setProductCardIndex(Math.max(0, Math.min(idx, productCards.length - 1)));
      },
      onThrowComplete: function() {
        const idx = Math.round(this.x / -cardWidth);
        setProductCardIndex(Math.max(0, Math.min(idx, productCards.length - 1)));
      }
    });
    // Start at first card (index 0)
    gsap.set(productCardsGridRef.current, { x: 0 });
    setProductCardIndex(0);
    // eslint-disable-next-line
  }, [open]);

  // Button navigation without loop
  const handleNextProductCard = () => {
    if (!productCardsGridRef.current) return;
    const cardWidth = getCardWidth();
    const gridWrapperWidth = getGridWrapperWidth();
    const totalCards = productCards.length;
    const totalWidth = cardWidth * totalCards;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;

    setProductCardIndex((prev) => {
      let next = Math.min(prev + 1, totalCards - 1);
      let targetX = -next * cardWidth;
      if (targetX < minX) targetX = minX;
      gsap.to(productCardsGridRef.current, { x: targetX, duration: 0.4, ease: 'power2.out' });
      return next;
    });
  };
  const handlePrevProductCard = () => {
    if (!productCardsGridRef.current) return;
    const cardWidth = getCardWidth();
    const gridWrapperWidth = getGridWrapperWidth();
    const totalCards = productCards.length;
    const totalWidth = cardWidth * totalCards;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;

    setProductCardIndex((prev) => {
      let next = Math.max(prev - 1, 0);
      let targetX = -next * cardWidth;
      if (targetX < minX) targetX = minX;
      gsap.to(productCardsGridRef.current, { x: targetX, duration: 0.4, ease: 'power2.out' });
      return next;
    });
  };

  // Helper to get stats card width
  const getStatsCardWidth = () => {
    if (!statsCardsRowRef.current || !statsCardsRowRef.current.children[0]) return 0;
    return statsCardsRowRef.current.children[0].offsetWidth + 32; // 32px = gap (adjust if needed)
  };

  // Helper to get visible width of the stats grid wrapper
  const getStatsGridWrapperWidth = () => {
    if (!statsCardsRowRef.current || !statsCardsRowRef.current.parentElement) return 0;
    return statsCardsRowRef.current.parentElement.offsetWidth;
  };

  // Setup GSAP Draggable for stats cards
  useEffect(() => {
    if (!open || !statsCardsRowRef.current) return;
    if (statsDraggableInstance.current) {
      statsDraggableInstance.current[0].kill();
    }
    const cardWidth = getStatsCardWidth();
    const totalCards = statsCards.length;
    const gridWrapperWidth = getStatsGridWrapperWidth();
    const gap = 32; // px, same as in CSS
    const totalWidth = cardWidth * totalCards - gap;
    const maxX = 0;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    statsDraggableInstance.current = Draggable.create(statsCardsRowRef.current, {
      type: 'x',
      inertia: true,
      edgeResistance: 0.85,
      bounds: { minX, maxX },
      cursor: 'grab',
      activeCursor: 'grabbing',
      snap: (endValue) => {
        let idx = Math.round(endValue / -cardWidth);
        idx = Math.max(0, Math.min(idx, totalCards - 1));
        // If last card, snap exactly to minX
        if (idx === totalCards - 1) return minX;
        let snapped = -idx * cardWidth;
        if (snapped < minX) snapped = minX;
        return snapped;
      },
      onDragEnd: function() {
        const idx = Math.round(this.x / -cardWidth);
        setStatsCardIndex(Math.max(0, Math.min(idx, statsCards.length - 1)));
      },
      onThrowComplete: function() {
        const idx = Math.round(this.x / -cardWidth);
        setStatsCardIndex(Math.max(0, Math.min(idx, statsCards.length - 1)));
      }
    });
    // Start at first card (index 0)
    gsap.set(statsCardsRowRef.current, { x: 0 });
    setStatsCardIndex(0);
    // eslint-disable-next-line
  }, [open]);

  // Button navigation for stats cards
  const handleNextStatsCardSlider = () => {
    if (!statsCardsRowRef.current) return;
    const cardWidth = getStatsCardWidth();
    const gridWrapperWidth = getStatsGridWrapperWidth();
    const totalCards = statsCards.length;
    const gap = 32; // px, same as in CSS
    const totalWidth = cardWidth * totalCards - gap;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    setStatsCardIndex((prev) => {
      let next = Math.min(prev + 1, totalCards - 1);
      let targetX = -next * cardWidth;
      if (targetX < minX) targetX = minX;
      gsap.to(statsCardsRowRef.current, { x: targetX, duration: 0.4, ease: 'power2.out' });
      return next;
    });
  };
  const handlePrevStatsCardSlider = () => {
    if (!statsCardsRowRef.current) return;
    const cardWidth = getStatsCardWidth();
    const gridWrapperWidth = getStatsGridWrapperWidth();
    const totalCards = statsCards.length;
    const gap = 32; // px, same as in CSS
    const totalWidth = cardWidth * totalCards - gap;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    setStatsCardIndex((prev) => {
      let next = Math.max(prev - 1, 0);
      let targetX = -next * cardWidth;
      if (targetX < minX) targetX = minX;
      gsap.to(statsCardsRowRef.current, { x: targetX, duration: 0.4, ease: 'power2.out' });
      return next;
    });
  };

  // Helper to get xaas card width
  const getXaasCardWidth = () => {
    if (!xaasFeatureCardsRowRef.current || !xaasFeatureCardsRowRef.current.children[0]) return 0;
    return xaasFeatureCardsRowRef.current.children[0].offsetWidth;
  };

  // Helper to get xaas card gap
  const getXaasCardGap = () => {
    if (!xaasFeatureCardsRowRef.current || xaasFeatureCardsRowRef.current.children.length < 2) return 0;
    const first = xaasFeatureCardsRowRef.current.children[0];
    const second = xaasFeatureCardsRowRef.current.children[1];
    return second.offsetLeft - first.offsetLeft - first.offsetWidth;
  };

  // Helper to get visible width of the xaas grid wrapper
  const getXaasGridWrapperWidth = () => {
    if (!xaasFeatureCardsRowRef.current || !xaasFeatureCardsRowRef.current.parentElement) return 0;
    return xaasFeatureCardsRowRef.current.parentElement.offsetWidth;
  };

  // Setup GSAP Draggable for xaas feature cards
  useEffect(() => {
    if (!open || !xaasFeatureCardsRowRef.current) return;
    if (xaasDraggableInstance.current) {
      xaasDraggableInstance.current[0].kill();
    }
    const cardWidth = getXaasCardWidth();
    const gap = getXaasCardGap();
    const totalCards = 6; // Number of xaas feature cards
    const gridWrapperWidth = getXaasGridWrapperWidth();
    const totalWidth = (cardWidth * totalCards) + (gap * (totalCards - 1));
    const maxX = 0;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    xaasDraggableInstance.current = Draggable.create(xaasFeatureCardsRowRef.current, {
      type: 'x',
      inertia: true,
      edgeResistance: 0.85,
      bounds: { minX, maxX },
      cursor: 'grab',
      activeCursor: 'grabbing',
      snap: (endValue) => {
        let idx = Math.round(endValue / -(cardWidth + gap));
        idx = Math.max(0, Math.min(idx, totalCards - 1));
        if (idx === totalCards - 1) return minX;
        let snapped = -idx * (cardWidth + gap);
        if (snapped < minX) snapped = minX;
        return snapped;
      },
      onDragEnd: function() {
        const idx = Math.round(this.x / -(cardWidth + gap));
        setXaasCardIndex(Math.max(0, Math.min(idx, totalCards - 1)));
      },
      onThrowComplete: function() {
        const idx = Math.round(this.x / -(cardWidth + gap));
        setXaasCardIndex(Math.max(0, Math.min(idx, totalCards - 1)));
      }
    });
    // Start at first card (index 0)
    gsap.set(xaasFeatureCardsRowRef.current, { x: 0 });
    setXaasCardIndex(0);
    // eslint-disable-next-line
  }, [open]);

  // Button navigation for xaas feature cards
  const handleNextXaasCard = () => {
    if (!xaasFeatureCardsRowRef.current) return;
    const cardWidth = getXaasCardWidth();
    const gridWrapperWidth = getXaasGridWrapperWidth();
    const totalCards = 6;
    const gap = getXaasCardGap();
    const totalWidth = cardWidth * totalCards - gap;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    setXaasCardIndex((prev) => {
      let next = Math.min(prev + 1, totalCards - 1);
      let targetX = -next * cardWidth;
      if (targetX < minX) targetX = minX;
      gsap.to(xaasFeatureCardsRowRef.current, { x: targetX, duration: 0.4, ease: 'power2.out' });
      return next;
    });
  };
  const handlePrevXaasCard = () => {
    if (!xaasFeatureCardsRowRef.current) return;
    const cardWidth = getXaasCardWidth();
    const gridWrapperWidth = getXaasGridWrapperWidth();
    const totalCards = 6;
    const gap = getXaasCardGap();
    const totalWidth = cardWidth * totalCards - gap;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    setXaasCardIndex((prev) => {
      let next = Math.max(prev - 1, 0);
      let targetX = -next * cardWidth;
      if (targetX < minX) targetX = minX;
      gsap.to(xaasFeatureCardsRowRef.current, { x: targetX, duration: 0.4, ease: 'power2.out' });
      return next;
    });
  };

  // Helper to get prof card width
  const getProfCardWidth = () => {
    if (!profServicesFeatureCardsRowRef.current || !profServicesFeatureCardsRowRef.current.children[0]) return 0;
    return profServicesFeatureCardsRowRef.current.children[0].offsetWidth;
  };
  // Helper to get visible width of the prof grid wrapper
  const getProfGridWrapperWidth = () => {
    if (!profServicesFeatureCardsRowRef.current || !profServicesFeatureCardsRowRef.current.parentElement) return 0;
    return profServicesFeatureCardsRowRef.current.parentElement.offsetWidth;
  };
  // Setup GSAP Draggable for prof services feature cards
  useEffect(() => {
    if (!open || !profServicesFeatureCardsRowRef.current) return;
    if (profDraggableInstance.current) {
      profDraggableInstance.current[0].kill();
    }
    const cardWidth = getProfCardWidth();
    const gap = 40; // px, same as in CSS
    const totalCards = profServicesFeatureCardsRowRef.current.children.length;
    const gridWrapperWidth = getProfGridWrapperWidth();
    const totalWidth = cardWidth * totalCards + gap * (totalCards - 1);
    const maxX = 0;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    profDraggableInstance.current = Draggable.create(profServicesFeatureCardsRowRef.current, {
      type: 'x',
      inertia: true,
      edgeResistance: 0.85,
      bounds: { minX, maxX },
      cursor: 'grab',
      activeCursor: 'grabbing',
      snap: (endValue) => {
        let idx = Math.round(endValue / -(cardWidth + gap));
        idx = Math.max(0, Math.min(idx, totalCards - 1));
        if (idx === totalCards - 1) return minX;
        let snapped = -idx * (cardWidth + gap);
        if (snapped < minX) snapped = minX;
        return snapped;
      },
      onDragEnd: function() {
        const idx = Math.round(this.x / -(cardWidth + gap));
        setProfCardIndex(Math.max(0, Math.min(idx, totalCards - 1)));
      },
      onThrowComplete: function() {
        const idx = Math.round(this.x / -(cardWidth + gap));
        setProfCardIndex(Math.max(0, Math.min(idx, totalCards - 1)));
      }
    });
    // Start at first card (index 0)
    gsap.set(profServicesFeatureCardsRowRef.current, { x: 0 });
    setProfCardIndex(0);
    // eslint-disable-next-line
  }, [open]);
  // Button navigation for prof services feature cards
  const handleNextProfCard = () => {
    if (!profServicesFeatureCardsRowRef.current) return;
    const cardWidth = getProfCardWidth();
    const gap = 40; // px, same as in CSS
    const totalCards = profServicesFeatureCardsRowRef.current.children.length;
    const gridWrapperWidth = getProfGridWrapperWidth();
    const totalWidth = cardWidth * totalCards + gap * (totalCards - 1);
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    setProfCardIndex((prev) => {
      let next = Math.min(prev + 1, totalCards - 1);
      let targetX = -next * (cardWidth + gap);
      // If last card, snap to minX
      if (next === totalCards - 1) targetX = minX;
      if (targetX < minX) targetX = minX;
      gsap.to(profServicesFeatureCardsRowRef.current, { x: targetX, duration: 0.4, ease: 'power2.out' });
      return next;
    });
  };
  const handlePrevProfCard = () => {
    if (!profServicesFeatureCardsRowRef.current) return;
    const cardWidth = getProfCardWidth();
    const gap = 40; // px, same as in CSS
    const totalCards = profServicesFeatureCardsRowRef.current.children.length;
    const gridWrapperWidth = getProfGridWrapperWidth();
    const totalWidth = cardWidth * totalCards + gap * (totalCards - 1);
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    setProfCardIndex((prev) => {
      let next = Math.max(prev - 1, 0);
      let targetX = -next * (cardWidth + gap);
      // If last card, snap to minX
      if (next === totalCards - 1) targetX = minX;
      if (targetX < minX) targetX = minX;
      gsap.to(profServicesFeatureCardsRowRef.current, { x: targetX, duration: 0.4, ease: 'power2.out' });
      return next;
    });
  };

  const handleMenuClick = (section) => {
    setActiveSection(section);
    let ref = null;
    if (section === 'AI SOLUTIONS') ref = aiSolutionsRef;
    if (section === 'X-AS-A-SERVICE') ref = xaasRef;
    if (section === 'PROFESSIONAL SERVICES') ref = professionalRef;
    if (ref && ref.current) {
      scrollToSectionWithOffset(ref, 50);
    }
  };

  const getProfProcessCardWidth = () => {
    if (!profServicesProcessRowRef.current || !profServicesProcessRowRef.current.children[0]) return 0;
    return profServicesProcessRowRef.current.children[0].offsetWidth + 40; // 40px = gap (adjust if needed)
  };
  const getProfProcessGridWrapperWidth = () => {
    if (!profServicesProcessRowRef.current || !profServicesProcessRowRef.current.parentElement) return 0;
    return profServicesProcessRowRef.current.parentElement.offsetWidth;
  };

  useEffect(() => {
    if (!open || !profServicesProcessRowRef.current) return;
    if (profProcessDraggableInstance.current) {
      profProcessDraggableInstance.current[0].kill();
    }
    const cardWidth = getProfProcessCardWidth();
    const totalCards = profServicesProcessRowRef.current.children.length;
    const gridWrapperWidth = getProfProcessGridWrapperWidth();
    const gap = 40; // px, same as in CSS
    const totalWidth = cardWidth * totalCards - gap;
    const maxX = 0;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    profProcessDraggableInstance.current = Draggable.create(profServicesProcessRowRef.current, {
      type: 'x',
      inertia: true,
      edgeResistance: 0.85,
      bounds: { minX, maxX },
      cursor: 'grab',
      activeCursor: 'grabbing',
      snap: (endValue) => {
        let idx = Math.round(endValue / -cardWidth);
        idx = Math.max(0, Math.min(idx, totalCards - 1));
        if (idx === totalCards - 1) return minX;
        let snapped = -idx * cardWidth;
        if (snapped < minX) snapped = minX;
        return snapped;
      },
      onDragEnd: function() {
        const idx = Math.round(this.x / -cardWidth);
        setProfProcessCardIndex(Math.max(0, Math.min(idx, totalCards - 1)));
      },
      onThrowComplete: function() {
        const idx = Math.round(this.x / -cardWidth);
        setProfProcessCardIndex(Math.max(0, Math.min(idx, totalCards - 1)));
      }
    });
    gsap.set(profServicesProcessRowRef.current, { x: 0 });
    setProfProcessCardIndex(0);
    // eslint-disable-next-line
  }, [open]);

  const handleNextProfProcessCard = () => {
    if (!profServicesProcessRowRef.current) return;
    const cardWidth = getProfProcessCardWidth();
    const gridWrapperWidth = getProfProcessGridWrapperWidth();
    const totalCards = profServicesProcessRowRef.current.children.length;
    const gap = 40; // px, same as in CSS
    const totalWidth = cardWidth * totalCards - gap;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    setProfProcessCardIndex((prev) => {
      let next = Math.min(prev + 1, totalCards - 1);
      let targetX = -next * cardWidth;
      if (targetX < minX) targetX = minX;
      gsap.to(profServicesProcessRowRef.current, { x: targetX, duration: 0.4, ease: 'power2.out' });
      return next;
    });
  };
  const handlePrevProfProcessCard = () => {
    if (!profServicesProcessRowRef.current) return;
    const cardWidth = getProfProcessCardWidth();
    const gridWrapperWidth = getProfProcessGridWrapperWidth();
    const totalCards = profServicesProcessRowRef.current.children.length;
    const gap = 40; // px, same as in CSS
    const totalWidth = cardWidth * totalCards - gap;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    setProfProcessCardIndex((prev) => {
      let next = Math.max(prev - 1, 0);
      let targetX = -next * cardWidth;
      if (targetX < minX) targetX = minX;
      gsap.to(profServicesProcessRowRef.current, { x: targetX, duration: 0.4, ease: 'power2.out' });
      return next;
    });
  };

  const getProfFeatureCardBottomWidth = () => {
    if (!profFeatureCardBottomRowRef.current || !profFeatureCardBottomRowRef.current.children[0]) return 0;
    return profFeatureCardBottomRowRef.current.children[0].offsetWidth;
  };
  const getProfFeatureCardBottomGridWrapperWidth = () => {
    if (!profFeatureCardBottomRowRef.current || !profFeatureCardBottomRowRef.current.parentElement) return 0;
    return profFeatureCardBottomRowRef.current.parentElement.offsetWidth;
  };

  useEffect(() => {
    if (!open || !profFeatureCardBottomRowRef.current) return;
    if (profFeatureCardBottomDraggableInstance.current) {
      profFeatureCardBottomDraggableInstance.current[0].kill();
    }
    const cardWidth = getProfFeatureCardBottomWidth();
    const gap = 40; // px, same as in CSS
    const totalCards = 5;
    const gridWrapperWidth = getProfFeatureCardBottomGridWrapperWidth();
    const totalWidth = cardWidth * totalCards + gap * (totalCards - 1);
    const maxX = 0;
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    profFeatureCardBottomDraggableInstance.current = Draggable.create(profFeatureCardBottomRowRef.current, {
      type: 'x',
      inertia: true,
      edgeResistance: 0.85,
      bounds: { minX, maxX },
      cursor: 'grab',
      activeCursor: 'grabbing',
      snap: (endValue) => {
        let idx = Math.round(endValue / -(cardWidth + gap));
        idx = Math.max(0, Math.min(idx, totalCards - 1));
        if (idx === totalCards - 1) return minX;
        let snapped = -idx * (cardWidth + gap);
        if (snapped < minX) snapped = minX;
        return snapped;
      },
      onDragEnd: function() {
        const idx = Math.round(this.x / -(cardWidth + gap));
        setProfFeatureCardBottomIndex(Math.max(0, Math.min(idx, totalCards - 1)));
      },
      onThrowComplete: function() {
        const idx = Math.round(this.x / -(cardWidth + gap));
        setProfFeatureCardBottomIndex(Math.max(0, Math.min(idx, totalCards - 1)));
      }
    });
    gsap.set(profFeatureCardBottomRowRef.current, { x: 0 });
    setProfFeatureCardBottomIndex(0);
    // eslint-disable-next-line
  }, [open]);

  const handleNextProfFeatureCardBottom = () => {
    if (!profFeatureCardBottomRowRef.current) return;
    const cardWidth = getProfFeatureCardBottomWidth();
    const gap = 40; // px, same as in CSS
    const totalCards = 5;
    const gridWrapperWidth = getProfFeatureCardBottomGridWrapperWidth();
    const totalWidth = cardWidth * totalCards + gap * (totalCards - 1);
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    setProfFeatureCardBottomIndex((prev) => {
      let next = Math.min(prev + 1, totalCards - 1);
      let targetX = -next * (cardWidth + gap);
      if (next === totalCards - 1) targetX = minX;
      if (targetX < minX) targetX = minX;
      gsap.to(profFeatureCardBottomRowRef.current, { x: targetX, duration: 0.4, ease: 'power2.out' });
      return next;
    });
  };
  const handlePrevProfFeatureCardBottom = () => {
    if (!profFeatureCardBottomRowRef.current) return;
    const cardWidth = getProfFeatureCardBottomWidth();
    const gap = 40; // px, same as in CSS
    const totalCards = 5;
    const gridWrapperWidth = getProfFeatureCardBottomGridWrapperWidth();
    const totalWidth = cardWidth * totalCards + gap * (totalCards - 1);
    let minX = gridWrapperWidth - totalWidth;
    if (minX > 0) minX = 0;
    setProfFeatureCardBottomIndex((prev) => {
      let next = Math.max(prev - 1, 0);
      let targetX = -next * (cardWidth + gap);
      if (next === totalCards - 1) targetX = minX;
      if (targetX < minX) targetX = minX;
      gsap.to(profFeatureCardBottomRowRef.current, { x: targetX, duration: 0.4, ease: 'power2.out' });
      return next;
    });
  };

  // Add scroll-based active section detection
  useEffect(() => {
    if (!open) return;
    const scrollContainer = document.querySelector(`.${styles.contentRightScrollable}`);
    if (!scrollContainer) return;

    function onScroll() {
      const sections = [
        { key: 'AI SOLUTIONS', ref: aiSolutionsRef },
        { key: 'X-AS-A-SERVICE', ref: xaasRef },
        { key: 'PROFESSIONAL SERVICES', ref: professionalRef },
      ];
      const containerRect = scrollContainer.getBoundingClientRect();
      const threshold = 80; // px, how close heading should be to top
      // If KI-AS-A-SERVICE heading is above threshold (out of view at top), set AI SOLUTIONS as active
      if (xaasRef.current) {
        const xaasRect = xaasRef.current.getBoundingClientRect();
        if (xaasRect.top - containerRect.top > threshold) {
          if (activeSection !== 'AI SOLUTIONS') setActiveSection('AI SOLUTIONS');
          return;
        }
      }
      // Otherwise, use the last section whose heading is at or above the threshold
      let active = 'AI SOLUTIONS';
      for (let i = 0; i < sections.length; i++) {
        const section = sections[i];
        if (!section.ref.current) continue;
        const rect = section.ref.current.getBoundingClientRect();
        if (rect.top - containerRect.top <= threshold) {
          active = section.key;
        }
      }
      if (active !== activeSection) {
        setActiveSection(active);
      }
    }
    scrollContainer.addEventListener('scroll', onScroll, { passive: true });
    // Initial check
    onScroll();
    return () => {
      scrollContainer.removeEventListener('scroll', onScroll);
    };
    // eslint-disable-next-line
  }, [open]);

  useEffect(() => {
    if (open && initialSection) {
      setActiveSection(initialSection);
      let ref = null;
      if (initialSection === 'AI SOLUTIONS') ref = aiSolutionsRef;
      if (initialSection === 'X-AS-A-SERVICE') ref = xaasRef;
      if (initialSection === 'PROFESSIONAL SERVICES') ref = professionalRef;
      if (ref && ref.current) {
        scrollToSectionWithOffset(ref, 50);
      }
    }
    // eslint-disable-next-line
  }, [open, initialSection]);

  if (!open) return null;
  return (
    <div className={styles.overlay}>
      <button className={styles.closeButton} onClick={onClose} aria-label="Close Services Overlay">&times;</button>
      <div className={styles.layout}>
        <nav className={styles.menu}>
          <ul>
            <li
              className={activeSection === 'AI SOLUTIONS' ? styles.menuItemActive : styles.menuItem}
              onClick={() => handleMenuClick('AI SOLUTIONS')}
            >
              KI LÖSUNGEN
            </li>
            <li
              className={activeSection === 'X-AS-A-SERVICE' ? styles.menuItemActive : styles.menuItem}
              onClick={() => handleMenuClick('X-AS-A-SERVICE')}
            >
              KI-AS-A-SERVICE
            </li>
            <li
              className={activeSection === 'PROFESSIONAL SERVICES' ? styles.menuItemActive : styles.menuItem}
              onClick={() => handleMenuClick('PROFESSIONAL SERVICES')}
            >
              INDIVIDUELLE <br/>KI-LÖSUNGEN
            </li>
          </ul>
        </nav>
        <div className={styles.contentRightScrollable}>
          {/* AI SOLUTIONS SECTION */}
          <section ref={aiSolutionsRef} className={styles.sectionBlock}>
            <div className={styles.logoRow}>
              <img src="/Blcks_Icon_grey.svg" alt="AI SOLUTIONS" className={styles.logoIcon} />
              <span className={styles.logoText}>KI LÖSUNGEN</span>
            </div>
            <h1 className={styles.heading}>VERGISS STANDARD CHATBOTS UND ROBOTERGESTÜTZTE PROZESSAUTOMATISIERUNG. WILLKOMMEN IN DER WELT DER INTELLIGENTEN KI-AUTOMATISIERUNG.</h1>
            <p className={styles.paragraph}>
                Wir verbinden tiefes Plattformverständnis mit modernster KI-Technologie. Unsere Lösungen bieten maßgeschneiderte Datenabfragen, Tool-Integration, Speichersysteme und volle Kompatibilität mit großen und kleinen Sprachmodellen. In Zusammenarbeit mit führenden SaaS-Anbietern machen wir Systeme und Daten bereit für den Einsatz von KI – und heben das volle Potenzial für moderne Unternehmen.
            </p>
            <div className={styles.sectionNavSpacer} />
            <div className={styles.productCardsHeading}>UNSERE KI-LÖSUNGEN</div>
            <div className={styles.productCardsNavRow}>
              <button className={styles.productCardsNavBtn} onClick={handlePrevProductCard} aria-label="Vorherige Karte">
                <img src="/arrow-right-222.svg" alt="Left Arrow" style={{ transform: 'rotate(-90deg)', width: 16, height: 16, display: 'block' }} />
              </button>
              <button className={styles.productCardsNavBtn} onClick={handleNextProductCard} aria-label="Nächste Karte">
                <img src="/arrow-right-222.svg" alt="Right Arrow" style={{ transform: 'rotate(90deg)', width: 16, height: 16, display: 'block' }} />
              </button>
            </div>
            <div className={styles.productCardsGridWrapper}>
              <div className={styles.productCardsGrid} ref={productCardsGridRef}>
                {productCards.map((product, i) => (
                  <div key={i} className={styles.productCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.productCardIcon} />
                    <div className={styles.productCardHeading}>{product.heading}</div>
                    <div className={styles.productCardDesc}>{product.desc}</div>
                  </div>
                ))}
              </div>
            </div>
            <div className={styles.aiExpertHelpSection}>
              <h2 className={styles.aiExpertHelpHeadline}>
                Standardlösungen sind nicht genug. Unsere KI denkt weiter.
              </h2>
              <div className={styles.aiExpertHelpColumns}>
                <div className={styles.aiExpertHelpCol}>
                  <div className={styles.aiExpertHelpColTitle}>Prozessautomatisierung</div>
                  <div className={styles.aiExpertHelpColText}>
                    Automatisiere Abläufe plattformübergreifend – mit intelligenten Tools, die sich an deine Prozesse anpassen, nicht umgekehrt. Vereinfachte Workflows durch flexible API-Integrationen, individuell auf deine Systeme und Arbeitsweise zugeschnitten.
                  </div>
                </div>
                <div className={styles.aiExpertHelpCol}>
                  <div className={styles.aiExpertHelpColTitle}>Keine Lösung von der Stange</div>
                  <div className={styles.aiExpertHelpColText}>
                    Unsere KI-Lösungen werden nicht einfach „implementiert“, sondern präzise auf deine Daten, Systeme und Ziele abgestimmt. Statt generischer Modelle liefern wir intelligente Systeme, die dein Business wirklich verstehen – und messbar verbessern.
                  </div>
                </div>
                <div className={styles.aiExpertHelpCol}>
                  <div className={styles.aiExpertHelpColTitle}>Echte Integration, kein Add-on</div>
                  <div className={styles.aiExpertHelpColText}>
                    Unsere Technologie wird nicht außen draufgesetzt – sie wird Teil deiner Prozesse. Durch nahtlose Anbindung an bestehende Tools und Workflows entsteht kein zusätzlicher Pflegeaufwand, sondern ein nachhaltiger Produktivitätsgewinn.
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.statsCardsSection}>
              <div className={styles.statsCardsNavRow}>
                <button className={styles.statsCardsNavBtn} onClick={handlePrevStatsCardSlider} aria-label="Previous Stat Card">
                  <img src="/arrow-right.svg" alt="Left Arrow" style={{ transform: 'rotate(-90deg)', width: 16, height: 16, display: 'block' }} />
                </button>
                <button className={styles.statsCardsNavBtn} onClick={handleNextStatsCardSlider} aria-label="Next Stat Card">
                  <img src="/arrow-right.svg" alt="Right Arrow" style={{ transform: 'rotate(90deg)', width: 16, height: 16, display: 'block' }} />
                </button>
              </div>
              <div className={styles.statsCardsRowWrapper}>
                <div className={styles.statsCardsRow} ref={statsCardsRowRef}>
                  {statsCards.map((card, i) => (
                    <div key={i} className={styles.statsCard}>
                      <div className={styles.statsCardFlexRow}>
                        <div className={styles.statsCardLeftCol}>
                          <div className={styles.statsCardPercentRow}>
                            <span className={styles.statsCardPercent}>{card.percent}</span>
                            <span className={styles.statsCardPercentSymbol}>%</span>
                          </div>
                          <div className={styles.statsCardBottomGroup}>
                            <div className={styles.statsCardDesc}>{card.desc}</div>
                            <div className={styles.statsCardSource}>{card.source}</div>
                          </div>
                        </div>
                        <div className={styles.statsCardRightCol}>
                          {card.visual}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>
          {/* X-AS-A-SERVICE SECTION */}
          <div className={styles.horizontalDivider} />
          <section ref={xaasRef} className={styles.sectionBlock}>
            <div className={styles.logoRow}>
              <img src="/Blcks_Icon.svg" alt="X-AS-A-SERVICE" className={styles.logoIcon} />
              <span className={styles.logoText}>KI-AS-A-SERVICE</span>
            </div>
            <h1 className={styles.heading}>DER WEG ZUR KI.<br />JETZT SCHNELLER.<br />JETZT SMARTER.</h1>
            <div className={styles.xaasColumnsWrapper}>
              <div className={styles.xaasCol}>
                <div className={styles.xaasColTitle}>On-Demand</div>
                <div className={styles.xaasColText}>
                    Zahle nur für das, was du wirklich nutzt. Spare dir teure Spezialisten oder ungenutzte Ressourcen im Jahresbetrieb – skaliere flexibel mit deinem Bedarf.
                </div>
              </div>
              <div className={styles.xaasCol}>
                <div className={styles.xaasColTitle}>AI-unterstützte Services & Tools</div>
                <div className={styles.xaasColText}>
                    Mehr als nur Managed Services: Wir bieten dir ein wachsendes Portfolio an KI-basierten Modulen für deine Business-Apps – auch für individuelle oder nicht-standardisierte Anforderungen.
                </div>
              </div>
              <div className={styles.xaasCol}>
                <div className={styles.xaasColTitle}>Konfiguration & Rollout inklusive</div>
                <div className={styles.xaasColText}>
                    Wir übernehmen die Einrichtung und Anbindung der KI-Lösungen – individuell an deine Prozesse angepasst, ohne Overhead.
                </div>
              </div>
            </div>
            <div className={styles.xaasFeatureCardsSection}>
              <div className={styles.xaasFeatureCardsNavRow}>
                <button className={styles.xaasFeatureCardsNavBtn} onClick={handlePrevXaasCard} aria-label="Vorherige Karte">
                  <img src="/arrow-right-222.svg" alt="Left Arrow" style={{ transform: 'rotate(-90deg)', width: 16, height: 16, display: 'block' }} />
                </button>
                <button className={styles.xaasFeatureCardsNavBtn} onClick={handleNextXaasCard} aria-label="Nächste Karte">
                  <img src="/arrow-right-222.svg" alt="Right Arrow" style={{ transform: 'rotate(90deg)', width: 16, height: 16, display: 'block' }} />
                </button>
              </div>
              <div className={styles.xaasFeatureCardsRowWrapper}>
                <div className={styles.xaasFeatureCardsRow} ref={xaasFeatureCardsRowRef}>
                  {/* Card 1 */}
                  <div className={styles.xaasFeatureCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.productCardIcon} />
                    <div className={styles.xaasFeatureCardContent}>
                      <div className={styles.xaasFeatureCardTitle}>KI-Support-Modul<br />für CRM-Systeme</div>
                      <div className={styles.xaasFeatureCardDesc}>Automatisiert die Kommunikation, Kundenpflege & Follow-ups – z. B. für HubSpot, Pipedrive, Zoho, Salesforce.</div>
                      <ul className={styles.xaasFeatureCardList}>
                        <li>GPT-gestützte Mail- & Chat-Antworten</li>
                        <li>Kundenfeedback-Auswertung</li>
                        <li>Erinnerungen & Next-Best-Actions</li>
                      </ul>
                    </div>
                  </div>
                  {/* Card 2 */}
                  <div className={styles.xaasFeatureCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.productCardIcon} />
                    <div className={styles.xaasFeatureCardContent}>
                      <div className={styles.xaasFeatureCardTitle}>KI-Content-Service<br />on Demand</div>
                      <div className={styles.xaasFeatureCardDesc}>Inhalte auf Knopfdruck: Social-Media-Posts, Blogartikel, LinkedIn-Beiträge oder Produktbeschreibungen.</div>
                      <ul className={styles.xaasFeatureCardList}>
                        <li>Mehrsprachig & markenkonform</li>
                        <li>Automatisierte Planung & Distribution</li>
                        <li>SEO-freundlich</li>
                      </ul>
                    </div>
                  </div>
                  {/* Card 3 */}
                  <div className={styles.xaasFeatureCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.productCardIcon} />
                    <div className={styles.xaasFeatureCardContent}>
                      <div className={styles.xaasFeatureCardTitle}>On-Demand<br />KI-Meeting-Notizen</div>
                      <div className={styles.xaasFeatureCardDesc}>Transkription, Zusammenfassung & To-dos per KI – z. B. aus Zoom, MS Teams oder Google Meet.</div>
                      <ul className={styles.xaasFeatureCardList}>
                        <li>PDF-/Markdown-Ausgabe</li>
                        <li>Aufgaben-Extraktion</li>
                        <li>Mail-Export an Team</li>
                      </ul>
                    </div>
                  </div>
                  {/* Card 4 */}
                  <div className={styles.xaasFeatureCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.productCardIcon} />
                    <div className={styles.xaasFeatureCardContent}>
                      <div className={styles.xaasFeatureCardTitle}>KI-Leadrecherche<br />as-a-Service</div>
                      <div className={styles.xaasFeatureCardDesc}>Lass KI gezielt Leads suchen, sortieren und anreichern (z. B. für B2B-Vertrieb).</div>
                      <ul className={styles.xaasFeatureCardList}>
                        <li>LinkedIn- & Web-Recherche</li>
                        <li>GPT-generierte Pitch-Vorschläge</li>
                        <li>Kombination mit Outreach möglich</li>
                      </ul>
                    </div>
                  </div>
                  {/* Card 5 */}
                  <div className={styles.xaasFeatureCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.productCardIcon} />
                    <div className={styles.xaasFeatureCardContent}>
                      <div className={styles.xaasFeatureCardTitle}>Automated KI-SEO</div>
                      <div className={styles.xaasFeatureCardDesc}>Automatisierte SEO-Inhalte & -Analysen für Websites, Blogposts und Landingpages – ohne manuelles Keyword-Stochern.</div>
                      <ul className={styles.xaasFeatureCardList}>
                        <li>Keyword-Analyse & Content-Strategie</li>
                        <li>Erstellung von SEO-optimierten Texten inkl. Meta-Tags</li>
                        <li>Automatisierte SEO Wettbewerbsanalyse</li>
                      </ul>
                    </div>
                  </div>
                  {/* Card 6 */}
                  <div className={styles.xaasFeatureCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.productCardIcon} />
                    <div className={styles.xaasFeatureCardContent}>
                      <div className={styles.xaasFeatureCardTitle}>KI Image &<br />Video Ad Generator</div>
                      <div className={styles.xaasFeatureCardDesc}>Erstelle mit wenigen Inputs ansprechende Werbeanzeigen – komplett automatisiert für Meta, TikTok, Google & Co.</div>
                      <ul className={styles.xaasFeatureCardList}>
                        <li>Auto-generierte Image Ads (inkl. Mockups, Texten, Formaten)</li>
                        <li>Video Ad Generation (aus bestehenden Assets, Slides, Script etc.)</li>
                        <li>Zielgruppenspezifisches Copywriting</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.xaasLargeTextSection}>
                Wir unterstützen moderne SaaS-Ökosysteme mit smarten, KI-basierten Services. Wie unsere Kunden arbeiten auch wir mit den führenden Business-Plattformen – und wissen genau, wie man ihren vollen Mehrwert durch KI erschließt. Mit unseren modularen KI-as-a-Service-Lösungen integrieren wir intelligente Automatisierung direkt in deine bestehenden Tools und Prozesse – schnell, skalierbar und ohne Umwege.
            </div>
            {/*
            <div className={styles.xaasPlansSection}>
              <div className={styles.xaasPlansHeaderRow}>
                <h2 className={styles.xaasPlansTitle}>KI-as-a-Service Pläne</h2>
                <div className={styles.xaasPlansSubtitle}>Für alle gemacht. Ohne Kompromisse.</div>
              </div>
              <div className={styles.xaasPlansCardsRow}>
                <!-- On-demand card and all plan cards are commented out here -->
              </div>
            </div>
            */}
          </section>
          {/* PROFESSIONAL SERVICES SECTION */}
          <div className={styles.horizontalDivider} />
          <section ref={professionalRef} className={styles.sectionBlock}>
            <div className={styles.logoRow}>
              <img src="/Blcks_Icon.svg" alt="PROFESSIONAL SERVICES" className={styles.logoIcon} />
              <span className={styles.logoText}>INDIVIDUELLE KI-LÖSUNGEN</span>
            </div>
            <h1 className={styles.profServicesHeading}>
              INDIVIDUELLE KI-LÖSUNGEN,<br />
              EXAKT AUF DEIN BUSINESS<br />
              ZUGESCHNITTEN. <br />
              WEIL STANDARD NICHT REICHT, WENN DU ECHTE EFFIZIENZ WILLST.
            </h1>
            <div className={styles.profServicesSubheading}>INDIVIDUELLE LÖSUNGEN FÜR DEIN BUSINESS:</div>
            <div className={styles.profServicesFeatureCardsSection}>
              <div className={styles.profServicesFeatureCardsNavRow}>
                <button className={styles.productCardsNavBtn} onClick={handlePrevProfCard} aria-label="Vorherige Karte">
                  <img src="/arrow-right-222.svg" alt="Left Arrow" style={{ transform: 'rotate(-90deg)', width: 16, height: 16, display: 'block' }} />
                </button>
                <button className={styles.productCardsNavBtn} onClick={handleNextProfCard} aria-label="Nächste Karte">
                  <img src="/arrow-right-222.svg" alt="Right Arrow" style={{ transform: 'rotate(90deg)', width: 16, height: 16, display: 'block' }} />
                </button>
              </div>
              <div className={styles.profServicesFeatureCardsRowWrapper}>
                <div className={styles.profServicesFeatureCardsRow} ref={profServicesFeatureCardsRowRef}>
                  {/* Card 1 */}
                  <div className={styles.profFeatureCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.profFeatureCardIcon} />
                    <div className={styles.profFeatureCardTitle}>KI Agent mit Wissensdatenbank + Tool-Integration (z. B. für Support & Sales)</div>
                    <ul className={styles.profFeatureCardList}>
                      <li>Erstellung eines RAG-basierten AI-Agenten (Retrieval-Augmented Generation)</li>
                      <li>Anbindung an Tools wie HubSpot, Slack, CRM, Support-Systeme</li>
                      <li>Tool-Calling: z. B. automatisiert Tickets erstellen, E-Mails verschicken, Kalender verwalten</li>
                      <li>Live-Chat oder Intranet-Integration</li>
                    </ul>
                  </div>
                  {/* Card 2 */}
                  <div className={styles.profFeatureCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.profFeatureCardIcon} />
                    <div className={styles.profFeatureCardTitle}>Automatisiertes Angebots- & Anfrage-System (z. B. für Agenturen oder Fertigung)</div>
                    <ul className={styles.profFeatureCardList}>
                      <li>Kunden stellen freie Anfragen → AI analysiert, generiert passende Angebotsstruktur</li>
                      <li>Automatische Preisberechnung aus ERP oder Excel-Tabellen</li>
                      <li>Versand via E-Mail oder PDF-Generator</li>
                      <li>Optional: Integration mit Vertriebsteam zur Nachverfolgung</li>
                    </ul>
                  </div>
                  {/* Card 3 */}
                  <div className={styles.profFeatureCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.profFeatureCardIcon} />
                    <div className={styles.profFeatureCardTitle}>Custom KI-Dashboard zur internen Prozessoptimierung</div>
                    <ul className={styles.profFeatureCardList}>
                      <li>KI analysiert interne Dokumente, Projektstatus, Workflows</li>
                      <li>Alerts & Empfehlungen: "Dieser Schritt kann automatisiert werden"</li>
                      <li>Optional: native Integration mit Notion, Monday.com, Asana etc.</li>
                      <li>Ziel: interne Effizienzsteigerung & weniger manuelle Eingriffe</li>
                    </ul>
                  </div>
                  {/* Card 4 */}
                  <div className={styles.profFeatureCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.profFeatureCardIcon} />
                    <div className={styles.profFeatureCardTitle}>AI Knowledge Hub mit semantischer Suche (z. B. für Recht, Pharma, Beratung)</div>
                    <ul className={styles.profFeatureCardList}>
                      <li>Aufbau eines AI-gestützten Suchsystems für interne PDFs, Verträge, Berichte etc.</li>
                      <li>Semantische Suche + Konversationsschnittstelle</li>
                      <li>DSGVO-konforme Datenhaltung + Zugriffskontrollen</li>
                      <li>Einbindung in bestehende Dokumenten-Systeme</li>
                    </ul>
                  </div>
                  {/* Card 5 */}
                  <div className={styles.profFeatureCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.profFeatureCardIcon} />
                    <div className={styles.profFeatureCardTitle}>Custom Lead Qualifier Bot (für Sales-Teams mit hohem Anfragevolumen)</div>
                    <ul className={styles.profFeatureCardList}>
                      <li>AI bot prüft eingehende Leads automatisch: Bedarf, Budget, Timing</li>
                      <li>Übergabe an CRM oder Sales Pipeline (z. B. Pipedrive, Salesforce)</li>
                      <li>Scoring-Logik via kontextuelle Analyse von Nachrichten & Formularen</li>
                      <li>Optional: Follow-up-E-Mails + Terminvereinbarung</li>
                    </ul>
                  </div>
                  {/* Card 6 */}
                  <div className={styles.profFeatureCard}>
                    <img src="/Blcks_Icon_grey.svg" alt="Card Icon" className={styles.profFeatureCardIcon} />
                    <div className={styles.profFeatureCardTitle}>AI-Personalisierung für interne Weiterbildung / Wissensvermittlung</div>
                    <ul className={styles.profFeatureCardList}>
                      <li>AI lernt Unternehmenswissen & baut individuelle Lernpfade</li>
                      <li>Personalisierte Trainingsfragen & Onboarding-Pfade</li>
                      <li>Fortschrittsüberwachung + automatisiertes Feedback</li>
                      <li>Integration mit bestehenden LMS-Systemen (z. B. Moodle, TalentLMS)</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.profServicesLargeText}>
                Die digitale Transformation von heute stellt KI und agentische Workflows in den Mittelpunkt. Um KI für Unternehmen bereitzustellen, bietet BLCKS den ersten integrierten Ansatz – wir arbeiten partnerschaftlich mit unseren Kunden zusammen und unterstützen deren Plattformen auch nach der Implementierung durch unsere KI-gestützte Support-Plattform und erfahrene Beratungsexperten. So setzen wir den neuen Maßstab für Erfolg.
            </div>
            <div className={styles.profFeatureCardBottomNavRow}>
              <button className={styles.profFeatureCardBottomNavBtn} onClick={handlePrevProfFeatureCardBottom} aria-label="Vorherige Karte">
                <img src="/arrow-right.svg" alt="Left Arrow" style={{ transform: 'rotate(-90deg)', width: 16, height: 16, display: 'block' }} />
              </button>
              <button className={styles.profFeatureCardBottomNavBtn} onClick={handleNextProfFeatureCardBottom} aria-label="Nächste Karte">
                <img src="/arrow-right.svg" alt="Right Arrow" style={{ transform: 'rotate(90deg)', width: 16, height: 16, display: 'block' }} />
              </button>
            </div>
            <div className={styles.profFeatureCardBottomRowWrapper}>
              <div className={styles.profFeatureCardBottomRow} ref={profFeatureCardBottomRowRef}>
                {/* Card 1 */}
                <div className={styles.profFeatureCardBottom}>
                  <div className={styles.profFeatureCardBottomHeader}>
                    <span className={styles.profFeatureCardBottomStepNum}>01</span>
                    <span className={styles.profFeatureCardBottomTitle}>START</span>
                  </div>
                  <div className={styles.profFeatureCardBottomPhasesRow}>
                    <div>
                      <span className={styles.profFeatureCardBottomPhase}><b>Phase 01: Start</b></span><br />
                      <span className={styles.profFeatureCardBottomPhaseSub}>Kickoff &amp; Planung</span>
                    </div>
                    <div>
                      <span className={styles.profFeatureCardBottomPhase}><b>Phase 02: Erkunden</b></span><br />
                      <span className={styles.profFeatureCardBottomPhaseSub}>Anforderungen definieren &amp; Chancen erkennen</span>
                    </div>
                  </div>
                  <div className={styles.profFeatureCardBottomBarRow}>
                    <img src="/step-chart-1.svg" alt="Progress Bar" style={{ width: '100%' }} />
                  </div>
                </div>
                {/* Card 2 */}
                <div className={styles.profFeatureCardBottom}>
                  <div className={styles.profFeatureCardBottomHeader}>
                    <span className={styles.profFeatureCardBottomStepNum}>02</span>
                    <span className={styles.profFeatureCardBottomTitle}>ENTWICKELN</span>
                  </div>
                  <div className={styles.profFeatureCardBottomPhasesRow}>
                    <div>
                      <span className={styles.profFeatureCardBottomPhase}><b>Phase 03: Konzipieren</b></span><br />
                      <span className={styles.profFeatureCardBottomPhaseSub}>Lösungsentwurf &amp; Planung</span>
                    </div>
                    <div>
                      <span className={styles.profFeatureCardBottomPhase}><b>Phase 04: Umsetzen</b></span><br />
                      <span className={styles.profFeatureCardBottomPhaseSub}>Konfiguration &amp; Entwicklung</span>
                    </div>
                  </div>
                  <div className={styles.profFeatureCardBottomBarRow}>
                    <img src="/step-chart-2.svg" alt="Progress Bar" style={{ width: '100%' }} />
                  </div>
                </div>
                {/* Card 3 */}
                <div className={styles.profFeatureCardBottom}>
                  <div className={styles.profFeatureCardBottomHeader}>
                    <span className={styles.profFeatureCardBottomStepNum}>03</span>
                    <span className={styles.profFeatureCardBottomTitle}>VALIDIEREN</span>
                  </div>
                  <div className={styles.profFeatureCardBottomPhasesRow}>
                    <div>
                      <span className={styles.profFeatureCardBottomPhase}><b>Phase 05: Testen</b></span><br />
                      <span className={styles.profFeatureCardBottomPhaseSub}>Testphase</span>
                    </div>
                    <div>
                      <span className={styles.profFeatureCardBottomPhase}><b>Phase 06: Verifizieren</b></span><br />
                      <span className={styles.profFeatureCardBottomPhaseSub}>Systemprüfung &amp; Qualitätskontrolle</span>
                    </div>
                    <div>
                      <span className={styles.profFeatureCardBottomPhase}><b>Phase 07: Freigeben</b></span><br />
                      <span className={styles.profFeatureCardBottomPhaseSub}>Abnahme &amp; finale Validierung</span>
                    </div>
                  </div>
                  <div className={styles.profFeatureCardBottomBarRow}>
                    <img src="/step-chart-3.svg" alt="Progress Bar" style={{ width: '100%' }} />
                  </div>
                </div>
                {/* Card 4 */}
                <div className={styles.profFeatureCardBottom}>
                  <div className={styles.profFeatureCardBottomHeader}>
                    <span className={styles.profFeatureCardBottomStepNum}>04</span>
                    <span className={styles.profFeatureCardBottomTitle}>LAUNCH</span>
                  </div>
                  <div className={styles.profFeatureCardBottomPhasesRow}>
                    <div>
                      <span className={styles.profFeatureCardBottomPhase}><b>Phase 08: Ausrollen</b></span><br />
                      <span className={styles.profFeatureCardBottomPhaseSub}>Go-Live &amp; Produktivsetzung</span>
                    </div>
                    <div>
                      <span className={styles.profFeatureCardBottomPhase}><b>Phase 09: Messen</b></span><br />
                      <span className={styles.profFeatureCardBottomPhaseSub}>ROI-Analyse &amp; KPI-Tracking</span>
                    </div>
                  </div>
                  <div className={styles.profFeatureCardBottomBarRow}>
                    <img src="/step-chart-4.svg" alt="Progress Bar" style={{ width: '100%' }} />
                  </div>
                </div>
                {/* Card 5 */}
                <div className={styles.profFeatureCardBottom}>
                  <div className={styles.profFeatureCardBottomHeader}>
                    <span className={styles.profFeatureCardBottomStepNum}>05</span>
                    <span className={styles.profFeatureCardBottomTitle}>OPTIMIEREN</span>
                  </div>
                  <div className={styles.profFeatureCardBottomPhasesRow}>
                    <div>
                      <span className={styles.profFeatureCardBottomPhase}><b>Phase 10: Support</b></span><br />
                      <span className={styles.profFeatureCardBottomPhaseSub}>Support &amp; laufende Betreuung</span>
                    </div>
                    <div>
                      <span className={styles.profFeatureCardBottomPhase}><b>Phase 11: Optimieren</b></span><br />
                      <span className={styles.profFeatureCardBottomPhaseSub}>Strategische Weiterentwicklung</span>
                    </div>
                  </div>
                  <div className={styles.profFeatureCardBottomBarRow}>
                    <img src="/step-chart-5.svg" alt="Progress Bar" style={{ width: '100%' }} />
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
} 