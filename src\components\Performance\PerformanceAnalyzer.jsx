import React, { useRef, useState, useEffect } from 'react';
import { useControls } from 'leva';

export default function PerformanceAnalyzer() {
  const [performanceData, setPerformanceData] = useState({
    fps: 0,
    frameTime: 0,
    memoryUsage: 0,
    drawCalls: 0,
    triangles: 0,
    geometries: 0,
    textures: 0,
    programs: 0,
    loadingPhases: {
      modelsLoaded: false,
      texturesLoaded: false,
      shadersCompiled: false,
      totalLoadTime: 0
    },
    bottlenecks: []
  });
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const startTime = useRef(performance.now());
  const frameTimeHistory = useRef([]);
  const maxHistoryLength = 120; // 2 seconds at 60fps

  // Performance thresholds
  const PERFORMANCE_THRESHOLDS = {
    fps: { good: 55, warning: 45, critical: 30 },
    frameTime: { good: 16.67, warning: 22, critical: 33.33 }, // ms
    drawCalls: { good: 50, warning: 100, critical: 200 },
    triangles: { good: 50000, warning: 100000, critical: 200000 },
    memory: { good: 100, warning: 200, critical: 400 } // MB
  };

  const {
    showAnalyzer,
    showBottlenecks,
    showLoadingAnalysis,
    autoOptimize
  } = useControls('Performance Analyzer', {
    showAnalyzer: true,
    showBottlenecks: true,
    showLoadingAnalysis: true,
    autoOptimize: false
  });

  // Performance monitoring with setInterval instead of useFrame
  useEffect(() => {
    if (!showAnalyzer) return;

    const interval = setInterval(() => {
      frameCount.current++;
      const currentTime = performance.now();

      // Update every 30 frames equivalent (0.5 seconds)
      if (frameCount.current % 30 === 0) {
        const deltaTime = currentTime - lastTime.current;
        const fps = Math.round(30000 / deltaTime);
        const frameTime = Math.round(deltaTime / 30 * 100) / 100;

        // Track frame time history
        frameTimeHistory.current.push(frameTime);
        if (frameTimeHistory.current.length > maxHistoryLength) {
          frameTimeHistory.current.shift();
        }

        // Get memory info
        const memory = performance.memory || {};

        const stats = {
          fps: fps || 0,
          frameTime: frameTime || 0,
          memoryUsage: memory.usedJSHeapSize ? Math.round(memory.usedJSHeapSize / 1024 / 1024) : 0,
          drawCalls: 0, // Will be updated by Three.js context if available
          triangles: 0, // Will be updated by Three.js context if available
          geometries: 0,
          textures: 0,
          programs: 0
        };

        const sceneStats = {
          meshCount: 0,
          lightCount: 0,
          instancedMeshCount: 0,
          geometryCount: 0,
          materialCount: 0,
          totalVertices: 0
        };

        const bottlenecks = identifyBottlenecks(stats, sceneStats);

        setPerformanceData({
          ...stats,
          sceneStats,
          bottlenecks,
          loadingPhases: {
            modelsLoaded: false,
            texturesLoaded: false,
            shadersCompiled: false,
            totalLoadTime: Math.round((currentTime - startTime.current) / 1000 * 100) / 100
          }
        });

        lastTime.current = currentTime;
      }
    }, 16); // ~60fps monitoring

    return () => clearInterval(interval);
  }, [showAnalyzer, performanceData.loadingPhases]);

  // Simplified scene analysis without Three.js context
  const analyzeScene = () => {
    return {
      meshCount: 0,
      instancedMeshCount: 0,
      geometryCount: 0,
      materialCount: 0,
      lightCount: 0,
      totalVertices: 0
    };
  };

  // Identify performance bottlenecks
  const identifyBottlenecks = (stats, sceneStats) => {
    const bottlenecks = [];

    // Safely check values with fallbacks
    const fps = stats.fps || 0;
    const drawCalls = stats.drawCalls || 0;
    const triangles = stats.triangles || 0;
    const memoryUsage = stats.memoryUsage || 0;
    const meshCount = sceneStats?.meshCount || 0;
    const lightCount = sceneStats?.lightCount || 0;

    if (fps > 0 && fps < PERFORMANCE_THRESHOLDS.fps.warning) {
      bottlenecks.push({
        type: 'fps',
        severity: fps < PERFORMANCE_THRESHOLDS.fps.critical ? 'critical' : 'warning',
        message: `Low FPS: ${fps} (target: 60)`,
        suggestions: [
          'Reduce polygon count in models',
          'Optimize textures (use compressed formats)',
          'Reduce number of lights',
          'Use LOD (Level of Detail) for distant objects'
        ]
      });
    }

    if (drawCalls > PERFORMANCE_THRESHOLDS.drawCalls.warning) {
      bottlenecks.push({
        type: 'drawCalls',
        severity: drawCalls > PERFORMANCE_THRESHOLDS.drawCalls.critical ? 'critical' : 'warning',
        message: `High draw calls: ${drawCalls}`,
        suggestions: [
          'Use instanced rendering for repeated objects',
          'Merge geometries where possible',
          'Use texture atlases to reduce material switches'
        ]
      });
    }

    if (triangles > PERFORMANCE_THRESHOLDS.triangles.warning) {
      bottlenecks.push({
        type: 'triangles',
        severity: triangles > PERFORMANCE_THRESHOLDS.triangles.critical ? 'critical' : 'warning',
        message: `High triangle count: ${triangles.toLocaleString()}`,
        suggestions: [
          'Use DRACO compression for models',
          'Implement LOD system',
          'Use simpler geometries for distant objects'
        ]
      });
    }

    if (memoryUsage > PERFORMANCE_THRESHOLDS.memory.warning) {
      bottlenecks.push({
        type: 'memory',
        severity: memoryUsage > PERFORMANCE_THRESHOLDS.memory.critical ? 'critical' : 'warning',
        message: `High memory usage: ${memoryUsage}MB`,
        suggestions: [
          'Use KTX2 compressed textures',
          'Reduce texture resolution',
          'Dispose unused geometries and materials',
          'Use texture streaming for large textures'
        ]
      });
    }

    // Scene-specific bottlenecks
    if (meshCount > 100) {
      bottlenecks.push({
        type: 'meshCount',
        severity: 'warning',
        message: `High mesh count: ${meshCount}`,
        suggestions: [
          'Use instanced meshes for repeated objects',
          'Merge static geometries'
        ]
      });
    }

    if (lightCount > 8) {
      bottlenecks.push({
        type: 'lights',
        severity: 'warning',
        message: `Too many lights: ${lightCount}`,
        suggestions: [
          'Use fewer dynamic lights',
          'Bake lighting where possible',
          'Use light probes for ambient lighting'
        ]
      });
    }

    return bottlenecks;
  };

  // useFrame removed - performance monitoring now handled by useEffect with setInterval

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return '#ff4444';
      case 'warning': return '#ffaa44';
      default: return '#44ff44';
    }
  };

  const getPerformanceColor = (value, thresholds) => {
    if (value <= thresholds.good) return '#44ff44';
    if (value <= thresholds.warning) return '#ffaa44';
    return '#ff4444';
  };

  if (!showAnalyzer) return null;

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      left: '50%',
      transform: 'translateX(-50%)',
      background: 'rgba(0, 0, 0, 0.9)',
      color: 'white',
      padding: '15px',
      borderRadius: '8px',
      fontFamily: 'monospace',
      fontSize: '11px',
      zIndex: 1000,
      minWidth: '400px',
      maxWidth: '600px',
      maxHeight: '80vh',
      overflow: 'auto'
    }}>
      <div style={{ color: '#44aaff', marginBottom: '10px', fontSize: '14px', fontWeight: 'bold' }}>
        🔍 Performance Analyzer
      </div>

      {/* Core Performance Metrics */}
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '10px', marginBottom: '15px' }}>
        <div>
          <div style={{ color: getPerformanceColor(performanceData.fps, PERFORMANCE_THRESHOLDS.fps) }}>
            FPS: {performanceData.fps}
          </div>
          <div style={{ color: getPerformanceColor(performanceData.frameTime, PERFORMANCE_THRESHOLDS.frameTime) }}>
            Frame: {performanceData.frameTime}ms
          </div>
        </div>
        <div>
          <div style={{ color: getPerformanceColor(performanceData.drawCalls, PERFORMANCE_THRESHOLDS.drawCalls) }}>
            Draws: {performanceData.drawCalls}
          </div>
          <div style={{ color: getPerformanceColor(performanceData.triangles, PERFORMANCE_THRESHOLDS.triangles) }}>
            Tris: {(performanceData.triangles || 0).toLocaleString()}
          </div>
        </div>
        <div>
          <div style={{ color: getPerformanceColor(performanceData.memoryUsage, PERFORMANCE_THRESHOLDS.memory) }}>
            Memory: {performanceData.memoryUsage}MB
          </div>
          <div>Load: {performanceData.loadingPhases.totalLoadTime}s</div>
        </div>
      </div>

      {/* Scene Statistics */}
      {performanceData.sceneStats && (
        <div style={{ marginBottom: '15px', padding: '8px', background: 'rgba(255,255,255,0.1)', borderRadius: '4px' }}>
          <div style={{ color: '#aaaaff', marginBottom: '5px' }}>Scene Stats:</div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '5px', fontSize: '10px' }}>
            <div>Meshes: {performanceData.sceneStats.meshCount || 0}</div>
            <div>Instanced: {performanceData.sceneStats.instancedMeshCount || 0}</div>
            <div>Geometries: {performanceData.sceneStats.geometryCount || 0}</div>
            <div>Materials: {performanceData.sceneStats.materialCount || 0}</div>
            <div>Lights: {performanceData.sceneStats.lightCount || 0}</div>
            <div>Vertices: {(performanceData.sceneStats.totalVertices || 0).toLocaleString()}</div>
          </div>
        </div>
      )}

      {/* Bottlenecks */}
      {showBottlenecks && performanceData.bottlenecks.length > 0 && (
        <div style={{ marginBottom: '15px' }}>
          <div style={{ color: '#ffaa44', marginBottom: '8px', fontSize: '12px' }}>
            ⚠️ Performance Issues ({performanceData.bottlenecks.length}):
          </div>
          {performanceData.bottlenecks.map((bottleneck, index) => (
            <div key={index} style={{ 
              marginBottom: '8px', 
              padding: '6px', 
              background: 'rgba(255,100,100,0.1)', 
              borderRadius: '3px',
              borderLeft: `3px solid ${getSeverityColor(bottleneck.severity)}`
            }}>
              <div style={{ color: getSeverityColor(bottleneck.severity), fontWeight: 'bold' }}>
                {bottleneck.message}
              </div>
              <div style={{ fontSize: '9px', color: '#cccccc', marginTop: '3px' }}>
                Suggestions: {bottleneck.suggestions.slice(0, 2).join(', ')}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Loading Analysis */}
      {showLoadingAnalysis && (
        <div style={{ fontSize: '10px', color: '#aaaaaa' }}>
          <div>Textures: {performanceData.textures} | Geometries: {performanceData.geometries}</div>
          <div>Shader Programs: {performanceData.programs}</div>
        </div>
      )}
    </div>
  );
}
