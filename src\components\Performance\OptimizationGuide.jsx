import React from 'react';
import { useControls } from 'leva';

export default function OptimizationGuide() {
  const { showOptimizationGuide } = useControls('Optimization Guide', {
    showOptimizationGuide: false
  });

  if (!showOptimizationGuide) return null;

  const optimizations = [
    {
      category: "Asset Loading",
      priority: "HIGH",
      issues: [
        "Large GLTF/GLB models loading synchronously",
        "Uncompressed textures (JPG/PNG instead of KTX2)",
        "Multiple DRACO models loading simultaneously",
        "No asset preloading or progressive loading"
      ],
      solutions: [
        "Use DRACO compression for all models",
        "Convert textures to KTX2 format",
        "Implement asset preloader with progress indicator",
        "Use Suspense boundaries for progressive loading",
        "Load assets in order of importance (hero content first)"
      ]
    },
    {
      category: "Rendering Performance",
      priority: "HIGH", 
      issues: [
        "400+ debris particles with individual materials",
        "Complex shader calculations in UnderwaterShader",
        "Multiple render targets in SceneManager",
        "No frustum culling for off-screen objects"
      ],
      solutions: [
        "Reduce debris count to 100-200 particles",
        "Use instanced rendering for debris",
        "Optimize shader complexity (reduce iterations)",
        "Implement LOD system for distant objects",
        "Use object pooling for dynamic objects"
      ]
    },
    {
      category: "Memory Management",
      priority: "MEDIUM",
      issues: [
        "Materials and geometries not disposed properly",
        "Textures loaded multiple times",
        "Large normal maps and roughness maps",
        "No texture compression"
      ],
      solutions: [
        "Implement proper cleanup in useEffect",
        "Share materials between similar objects",
        "Use texture atlases where possible",
        "Implement texture streaming for large assets",
        "Use smaller texture resolutions for mobile"
      ]
    },
    {
      category: "Animation Optimization",
      priority: "MEDIUM",
      issues: [
        "Complex wave calculations for each mesh piece",
        "Multiple overlapping animation zones",
        "Continuous rotation calculations",
        "No animation culling for inactive scenes"
      ],
      solutions: [
        "Use simpler animation patterns",
        "Implement animation LOD (fewer calculations for distant objects)",
        "Pause animations when scene is not active",
        "Use requestAnimationFrame for non-critical animations",
        "Pre-calculate animation curves where possible"
      ]
    },
    {
      category: "Scene Management",
      priority: "LOW",
      issues: [
        "Both scenes render simultaneously during transitions",
        "No scene-specific optimization",
        "Camera calculations every frame",
        "Unnecessary re-renders"
      ],
      solutions: [
        "Implement scene-specific render optimization",
        "Use React.memo for expensive components",
        "Optimize camera movement calculations",
        "Implement render-on-demand for static scenes"
      ]
    }
  ];

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'HIGH': return '#ff4444';
      case 'MEDIUM': return '#ffaa44';
      case 'LOW': return '#44ff44';
      default: return '#ffffff';
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: '50%',
      right: '10px',
      transform: 'translateY(-50%)',
      background: 'rgba(0, 0, 0, 0.95)',
      color: 'white',
      padding: '20px',
      borderRadius: '8px',
      fontFamily: 'monospace',
      fontSize: '11px',
      zIndex: 1000,
      width: '400px',
      maxHeight: '80vh',
      overflow: 'auto',
      border: '1px solid #333'
    }}>
      <div style={{ 
        color: '#44aaff', 
        marginBottom: '15px', 
        fontSize: '16px', 
        fontWeight: 'bold',
        textAlign: 'center'
      }}>
        🚀 Performance Optimization Guide
      </div>

      <div style={{ marginBottom: '15px', padding: '10px', background: 'rgba(255,255,255,0.1)', borderRadius: '4px' }}>
        <div style={{ color: '#ffaa44', marginBottom: '5px', fontWeight: 'bold' }}>
          Quick Wins (Immediate Impact):
        </div>
        <div style={{ fontSize: '10px', lineHeight: '1.4' }}>
          • Reduce debris particles from 400 to 200<br/>
          • Convert textures to KTX2 format<br/>
          • Add Suspense boundaries around heavy components<br/>
          • Disable animations when scenes are inactive
        </div>
      </div>

      {optimizations.map((opt, index) => (
        <div key={index} style={{ 
          marginBottom: '15px',
          border: `1px solid ${getPriorityColor(opt.priority)}`,
          borderRadius: '4px',
          overflow: 'hidden'
        }}>
          <div style={{ 
            background: getPriorityColor(opt.priority),
            color: 'black',
            padding: '8px',
            fontWeight: 'bold',
            fontSize: '12px'
          }}>
            {opt.category} - {opt.priority} PRIORITY
          </div>
          
          <div style={{ padding: '10px' }}>
            <div style={{ marginBottom: '8px' }}>
              <div style={{ color: '#ff8888', marginBottom: '4px', fontSize: '10px', fontWeight: 'bold' }}>
                Issues:
              </div>
              {opt.issues.map((issue, i) => (
                <div key={i} style={{ fontSize: '9px', marginBottom: '2px', paddingLeft: '8px' }}>
                  • {issue}
                </div>
              ))}
            </div>
            
            <div>
              <div style={{ color: '#88ff88', marginBottom: '4px', fontSize: '10px', fontWeight: 'bold' }}>
                Solutions:
              </div>
              {opt.solutions.map((solution, i) => (
                <div key={i} style={{ fontSize: '9px', marginBottom: '2px', paddingLeft: '8px' }}>
                  • {solution}
                </div>
              ))}
            </div>
          </div>
        </div>
      ))}

      <div style={{ 
        marginTop: '20px', 
        padding: '10px', 
        background: 'rgba(68, 170, 255, 0.1)', 
        borderRadius: '4px',
        border: '1px solid #44aaff'
      }}>
        <div style={{ color: '#44aaff', marginBottom: '5px', fontWeight: 'bold' }}>
          Performance Monitoring:
        </div>
        <div style={{ fontSize: '10px', lineHeight: '1.4' }}>
          • Target: 60 FPS, &lt;16.67ms frame time<br/>
          • Memory: Keep under 200MB<br/>
          • Draw calls: Aim for &lt;50 per frame<br/>
          • Triangles: Keep under 100k visible<br/>
          • Use browser DevTools Performance tab for detailed analysis
        </div>
      </div>

      <div style={{ 
        marginTop: '15px', 
        padding: '8px', 
        background: 'rgba(255, 255, 255, 0.05)', 
        borderRadius: '4px',
        fontSize: '9px',
        color: '#aaaaaa',
        textAlign: 'center'
      }}>
        💡 Tip: Start with HIGH priority optimizations first.<br/>
        Each optimization should be tested individually.
      </div>
    </div>
  );
}
