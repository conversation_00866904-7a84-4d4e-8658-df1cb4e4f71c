import React, { useEffect, useRef, useState, Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { Fluid } from '@whatisjery/react-fluid-distortion';
import { EffectComposer } from '@react-three/postprocessing';
import { Color } from 'three';
import Lenis from 'lenis';
import gsap from 'gsap'; // Import GSAP
import AnimatedText from './components/AnimatedText'; // Import the AnimatedText component
import styles from './ProjectPage.module.css'; // Updated import for CSS modules

// Enhanced FluidEffect component that affects text and images
function EnhancedFluidEffect({ darkMode = false, scrollProgress = 0, transitionThreshold = 0.1 }) {
  const canvasRef = useRef(null);
  const animationStateRef = useRef({ isAnimating: false, lastX: 0, lastY: 0 });
  const [isDragging, setIsDragging] = useState(false);
  
  // Determine if fluid effects should be disabled based on transition state
  const shouldDisableEffects = scrollProgress > transitionThreshold;
  
  useEffect(() => {
    // Early exit if effects should be disabled during transition
    if (shouldDisableEffects) {
      return;
    }
    
    // Get the canvas element for fluid interaction
    const getCanvas = () => {
      canvasRef.current = document.querySelector(`.${styles['project-fluid-canvas']} canvas`);
      return canvasRef.current;
    };
    
    // Create a pointer move event for the canvas
    const createPointerMove = (x, y) => {
      const canvas = getCanvas();
      if (!canvas) return;
      const event = new PointerEvent('pointermove', { 
        clientX: x, 
        clientY: y, 
        bubbles: true 
      });
      canvas.dispatchEvent(event);
      animationStateRef.current.lastX = x;
      animationStateRef.current.lastY = y;
    };
    
    // Generate a smooth path for the cursor movement
    const generateSmoothPath = (startX, startY, endX, endY, numPoints) => {
      const points = [];
      for (let i = 0; i <= numPoints; i++) {
        const t = i / numPoints;
        // Create a curve with a random control point for natural movement
        const controlX = (startX + endX) / 2 + (Math.random() - 0.5) * 100;
        const controlY = (startY + endY) / 2 + (Math.random() - 0.5) * 100;
        // Quadratic Bezier curve calculation
        const x = Math.pow(1-t, 2) * startX + 2 * (1-t) * t * controlX + Math.pow(t, 2) * endX;
        const y = Math.pow(1-t, 2) * startY + 2 * (1-t) * t * controlY + Math.pow(t, 2) * endY;
        points.push({ x, y });
      }
      return points;
    };
    
    // Simulate a mouse path for automatic effects
    const simulateMousePath = () => {
      const canvas = getCanvas();
      if (!canvas || animationStateRef.current.isAnimating || isDragging) return;
      
      animationStateRef.current.isAnimating = true;
      const rect = canvas.getBoundingClientRect();
      const startX = animationStateRef.current.lastX || (Math.random() * rect.width + rect.left);
      const startY = animationStateRef.current.lastY || (Math.random() * rect.height + rect.top);
      const endX = Math.random() * rect.width + rect.left;
      const endY = Math.random() * rect.height + rect.top;
      const numPoints = Math.floor(Math.random() * 10) + 15; // Random number of points for path
      const points = generateSmoothPath(startX, startY, endX, endY, numPoints);
      
      // Apply the movement with varying speed for natural effect
      const baseDelay = 40;
      points.forEach((point, index) => {
        const pointDelay = baseDelay + Math.random() * 15;
        setTimeout(() => {
          createPointerMove(point.x, point.y);
          if (index === points.length - 1) {
            setTimeout(() => { 
              animationStateRef.current.isAnimating = false; 
            }, 300);
          }
        }, index * pointDelay);
      });
    };
    
    // Get a random interval for the next automated movement
    const getRandomInterval = () => (Math.random() * 3000) + 3000;
    
    // Start the automated movement
    let intervalId;
    const timeoutId = setTimeout(() => {
      simulateMousePath();
      intervalId = setInterval(simulateMousePath, getRandomInterval());
    }, 1000);
    
    // Handle user interactions
    const handleUserInteraction = () => {
      // Stop automation when user starts interacting
      setIsDragging(true);
      clearInterval(intervalId);
      
      // Resume automation after a pause
      const resumeTimeout = setTimeout(() => {
        setIsDragging(false);
        intervalId = setInterval(simulateMousePath, getRandomInterval());
      }, 5000);
      
      return () => clearTimeout(resumeTimeout);
    };
    
    // Add event listeners for user interactions
    const canvas = getCanvas();
    if (canvas) {
      canvas.addEventListener('pointerdown', handleUserInteraction);
    }
    
    // Cleanup function
    return () => { 
      clearTimeout(timeoutId); 
      clearInterval(intervalId); 
      if (canvas) {
        canvas.removeEventListener('pointerdown', handleUserInteraction);
      }
    };
  }, [isDragging, shouldDisableEffects, scrollProgress, transitionThreshold]);
  
  return null;
}

// Video management component for controlling lazy-loaded videos
const LazyVideo = React.forwardRef(({ src, type, autoPlay = true, loop = true, muted = true, controls = false, className = "", ...props }, ref) => {
  const videoRef = useRef(null);
  const combinedRef = React.useCallback(node => {
    // Store the node in our own ref
    videoRef.current = node;
    // Pass it up to the parent ref if provided
    if (typeof ref === 'function') {
      ref(node);
    } else if (ref) {
      ref.current = node;
    }
  }, [ref]);

  // Set up intersection observer to play/pause video based on visibility
  useEffect(() => {
    if (!videoRef.current) return;
    
    const options = {
      root: null, // Use viewport
      rootMargin: '0px',
      threshold: 0.3 // Play video when 30% visible
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Try to play the video
          const playPromise = videoRef.current.play();
          
          // Handle potential play() rejection (usually from user interaction requirement)
          if (playPromise !== undefined) {
            playPromise.catch(error => {
              console.error("Video play failed:", error);
              // You might want to add visual feedback here or a play button
            });
          }
        } else {
          // Pause the video when not visible
          if (videoRef.current && !videoRef.current.paused) {
            videoRef.current.pause();
          }
        }
      });
    }, options);

    observer.observe(videoRef.current);
    
    return () => {
      if (videoRef.current) {
        observer.unobserve(videoRef.current);
      }
    };
  }, [src]); // Re-run if src changes

  return (
    <video
      ref={combinedRef}
      src={src}
      type={type || "video/mp4"}
      autoPlay={false} // Set to false initially - intersection observer will handle this
      loop={loop}
      muted={muted}
      playsInline
      controls={controls}
      className={className}
      {...props}
    />
  );
});

function ProjectPage({ projectId, onBack, onReady, projectsData }) {
  const project = projectsData && projectsData[projectId];
  const contentRef = useRef(null); // Ref for the scroll container
  const contentWrapperRef = useRef(null); // Ref for the content wrapper with fixed height
  const lenisRef = useRef(null);
  const rafRef = useRef(null);
  const lastProjectImageRef = useRef(null);
  const footerSectionRef = useRef(null);
  const footerContentRef = useRef(null); // Add ref for footer content
  
  // Add refs for the back button, about button, and hero image
  const backButtonRef = useRef(null);
  const aboutButtonRef = useRef(null);
  const heroImageRef = useRef(null);
  
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [contentHeight, setContentHeight] = useState(0); // State to store the calculated height
  
  // Added state to control animation start
  const [startAnimation, setStartAnimation] = useState(false);
  
  // Initialize Lenis for smooth scrolling
  useEffect(() => {
    // Check if contentRef is available
    if (!contentRef.current || !contentWrapperRef.current) return;
    
    // Calculate the total height of all content
    const calculateTotalHeight = () => {
      if (!contentWrapperRef.current) return;
      
      // Get all the direct children of the content wrapper
      const children = Array.from(contentWrapperRef.current.children);
      
      // Calculate the total height by summing up the heights of all children
      let totalHeight = 0;
      children.forEach(child => {
        totalHeight += child.offsetHeight;
      });
      
      // Add extra space at the end to ensure we can scroll past the last element
      totalHeight += 200;
      
      setContentHeight(totalHeight);
      
      // If Lenis is already initialized, update its instance
      if (lenisRef.current) {
        lenisRef.current.resize();
      }
    };
    
    // Initialize Lenis with the same settings as in App.js
    const lenisInstance = new Lenis({
      duration: 3.0, 
      easing: (t) => (t === 1 ? 1 : 1 - Math.pow(2, -10 * t)),
      orientation: 'vertical', 
      gestureOrientation: 'vertical', 
      smoothWheel: true,
      wheelMultiplier: 0.8, 
      smoothTouch: true, 
      touchMultiplier: 1.5, 
      infinite: false,
      wrapper: contentRef.current,
      content: contentWrapperRef.current, // Specify content element
      autoResize: true, // Add auto resize to handle content changes
      normalizeWheel: true // Helps with consistent scrolling behavior
    });
    
    lenisRef.current = lenisInstance;
    
    const handleScroll = () => {
      if (!lastProjectImageRef.current || !footerSectionRef.current) return;
      
      const scrollY = lenisRef.current.scroll;
      const viewportHeight = window.innerHeight;
      const totalScrollableHeight = contentHeight - viewportHeight;
      
      // Calculate positions
      const lastImagePosition = lastProjectImageRef.current.offsetTop;
      const lastImageHeight = lastProjectImageRef.current.offsetHeight;
      const footerPosition = footerSectionRef.current.offsetTop;
      
      // Black overlay transition starts at last image
      const blackOverlayStart = lastImagePosition;
      const blackOverlayEnd = lastImagePosition + lastImageHeight;
      const blackOverlayDistance = blackOverlayEnd - blackOverlayStart;

      let progress = 0;
      if (scrollY >= blackOverlayStart) {
        progress = (scrollY - blackOverlayStart) / blackOverlayDistance;
        progress = Math.min(Math.max(progress, 0), 1); // Clamp between 0 and 1
      }
      
      // Footer visibility calculation - based on scroll position in the footer section
      let footerProgress = 0;
      if (scrollY >= footerPosition) {
        // Calculate progress based on how far we've scrolled into the footer section
        footerProgress = (scrollY - footerPosition) / (viewportHeight * 0.7);
        footerProgress = Math.min(Math.max(footerProgress, 0), 1);
        
        // Animate footer content with GSAP
        if (footerContentRef.current) {
          gsap.to(footerContentRef.current, {
            opacity: footerProgress,
            duration: 0.1, // Small duration for responsive feel
            overwrite: true
          });
        }
      } else {
        // Ensure footer is hidden when above the threshold
        if (footerContentRef.current) {
          gsap.to(footerContentRef.current, {
            opacity: 0,
            duration: 0.1,
            overwrite: true
          });
        }
      }
      
      setScrollProgress(progress);
      setIsDarkMode(progress > 0.3);
    };
    
    lenisRef.current.on("scroll", handleScroll);
    
    // Animation frame loop for Lenis
    function raf(time) {
      lenisRef.current?.raf(time);
      rafRef.current = requestAnimationFrame(raf);
    }
    
    rafRef.current = requestAnimationFrame(raf);
    
    // Reset scroll position to top when projectId changes
    lenisRef.current.scrollTo(0, { immediate: true });
    setScrollProgress(0);
    setIsDarkMode(false);
    
    // Hide footer initially with GSAP
    if (footerContentRef.current) {
      gsap.set(footerContentRef.current, { opacity: 0 });
    }
    
    // Calculate initial height after a short delay to ensure DOM is ready
    setTimeout(calculateTotalHeight, 300);

    // Cleanup
    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
      
      if (lenisRef.current) {
        lenisRef.current.off("scroll", handleScroll);
        lenisRef.current.destroy();
        lenisRef.current = null;
      }
    };
  }, [projectId, contentHeight]);

  // Add this useEffect to ensure Lenis knows the correct scrollable area
  useEffect(() => {
    if (lenisRef.current && contentWrapperRef.current) {
      // Force Lenis to recalculate dimensions
      const resizeObserver = new ResizeObserver(() => {
        // When content size changes, recalculate height and update Lenis
        if (contentWrapperRef.current) {
          const children = Array.from(contentWrapperRef.current.children);
          let totalHeight = 0;
          children.forEach(child => {
            totalHeight += child.offsetHeight;
          });
          
          // Add extra space at the end
          totalHeight += 200;
          setContentHeight(totalHeight);
          
          // Update Lenis
          if (lenisRef.current) {
            lenisRef.current.resize();
          }
        }
      });
      
      resizeObserver.observe(contentWrapperRef.current);
      
      return () => {
        if (contentWrapperRef.current) {
          resizeObserver.unobserve(contentWrapperRef.current);
        }
        resizeObserver.disconnect();
      };
    }
  }, [projectId]); // Re-run when project changes

  // Add window resize handler to update Lenis
  useEffect(() => {
    const handleResize = () => {
      if (lenisRef.current && contentWrapperRef.current) {
        // Recalculate content height on window resize
        const children = Array.from(contentWrapperRef.current.children);
        let totalHeight = 0;
        children.forEach(child => {
          totalHeight += child.offsetHeight;
        });
        
        // Add extra space
        totalHeight += 200;
        setContentHeight(totalHeight);
        
        // Update Lenis
        lenisRef.current.resize();
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  // Add effect to delay animation start
  useEffect(() => {
    // Reset animation state when project changes
    setStartAnimation(false);
    
    // Start animation after 2 seconds delay
    const animationTimer = setTimeout(() => {
      setStartAnimation(true);
    }, 100);
    
    return () => clearTimeout(animationTimer);
  }, [projectId]);
  
  // Add effect for back button, about button, and hero image fade-in animation
  useEffect(() => {
    // Initially hide the elements
    if (backButtonRef.current) {
      gsap.set(backButtonRef.current, { opacity: 0 });
    }
    
    if (aboutButtonRef.current) {
      gsap.set(aboutButtonRef.current, { opacity: 0 });
    }
    
    if (heroImageRef.current) {
      gsap.set(heroImageRef.current, { opacity: 0 });
    }
    
    // Fade in elements after 2 seconds
    const buttonAnimationTimer = setTimeout(() => {
      if (backButtonRef.current) {
        gsap.to(backButtonRef.current, {
          opacity: 1,
          duration: 0.8,
          ease: "power2.inOut"
        });
      }
      
      if (aboutButtonRef.current) {
        gsap.to(aboutButtonRef.current, {
          opacity: 1,
          duration: 0.8,
          ease: "power2.inOut"
        });
      }
      
      if (heroImageRef.current) {
        gsap.to(heroImageRef.current, {
          opacity: 1,
          duration: 0.8,
          ease: "power2.inOut"
        });
      }
    }, 2500);
    
    return () => clearTimeout(buttonAnimationTimer);
  }, [projectId]); // Re-run when project changes
  
  // Handle pointer events to make fluid effect follow cursor
  const handlePointerMove = (e) => {
    const canvasElement = document.querySelector(`.${styles['project-fluid-canvas']} canvas`);
    if (canvasElement) {
      const event = new PointerEvent('pointermove', {
        clientX: e.clientX,
        clientY: e.clientY,
        bubbles: true
      });
      canvasElement.dispatchEvent(event);
    }
  };
  
  useEffect(() => {
    if (onReady) {
      const readyTimerId = setTimeout(() => onReady(), 50);
      return () => clearTimeout(readyTimerId);
    }
  }, [projectId, onReady]);
  
  // Function to determine the next project ID
  const getNextProjectId = () => {
    if (!project || !projectsData) return null;
    
    const projectIds = Object.keys(projectsData);
    const currentIndex = projectIds.indexOf(projectId);
    
    // If current project is the last one, go to the first project
    // Otherwise go to the next project in the list
    return currentIndex < projectIds.length - 1 
      ? projectIds[currentIndex + 1] 
      : projectIds[0];
  };
  
  // Handler for next project navigation using custom event
  const handleNextProject = () => {
    const nextProjectId = getNextProjectId();
    if (nextProjectId && projectsData[nextProjectId]) {
      // Create and dispatch a custom event that App.js can listen for
      const customEvent = new CustomEvent('navigateToProject', {
        detail: {
          projectId: nextProjectId,
          projectName: projectsData[nextProjectId].name
        }
      });
      window.dispatchEvent(customEvent);
    }
  };
  
  // Calculate the current background color based on scroll progress
  const getBackgroundColor = () => {
    const r = Math.round(232 - (232 * scrollProgress));
    const g = Math.round(232 - (232 * scrollProgress));
    const b = Math.round(232 - (232 * scrollProgress));
    return `rgb(${r}, ${g}, ${b})`;
  };
  
  // Calculate the current fluid color based on scroll progress
  const getFluidColor = () => {
    // Interpolate between dark and light fluid color
    const darkValue = 59; // #3b3b3b
    const lightValue = 232; // #e8e8e8
    
    // Reverse the interpolation for dark mode
    const value = isDarkMode 
      ? Math.round(lightValue - (lightValue - darkValue) * (1 - scrollProgress))
      : Math.round(darkValue + (lightValue - darkValue) * scrollProgress);
    
    return isDarkMode ? '#e8e8e8' : '#3b3b3b';
  };

  // Extract custom styling from project data or use defaults
  const aboutTitleStyle = project?.aboutTitleStyle || {};
  const descriptionStyle = project?.descriptionStyle || {};
  const infoGridStyle = project?.infoGridStyle || {};

  // Special handling for QUBE project
  const isQubeProject = projectId === 'qube';
  const isNordwoodProject = projectId === 'nordwood';
  
  // Custom grid and heading styles for QUBE project
  const qubeInfoGridStyle = isQubeProject ? {
    gridTemplateColumns: '1fr 1fr', // Make the columns more equal for QUBE project
    ...infoGridStyle
  } : infoGridStyle;
  
  // Custom info heading style for QUBE project - adding 20px top margin to move it down
  const qubeInfoHeadingStyle = isQubeProject ? {
    paddingRight: '20px', // Add more padding to make it visually wider
    maxWidth: '100%', // Allow full width in its column
    width: '100%', // Ensure it takes full width
    marginTop: '20px' // Move the heading 20px lower
  } : {};

  if (!project && projectId) {
    return (
      <div className={styles['project-page-container-wrapper']} style={{ backgroundColor: '#e8e8e8' }}>
        <button 
          onClick={onBack} 
          className={`${styles['back-button']} ${styles['project-page-back-button']}`}
          ref={backButtonRef}
        >
          &larr; Back
        </button>
        <div style={{ color: 'black', textAlign: 'center', paddingTop: '20vh' }}>
          <h1>Project Not Found</h1>
          <p>The project with ID "{projectId}" does not exist.</p>
        </div>
      </div>
    );
  }

  if (!project) {
    return null;
  }

  // Extract images from project data or use placeholders
  const heroImage = project.heroImage || '/placeholder.jpg';
  const heroImage2 = project.heroImage2 || heroImage; // Fallback to heroImage if heroImage2 is not provided
  const heroVideo = project.heroVideo || null;
  const contentImages = project.images && project.images.length >= 4 
    ? project.images.slice(0, 4) 
    : [
        { src: heroImage, alt: "Project image 1" },
        { src: heroImage, alt: "Project image 2" },
        { src: heroImage, alt: "Project image 3" },
        { src: heroImage, alt: "Project image 4" }
      ];

  // Current background and text colors
  const backgroundColor = getBackgroundColor();
  const fluidColor = getFluidColor();

  return (
    <div 
      className={`${styles['project-page-container-wrapper']} ${scrollProgress > 0 ? styles['transitioning'] : ''}`} 
      style={{ 
        backgroundColor,
        '--scroll-progress': scrollProgress,
        '--dark-background': 'rgb(0, 0, 0)',
        '--light-background': 'rgb(232, 232, 232)'
      }}
    >
      {/* Back button positioned at top left - with ref for fade-in */}
      <button 
        onClick={onBack} 
        className={`${styles['back-button']} ${styles['project-page-back-button']}`}
        style={{ color: isDarkMode ? 'white' : 'black' }}
        ref={backButtonRef}
      >
        &larr; Back
      </button>
      
      {/* Fluid Canvas for ProjectPage */}
      <div className={styles['project-fluid-canvas']}>
        <Canvas
          camera={{ position: [0, 0, 5], fov: 45 }}
          gl={{ 
            alpha: true, 
            antialias: true, 
            powerPreference: 'high-performance',
            clearColor: new Color(backgroundColor).toArray()
          }}
          style={{ pointerEvents: scrollProgress > 0.1 ? 'none' : 'auto' }}
        >
          <Suspense fallback={null}>
            <color attach="background" args={[backgroundColor]} />
            <ambientLight intensity={0.5} />
            <pointLight position={[5, 5, 5]} intensity={1} />
            <EffectComposer enabled={scrollProgress <= 0.1}>
              <Fluid 
                radius={0.36} 
                curl={3} 
                swirl={2} 
                distortion={1.5} 
                force={1.2} 
                pressure={0.15} 
                densityDissipation={0.95} 
                velocityDissipation={1.00} 
                intensity={5.5} 
                rainbow={false} 
                blend={7} 
                showBackground={true} 
                backgroundColor='transparent'
                fluidColor={fluidColor}
                enabled={scrollProgress <= 0.1}
              />
            </EffectComposer>
            <EnhancedFluidEffect darkMode={isDarkMode} scrollProgress={scrollProgress} transitionThreshold={0.1} />
          </Suspense>
        </Canvas>
      </div>
      
      {/* Black overlay that fades in over the last image */}
      <div className={styles['black-overlay']} style={{ opacity: scrollProgress }}></div>
      
      {/* Project content area with scrollable content */}
      <div 
        className={styles['project-content-container']} 
        ref={contentRef}
        onPointerMove={handlePointerMove}
      >
        {/* Content wrapper with calculated height */}
        <div 
          ref={contentWrapperRef}
          className={styles['project-content-wrapper']}
          style={{ height: contentHeight > 0 ? `${contentHeight}px` : 'auto' }}
        >
          {/* 1. Heading centered in viewport - NOW USING ANIMATEDTEXT WITH DELAY */}
          <div className={`${styles['project-hero-section']} ${styles['fluid-effect-element']}`}>
            {startAnimation ? (
              <AnimatedText 
                effect="randomLetters" 
                threshold={0.2}
                duration={5}
                staggerAmount={0.7}
                outDuration={0.7}
                outStaggerAmount={0.4}
                elementType="h1"
                className={styles['project-title']}
                // Use the color from the parent component
                style={{ color: isDarkMode ? 'white' : 'black' }}
              >
                {project.title}
              </AnimatedText>
            ) : (
              // Initial placeholder until animation starts
              <h1 
                className={styles['project-title']} 
                style={{ 
                  color: isDarkMode ? 'white' : 'black',
                  opacity: 0 // Start invisible
                }}
              >
                {project.title}
              </h1>
            )}
          </div>
          
          {/* 2. Big image or video center/center - with ref for fade-in */}
          <div 
            className={`${styles['project-hero-image']} ${styles['fluid-effect-element']}`} 
            ref={heroImageRef}
          >
            {isNordwoodProject ? (
              // For nordwood project, use heroImage2 instead of heroVideo
              <img src={heroImage2} alt={project.title} />
            ) : heroVideo ? (
              // For other projects, use heroVideo if available with LazyVideo component
              <LazyVideo 
                src={heroVideo.src}
                type={heroVideo.type || "video/mp4"}
                autoPlay={heroVideo.autoplay !== false}
                loop={heroVideo.loop !== false}
                muted={heroVideo.muted !== false}
                controls={heroVideo.controls === true}
                className={styles['hero-video']}
              />
            ) : (
              // Default fallback to heroImage
              <img src={heroImage} alt={project.title} />
            )}
          </div>
          
          <div 
            className={`${styles['project-info-grid']} ${styles['fluid-effect-element']} ${project.infoGridClass || ''} ${isQubeProject ? styles['qube-info-grid'] : ''}`}
            style={{ 
              color: isDarkMode ? 'white' : 'black',
              ...qubeInfoGridStyle
            }}
          >
            <div 
              className={styles['project-info-heading']}
              style={qubeInfoHeadingStyle}
            >
              {/* About button - with ref for fade-in */}
              <h2 
                className={project.aboutTitleClass || ''} 
                style={{
                  ...aboutTitleStyle,
                  ...(isQubeProject ? { maxWidth: '100%', width: '100%' } : {})
                }}
                ref={aboutButtonRef}
              >
                {project.aboutTitle || "About this project"}
              </h2>
            </div>
            <div className={styles['project-info-content']}>
              <p 
                className={project.descriptionClass || ''} 
                style={descriptionStyle}
              >
                {project.description}
              </p>
              <p>
                {/*{project.category && <span>{project.category}</span>}*/}
                {/*{project.client && <span> | {project.client}</span>}*/}
                {/*{project.year && <span> | {project.year}</span>}*/}
              </p>
            </div>
          </div>
          
          {/* 4. Four big images below each other */}
          <div className={styles['project-gallery-images']}>
            {contentImages.map((media, index) => (
              <div 
                className={`${styles['project-gallery-image']} ${styles['fluid-effect-element']}`} 
                key={index}
                ref={index === contentImages.length - 1 ? lastProjectImageRef : null}
              >
                {media.type === "video" ? (
                  <LazyVideo 
                    src={media.src}
                    type={media.videoType || "video/mp4"}
                    autoPlay={media.autoplay !== false}
                    loop={media.loop !== false}
                    muted={media.muted !== false}
                    controls={media.controls === true}
                    className={styles['gallery-video']}
                  />
                ) : (
                  <img src={media.src} alt={media.alt || `${project.title} image ${index + 1}`} />
                )}
              </div>
            ))}
          </div>
          
          {/* Combined Next Project + Footer Section (200vh height) */}
          <div
            ref={footerSectionRef}
            className={styles['combined-footer-section']}
            style={{
              backgroundColor: 'transparent',
              height: '200vh',
              position: 'relative',
              zIndex: 4
            }}
          >
            {/* Next Project Section - First 100vh */}
            <div 
              className={styles['next-project-section']}
              style={{ 
                height: '100vh',
                backgroundColor: 'transparent',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'column'
              }}
            >
              <button 
                onClick={handleNextProject} 
                className={styles['next-project-button']}
                style={{ color: 'white' }}
              >
                See next Project
              </button>
            </div>

            {/* Footer Content Section - Second 100vh */}
            <div 
              ref={footerContentRef}
              className={styles['project-footer-content']}
              style={{ 
                opacity: 0,
                Height: '100vh',
              }}
            >
              {/* Big Contact Email Heading in Center */}
              <div className={styles['contact-email-container']}>
                <a href="mailto:<EMAIL>" className={styles['contact-email-link']}>
                  <h2 className={styles['contact-email-heading']}><EMAIL></h2>
                </a>
              </div>
              
              {/* Bottom Bar with Copyright and Built By */}
              <div className={styles['contact-bottom']}>
                <div className={styles['contact-copyright']}>All rights reserved. © 2025 BLCKs</div>
                <div className={styles['contact-built-by']}>built by BLCKs with ❤</div>

                {/* Footer section with 3 elements positioned as requested */}
                <div className={styles['footer-section']}>
                  {/* Footer links on the left */}
                  <div className={styles['footer-links-container']}>
                    <div className={styles['footer-links']}>
                      <a href="https://aback-syrup-e00.notion.site/BLCKS-IMPRESSUM-20d5934475c580629a78fdc6bbee5d60" className={styles['footer-link']} target="_blank" rel="noopener noreferrer">Impressum</a>
                      <a href="https://aback-syrup-e00.notion.site/BLCKS-DATENSCHUTZERKL-RUNG-20d5934475c580b69fdae48e3a5c6848" className={styles['footer-link']} target="_blank" rel="noopener noreferrer">Datenschutz</a>
                      <a href="https://aback-syrup-e00.notion.site/BLCKS-COOKIE-RICHTLINIE-20d5934475c58034a1edc5c56179f398" className={styles['footer-link']} target="_blank" rel="noopener noreferrer">Cookies</a>
                    </div>
                  </div>
                  
                  {/* Social icons in the middle */}
                  <div className={styles['social-icons']}>
                    <a href="#" className={styles['social-icon']}>
                      <img src="/instagram.svg" alt="Instagram" />
                    </a>
                    <a href="https://www.linkedin.com/in/philippfuchs-blcks" className={styles['social-icon']} target="_blank" rel="noopener noreferrer">
                      <img src="/linkedin.svg" alt="LinkedIn" />
                    </a>
                    <a href="#" className={styles['social-icon']}>
                      <img src="/twitter.svg" alt="Twitter" />
                    </a>
                  </div>

                  {/* QR Code section on the right */}
                  <div className={styles['qr-code-container']}>
                    <img src="/qr-code.svg" alt="WhatsApp QR Code" className={styles['qr-code-image']} />
                    <h3 className={styles['qr-code-heading']}>WhatsApp Coming</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProjectPage;