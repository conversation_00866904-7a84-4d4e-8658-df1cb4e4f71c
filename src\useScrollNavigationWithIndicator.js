import { useEffect, useRef, useState } from 'react';
import indicatorStyles from './ScrollIndicator.module.css';

export function useScrollNavigation({ onNext, onBack, scrollThreshold = 15 }) {
  const scrollAttemptsRef = useRef({ top: 0, bottom: 0 });
  const contentRef = useRef(null);
  const indicatorRef = useRef(null);
  const indicatorFillRef = useRef(null);
  const fadeOverlayRef = useRef(null);
  const hasShownIndicator = useRef(false);
  
  useEffect(() => {
    if (!contentRef.current || (!onNext && !onBack)) return;
    
    const element = contentRef.current;
    
    // Create indicator elements if they don't exist
    if (!indicatorRef.current) {
      // Create container element
      indicatorRef.current = document.createElement('div');
      indicatorRef.current.className = indicatorStyles.scrollIndicator;
      
      // Create the fill element (white progress bar)
      indicatorFillRef.current = document.createElement('div');
      indicatorFillRef.current.className = indicatorStyles.scrollIndicatorFill;
      
      // Append fill element to container
      indicatorRef.current.appendChild(indicatorFillRef.current);
      document.body.appendChild(indicatorRef.current);
    }
    
    // Create fade overlay for back navigation
    if (!fadeOverlayRef.current) {
      fadeOverlayRef.current = document.createElement('div');
      fadeOverlayRef.current.className = indicatorStyles.fadeOverlay;
      document.body.appendChild(fadeOverlayRef.current);
    }
    
    // Initially hide the indicator and fade overlay
    indicatorRef.current.className = indicatorStyles.scrollIndicator;
    fadeOverlayRef.current.className = indicatorStyles.fadeOverlay;
    
    const updateIndicator = (active, direction, progress) => {
      if (active) {
        // Show the indicator
        indicatorRef.current.className = `${indicatorStyles.scrollIndicator} ${indicatorStyles.active}`;
        
        // Calculate progress percentage
        const progressPercentage = Math.min((progress / scrollThreshold) * 100, 100);
        
        // Always fill from left to right, regardless of direction
        indicatorFillRef.current.style.width = `${progressPercentage}%`;
        indicatorFillRef.current.style.left = '0';
        indicatorFillRef.current.style.right = 'auto';
        
        // If scrolling up at top, show fade overlay with opacity based on progress
        if (direction === 'up') {
          const fadeOpacity = Math.min(progressPercentage / 200, 0.5); // Max opacity 0.5
          fadeOverlayRef.current.className = `${indicatorStyles.fadeOverlay} ${indicatorStyles.active}`;
          fadeOverlayRef.current.style.opacity = fadeOpacity;
        } else {
          fadeOverlayRef.current.className = indicatorStyles.fadeOverlay;
        }
      } else {
        indicatorRef.current.className = indicatorStyles.scrollIndicator;
        indicatorFillRef.current.style.width = '0%';
        fadeOverlayRef.current.className = indicatorStyles.fadeOverlay;
      }
    };
    
    // Prevent indicator from showing at initial page load
    setTimeout(() => {
      hasShownIndicator.current = true;
    }, 1000);
    
    // Function to check if we're at boundaries
    const isAtPageBoundary = () => {
      const isAtTop = element.scrollTop === 0;
      const isAtBottom = Math.abs(element.scrollHeight - element.scrollTop - element.clientHeight) < 2;
      return { isAtTop, isAtBottom };
    };
    
    // Track cumulative scroll to prevent rapid increments
    let cumulativeScroll = {
      top: 0,
      bottom: 0,
      lastDirection: null,
      lastTime: 0
    };
    
    const handleWheel = (e) => {
      if (!hasShownIndicator.current) return;
      
      const { isAtTop, isAtBottom } = isAtPageBoundary();
      
      // Minimum scroll amount to count as an increment (higher value = less sensitive)
      const scrollThresholdAmount = 50;
      
      // Minimum time between scroll counts in milliseconds (higher value = less rapid filling)
      const minTimeBetweenIncrements = 150;
      
      const now = Date.now();
      const timeSinceLastIncrement = now - cumulativeScroll.lastTime;
      
      // Only process wheel events when at the boundaries
      if (isAtTop && e.deltaY < 0) {
        // We're at the top and scrolling up
        scrollAttemptsRef.current.bottom = 0; // Reset bottom counter
        
        // Show fade overlay immediately when scrolling up at the top
        fadeOverlayRef.current.className = `${indicatorStyles.fadeOverlay} ${indicatorStyles.active}`;
        
        // Reset if direction changed
        if (cumulativeScroll.lastDirection !== 'up') {
          cumulativeScroll.top = 0;
          cumulativeScroll.lastDirection = 'up';
        }
        
        // Accumulate scroll amount
        cumulativeScroll.top += Math.abs(e.deltaY);
        
        // If we've accumulated enough scrolling and enough time has passed
        if (cumulativeScroll.top >= scrollThresholdAmount && timeSinceLastIncrement >= minTimeBetweenIncrements) {
          scrollAttemptsRef.current.top++;
          cumulativeScroll.top = 0;
          cumulativeScroll.lastTime = now;
          
          // Show and update indicator
          updateIndicator(true, 'up', scrollAttemptsRef.current.top);
          
          // Navigate if threshold reached
          if (scrollAttemptsRef.current.top >= scrollThreshold && onBack) {
            scrollAttemptsRef.current.top = 0;
            // Don't reset the overlay immediately, let the navigation handle it
            onBack();
          }
        }
      } else if (isAtBottom && e.deltaY > 0) {
        // We're at the bottom and scrolling down
        scrollAttemptsRef.current.top = 0; // Reset top counter
        fadeOverlayRef.current.className = indicatorStyles.fadeOverlay; // Hide fade overlay
        
        // Reset if direction changed
        if (cumulativeScroll.lastDirection !== 'down') {
          cumulativeScroll.bottom = 0;
          cumulativeScroll.lastDirection = 'down';
        }
        
        // Accumulate scroll amount
        cumulativeScroll.bottom += Math.abs(e.deltaY);
        
        // If we've accumulated enough scrolling and enough time has passed
        if (cumulativeScroll.bottom >= scrollThresholdAmount && timeSinceLastIncrement >= minTimeBetweenIncrements) {
          scrollAttemptsRef.current.bottom++;
          cumulativeScroll.bottom = 0;
          cumulativeScroll.lastTime = now;
          
          // Show and update indicator
          updateIndicator(true, 'down', scrollAttemptsRef.current.bottom);
          
          // Navigate if threshold reached
          if (scrollAttemptsRef.current.bottom >= scrollThreshold && onNext) {
            scrollAttemptsRef.current.bottom = 0;
            updateIndicator(false);
            onNext();
          }
        }
      } else if (!isAtTop && !isAtBottom) {
        // Hide indicator when not at boundaries and reset counters
        scrollAttemptsRef.current.top = 0;
        scrollAttemptsRef.current.bottom = 0;
        cumulativeScroll = {
          top: 0,
          bottom: 0,
          lastDirection: null,
          lastTime: 0
        };
        updateIndicator(false);
        fadeOverlayRef.current.className = indicatorStyles.fadeOverlay; // Hide fade overlay
      }
    };
    
    // Add wheel event listener
    element.addEventListener('wheel', handleWheel, { passive: false });
    
    return () => {
      element.removeEventListener('wheel', handleWheel);
      if (indicatorRef.current && document.body.contains(indicatorRef.current)) {
        document.body.removeChild(indicatorRef.current);
        indicatorRef.current = null;
        indicatorFillRef.current = null;
      }
      if (fadeOverlayRef.current && document.body.contains(fadeOverlayRef.current)) {
        document.body.removeChild(fadeOverlayRef.current);
        fadeOverlayRef.current = null;
      }
    };
  }, [onNext, onBack, scrollThreshold]);
  
  return contentRef;
}