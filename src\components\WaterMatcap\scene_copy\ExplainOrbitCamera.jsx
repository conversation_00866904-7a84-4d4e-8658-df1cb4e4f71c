import { useThree } from '@react-three/fiber';
import { useEffect, useRef } from 'react';
// import { OrbitControls } from '@react-three/drei';
import { orbitCamera, useCameraStore } from './cameras2';
import * as THREE from 'three';

export default function ExplainOrbitCamera({ position = [0, 3, 0], lookAt = [0, 0, 0] }) {
  const set = useCameraStore((state) => state.set);
  const threeSet = useThree((state) => state.set);
  const size = useThree((state) => state.size);
  const controlsRef = useRef();

  // Set camera aspect and projection matrix on mount/resize
  useEffect(() => {
    orbitCamera.aspect = size.width / size.height;
    orbitCamera.updateProjectionMatrix();
    set(orbitCamera);
    threeSet({ camera: orbitCamera });
  }, [set, threeSet, size]);

  // Set camera position and controls target when props change
  useEffect(() => {
    orbitCamera.position.set(...position);
    orbitCamera.up.set(0, 1, 0);
    if (controlsRef.current) {
      controlsRef.current.target.set(...lookAt);
      controlsRef.current.update();
    } else {
      orbitCamera.lookAt(...lookAt);
    }
  }, [position, lookAt]);

  return (
    <>
      <primitive object={orbitCamera} />
      {/* <OrbitControls ref={controlsRef} camera={orbitCamera} /> */}
    </>
  );
} 