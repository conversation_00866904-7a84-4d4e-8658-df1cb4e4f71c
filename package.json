{"name": "blcks_web_simple", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@alienkitty/alien.js": "^1.2.0", "@pmndrs/assets": "^1.7.0", "@react-spring/three": "^10.0.1", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@react-three/postprocessing": "^3.0.4", "@studio-freight/lenis": "^1.0.42", "@whatisjery/react-fluid-distortion": "^1.4.4", "astro": "^5.8.0", "chart.js": "^4.5.0", "gsap": "^3.13.0", "imagesloaded": "^5.0.0", "lenis": "^1.3.3", "leva": "^0.10.0", "lottie-react": "^2.4.1", "maath": "^0.10.8", "ogl": "^1.0.11", "postprocessing": "^6.37.3", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "react-spring": "^10.0.1", "splitting": "^1.1.0", "suspend-react": "^0.1.3", "three": "^0.177.0", "three-mesh-bvh": "^0.9.1", "wouter": "^3.7.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@gltf-transform/cli": "^4.2.0", "@gltf-transform/core": "^4.2.0", "@gltf-transform/extensions": "^4.2.0", "@gltf-transform/functions": "^4.2.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "rollup-plugin-visualizer": "^6.0.3", "vite": "^6.3.5", "vite-plugin-glsl": "^1.4.1"}}