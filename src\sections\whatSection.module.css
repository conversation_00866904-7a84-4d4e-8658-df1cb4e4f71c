/* Overlay container for WhatSection */
.overlayContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none; /* overlays are non-interactive by default */
  z-index: 10;
}

.sectionContent {
  background: rgba(20, 20, 20, 0.85);
  color: #fff;
  padding: 2rem 3rem;
  border-radius: 1.5rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.25);
  font-size: 1.5rem;
  max-width: 600px;
  text-align: center;
  pointer-events: auto; /* allow interaction if needed */
  transition: opacity 0.4s cubic-bezier(.4,0,.2,1);
  opacity: 1;
}

/* Hidden state for overlays */
.hidden {
  opacity: 0;
  pointer-events: none;
}

/* Block 1 Overlay Styles */
.block1Container {
  width: 400px;
  height: 636px;
  background: rgba(255, 255, 255, 0.10);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  position: relative;
  padding: 0 16px 16px 16px;
  box-sizing: border-box;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.block1TopLabel {
  font-size: 2.1rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  font-family: 'Inter', 'Arial Black', Arial, sans-serif;
  letter-spacing: 0.01em;
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 2;
}

.block1CardWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  position: absolute;
  /* Default: top left, can be overridden */
  bottom: 2rem;
  left: 2rem;
  z-index: 20;
}

/* Utility class for custom overlay placement */
.draggableOverlayPosition {
  /* Example: place overlay anywhere */
  top: 40px;
  left: 60px;
}

.block1Card {
  background: rgba(255, 255, 255, 1);
  border-radius: 16px;
  width: 100%;
  padding: 0px 18px 18px 18px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  margin: 0;
  box-sizing: border-box;
}

.block1Headline {
  font-size: 1.40rem;
  font-weight: 800;
  margin-bottom: 12px;
  color: #000000;
  font-family: 'Inter', 'Arial Black', Arial, sans-serif;
  letter-spacing: 0.01em;
}

.block1HeaderRow {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-size: 0.75rem;
  color: #181818;
  margin-bottom: 4px;
}
.block1HeaderLeft {
  flex: 2;
  text-align: left;
  font-size: 0.75rem;
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-weight: 300;
}
.block1HeaderCenter {
  flex: 1;
  text-align: center;
  font-size: 0.75rem;
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-weight: 300;
}
.block1HeaderRight {
  flex: 1;
  text-align: right;
  font-size: 0.75rem;
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-weight: 300;
}

.block1DottedDivider {
  width: 100%;
  text-align: center;
  color: #000000;
  font-size: 1.15rem;
  font-weight: 300;
  letter-spacing: 0.050em;
  margin: 4px 0 8px 0;
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  user-select: none;
}

.block1BodyRow {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  gap: 32px;
}

.block1BodyLeft {
  flex: 1.2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 6px;
}

.block1ProcessType {
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-size: 0.65rem;
  font-weight: 300;
  color: #000000;
  margin-bottom: 14px;
}

.block1Barcode {
  margin-top: 0px;
  width: 160px;
  height: 125px;
  display: block;
}

.block1BodyRight {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 4px;
  min-width: 100px;
  text-align: left;
}

.block1LogoRow {
  width: 100%;
  display: flex;
  justify-content: flex-start;
}

.block1Logo {
  font-size: 1.2rem;
  font-weight: 900;
  font-family: 'Archivo Black', 'Arial Black', Arial, sans-serif;
  color: #000000;
  letter-spacing: 0.00em;
}
.superscript {
  font-size: 0.6rem;
  vertical-align: super;
  margin-left: 2px;
}

.block1RefId {
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-size: 0.7rem;
  font-weight: 300;
  color: #000000;
  text-align: left;
  margin-bottom: 2px;
}

.block1Principle, .block1Goal {
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-size: 0.7rem;
  font-weight: 300;
  color: #000000;
  text-align: left;
  margin-bottom: 2px;
}

.block1CenterTexts {
  position: static;
  display: block;
  z-index: 10;
  pointer-events: none;
}

.block1CenterText {
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-weight: 300;
  font-size: 0.90rem;
  letter-spacing: 0.10em;
  color: #fff;
  pointer-events: none;
  position: absolute;
}

.block1CenterText1 {
  top: 15rem;
  left: 47rem;
}

.block1CenterText2 {
  bottom: 20rem;
  right: 40rem;
}

.block1CenterText3 {
  top: 22rem;
  right: 37rem;
}

.firstSectionContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 10;
}

.firstSectionTopRow {
  position: absolute;
  top: 2.5rem;
  left: 2.5rem;
  display: flex;
  flex-direction: row;
  gap: 2.5rem;
}

.firstSectionSmallText {
  font-family: 'Supply', 'Consolas', 'Courier New', monospace;
  font-size: 0.85rem;
  color: #fff;
  font-weight: 300;
  letter-spacing: 0.08em;
  opacity: 0.85;
  line-height: 1.2;
  pointer-events: none;
}

.firstSectionHeading {
  position: absolute;
  bottom: 2.5rem;
  left: 2.5rem;
  font-family: 'Inter', 'Arial', sans-serif;
  font-size: 2.3rem;
  color: #fff;
  font-weight: 700;
  line-height: 1.08;
  letter-spacing: -0.01em;
  max-width: 32rem;
  pointer-events: none;
}
