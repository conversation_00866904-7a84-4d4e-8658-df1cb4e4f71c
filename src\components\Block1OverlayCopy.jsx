import React from 'react';

function CornerL({ position }) {
  // position: 'tl', 'tr', 'bl', 'br'
  const size = 10; // line length
  const offset = 24; // distance from text
  const color = '#fff';
  const styleBase = { position: 'absolute', width: size, height: size };
  let style = {};
  if (position === 'tl') style = { top: 0, left: 0 };
  if (position === 'tr') style = { top: 0, right: 0 };
  if (position === 'bl') style = { bottom: 0, left: 0 };
  if (position === 'br') style = { bottom: 0, right: 0 };
  return (
    <div style={{ ...styleBase, ...style }}>
      {/* Horizontal */}
      <div style={{ position: 'absolute', top: 0, left: 0, width: size, height: 1, background: color }} />
      {/* Vertical */}
      <div style={{ position: 'absolute', top: 0, left: 0, width: 1, height: size, background: color }} />
    </div>
  );
}

// Reusable card component
function BentoCard({ heading, subheading, description, style, className }) {
  return (
    <div
      className={className}
      style={{
        background: '#161616',
        borderRadius: 16,
        padding: '32px 32px 24px 32px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-end',
        minHeight: 0,
        minWidth: 0,
        ...style,
      }}
    >
      <div style={{ fontSize: 64, fontWeight: 700, color: 'rgba(255,255,255,0.18)', lineHeight: 1, marginBottom: 16 }}>{heading}</div>
      <div style={{ fontSize: 20, fontWeight: 600, color: '#fff', marginBottom: 8 }}>{subheading}</div>
      <div style={{ fontSize: 15, color: '#b0b0b0', fontWeight: 400 }}>{description}</div>
    </div>
  );
}

function StatCard({ value, label }) {
  return (
    <div
      style={{
        background: 'rgba(34,34,34,0.05)',
        border: '0px solid rgba(255,255,255,0.18)',
        backdropFilter: 'blur(4px)',
        WebkitBackdropFilter: 'blur(4px)',
        borderRadius: 18,
        flex: 1,
        minWidth: 120,
        minHeight: 150,
        padding: '22px 16px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        margin: 0,
      }}
    >
      <div style={{ fontSize: 36, fontWeight: 700, color: '#b49cff', marginBottom: 8 }}>{value}</div>
      <div style={{ fontSize: 15, color: '#b0b0b0', textAlign: 'center', fontWeight: 400, lineHeight: 1.25 }}>{label}</div>
    </div>
  );
}

function EmptyCard() {
  return (
    <div
      style={{
        background: 'rgba(34,34,34,0.05)',
        borderRadius: 20,
        border: '0px solid rgba(255,255,255,0.18)',
        backdropFilter: 'blur(4px)',
        WebkitBackdropFilter: 'blur(4px)',
        flex: 1,
        minWidth: 120,
        aspectRatio: '4/3',
        margin: 0,
      }}
    />
  );
}

export default function Block1Overlay({ onClose, children }) {
  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        background: 'rgba(20,20,20,0.75)',
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#fff',
        fontSize: 32,
        padding: 0,
        boxSizing: 'border-box',
        backdropFilter: 'blur(16px)',
        WebkitBackdropFilter: 'blur(16px)',
      }}
    >
      <button
        onClick={onClose}
        style={{
          position: 'absolute',
          top: 32,
          right: 32,
          background: 'none',
          border: 'none',
          color: '#666666',
          fontSize: 15,
          fontWeight: 400,
          letterSpacing: 2,
          padding: '12px 32px',
          cursor: 'pointer',
          outline: 'none',
          zIndex: 1002,
        }}
        aria-label="Close overlay"
      >
        CLOSE
      </button>
      <div
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'flex-start',
          justifyContent: 'center',
          gap: 40,
          paddingTop: 50,
          paddingBottom: 50,
          paddingLeft: 50,
          paddingRight: 50,
          boxSizing: 'border-box',
        }}
      >
        {/* Left column: heading and big card at bottom */}
        <div
          style={{
            flex: '0 0 27.5%',
            minWidth: 240,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-end',
            alignItems: 'flex-start',
            height: '100%',
          }}
        >
          {/* Card at the top (replacing heading) */}
          <div
            style={{
              width: '100%',
              height: 325,
              minHeight: 80,
              background: 'rgba(34,34,34,0.05)',
              borderRadius: 24,
              border: '0px solid rgba(255,255,255,0.18)',
              backdropFilter: 'blur(4px)',
              WebkitBackdropFilter: 'blur(4px)',
              marginBottom: 24,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <div style={{
              position: 'relative',
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
              <div style={{
                fontSize: 32,
                fontWeight: 400,
                color: '#888888',
                textAlign: 'center',
                opacity: 0.85,
                maxWidth: 450,
                width: '100%',
              }}>
                „Die Frage ist nicht, ob AI unsere Arbeitswelt verändert – sondern wie schnell.“
              </div>
              <div style={{
                position: 'absolute',
                right: 24,
                bottom: 18,
                fontSize: 12,
                color: '#b0b0b0',
                opacity: 0.7,
                fontWeight: 400,
              }}>
                – Satya Nadella, CEO von Microsoft
              </div>
            </div>
          </div>
          {/* Big card at the bottom */}
          <div
            style={{
              width: '100%',
              height: '50vh',
              minHeight: 180,
              background: 'rgba(34,34,34,0.05)',
              borderRadius: 24,
              border: '0px solid rgba(255,255,255,0.18)',
              backdropFilter: 'blur(4px)',
              WebkitBackdropFilter: 'blur(4px)',
              marginRight: 10,
            }}
          />
        </div>
        {/* Right content */}
        <div style={{ flex: 1, minWidth: 0, display: 'flex', flexDirection: 'column', height: '100%', justifyContent: 'flex-end' }}>
          {/* Small heading above description */}
          <div style={{ fontSize: 20, fontWeight: 600, color: '#fff', marginBottom: 8 }}>Was hinter KI Automation steckt</div>
          {/* Description */}
          <div style={{ marginBottom: 24 }}>
            <div style={{ fontSize: 15, color: '#b0b0b0', fontWeight: 400, lineHeight: 1.4, maxWidth: 900 }}>
              KI Automation Kombiniert Klassische Automatisierung (Z.B. Robotic Process Automation, RPA) Mit Lernfähigen KI-Technologien Wie Machine Learning Oder Natural Language Processing. Dadurch Können Nicht Nur Strukturierte, Sondern Auch Komplexere, Unstrukturierte Aufgaben Automatisiert Werden. Typische Beispiele Sind Die Automatische Verarbeitung Von E-Mails, Rechnungen, Die Analyse Von Kundendaten Oder Die Steuerung Von Maschinen In Der Produktion.
            </div>
          </div>
          {/* Heading and description below main description */}
          <div style={{ marginBottom: 32 }}>
            <div style={{ fontSize: 20, fontWeight: 600, color: '#fff', marginBottom: 8 }}>
            Warum KI Automation jetzt entscheidend ist?
            </div>
            <div style={{ fontSize: 15, color: '#b0b0b0', fontWeight: 400, lineHeight: 1.4, maxWidth: 900 }}>
            KI Automation ist längst kein Zukunftsthema mehr – sie entwickelt sich zum Standard für Unternehmen, die wettbewerbsfähig bleiben wollen. Der weltweite Markt wächst jährlich zweistellig, weil Unternehmen erkennen, wie stark automatisierte Prozesse zur Effizienz, Skalierbarkeit und Resilienz beitragen.
            Ob Fachkräftemangel, steigende Betriebskosten oder komplexere Kundenanforderungen – AI Automation bietet konkrete Lösungen: Sie reduziert manuelle Arbeit, verkürzt Reaktionszeiten, minimiert Fehler und schafft Raum für strategische Aufgaben.
            </div>
          </div>
          {/* Stats Row */}
          <div
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'flex-start',
              gap: 16,
              marginBottom: 30,
            }}
          >
            <StatCard value="100 M+" label="End Users Supported\nWorldwide" />
            <StatCard value="7.5 M+" label="End Users Supported\nWorldwide" />
            <StatCard value="300 M+" label="End Users Supported\nWorldwide" />
            <StatCard value="80 %" label="End Users Supported\nWorldwide" />
          </div>
          {/* Bottom row of empty cards */}
          <div
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'flex-start',
              gap: 16,
            }}
          >
            <EmptyCard />
            <EmptyCard />
            <EmptyCard />
          </div>
        </div>
      </div>
      {children}
    </div>
  );
} 