import { useThree } from "@react-three/fiber";
import { useMemo } from "react";
import { Perspective<PERSON><PERSON><PERSON>, Vector2, Vector3 } from "three";

import { FLOW_SIM_SIZE } from "./constants";
import { createFlowMaterial } from "./materials/flow-material";
import { createFlowNormalMaterial } from "./materials/flow-normal-material";
import { getMapDebugProgram } from "./materials/map-debug-program";
import { getRaymarchProgram } from "./materials/raymarch-program";
import { flowSize } from "./use-targets";

export function useMaterials(targets, assets) {
  const size = useThree((state) => state.size);
  const materials = useMemo(() => {
    // FLOW MATERIAL (floor)
    const flowMaterial = createFlowMaterial(
      targets.flowFbo.read.texture,
      flowSize,
      { EDGE_DAMPING: "" }
    );

    // FLOW MATERIAL (orbe)
    const orbeFlowMaterial = createFlowMaterial(
      targets.orbeFlowFbo.read.texture,
      flowSize,
      {}
    );

    // FLOW NORMAL MATERIAL (TODO delete)
    const flowNormalMaterial = createFlowNormalMaterial();
    flowNormalMaterial.uniforms.uHeightmap.value = targets.flowFbo.read.texture;

    const mapDebugProgram = getMapDebugProgram();

    // WATER FLOOR MATERIAL
    const raymarchMaterial = getRaymarchProgram();
    raymarchMaterial.uniforms.uMatcap = { value: assets.matcap };
    raymarchMaterial.uniforms.time = { value: 0.0 };
    raymarchMaterial.uniforms.uNear = { value: 0.1 };
    raymarchMaterial.uniforms.uFar = { value: 10 };
    raymarchMaterial.uniforms.uHitPosition = { value: new Vector3() };
    raymarchMaterial.uniforms.noiseScale = { value: 5.0 };
    raymarchMaterial.uniforms.noiseLength = { value: 0.4 };
    raymarchMaterial.uniforms.uFlowTexture = {
      value: targets.flowFbo.read.texture
    };
    raymarchMaterial.uniforms.fov = { value: 45 };
    raymarchMaterial.uniforms.cameraQuaternion = { value: null };
    raymarchMaterial.uniforms.resolution = {
      value: new Vector2(size.width, size.height)
    };
    raymarchMaterial.uniforms.pyramidReveal = { value: 0.0 };
    raymarchMaterial.uniforms.mouseSpeed = { value: 0.0 };
    raymarchMaterial.uniforms.uNoiseTexture = { value: assets.noiseMap };
    raymarchMaterial.uniforms.uFlowSize = { value: FLOW_SIM_SIZE / 2 };

    const updateFlowCamera = (camera) => {
      raymarchMaterial.uniforms.cameraQuaternion.value = camera.quaternion;
      raymarchMaterial.uniforms.fov.value = camera.fov;
      raymarchMaterial.uniforms.resolution.value = new Vector2(
        size.width,
        size.height
      );
      raymarchMaterial.uniforms.uNear.value = camera.near;
      raymarchMaterial.uniforms.uFar.value = camera.far;
    };

    return {
      flowMaterial,
      flowNormalMaterial,
      mapDebugProgram,
      raymarchMaterial,
      orbeFlowMaterial,
      updateFlowCamera
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [targets, assets, size.width, size.height]);

  // update resolution related uniforms
  materials.raymarchMaterial.uniforms.resolution.value.set(
    size.width,
    size.height
  );

  return materials;
}