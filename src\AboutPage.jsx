import React, { useRef, useEffect, useState } from 'react'
import { Canvas, useThree } from '@react-three/fiber'
import * as THREE from 'three'
import aboutStyles from './About.module.css'
import servicesStyles from './Services.module.css'
import stepsStyles from './Steps.module.css'
import contactStyles from './Contact.module.css'
import Lenis from 'lenis'
import AnimatedText from './components/AnimatedText'

import ReflectiveFloor from './components/ReflectiveFloor.jsx'
import Romannumber from './components/Romannumber.jsx'
import Blcks_logo from './components/Blcks_logo_ext_1.jsx'

// Camera controller component that responds to scroll
function CameraController({ scrollY }) {
  const { camera } = useThree()
  const initialPosition = useRef(null)
  
  useEffect(() => {
    if (!initialPosition.current) {
      initialPosition.current = { 
        x: camera.position.x,
        y: camera.position.y, 
        z: camera.position.z 
      }
    }
  }, [camera])
  
  useEffect(() => {
    if (!initialPosition.current) return
    
    const baseFOV = 60
    const maxFOV = 65
    const maxMoveDown = 0.5
    
    const scrollProgress = Math.min(scrollY / (window.innerHeight * 3.5), 1)
    const easedProgress = easeOutCubic(scrollProgress)
    
    camera.fov = baseFOV + easedProgress * (maxFOV - baseFOV)
    camera.position.y = initialPosition.current.y - (easedProgress * maxMoveDown)
    camera.updateProjectionMatrix()
    
  }, [scrollY, camera])
  
  const easeOutCubic = (x) => {
    return 1 - Math.pow(1 - x, 3)
  }
  
  return null
}

// Scene opacity controller
function SceneFadeController({ scrollY }) {
  const { scene } = useThree()
  
  useEffect(() => {
    const scrollStart = window.innerHeight * 0.5
    const scrollEnd = window.innerHeight * 2
    
    let opacity = 1
    if (scrollY > scrollStart) {
      opacity = 1 - Math.min((scrollY - scrollStart) / (scrollEnd - scrollStart), 1)
    }
    
    scene.traverse((object) => {
      if (object.isMesh && object.material) {
        if (Array.isArray(object.material)) {
          object.material.forEach(material => {
            if (material.opacity !== undefined) {
              material.opacity = opacity
              material.transparent = true
            }
          })
        } else {
          if (object.material.opacity !== undefined) {
            object.material.opacity = opacity
            object.material.transparent = true
          }
        }
      }
    })
    
  }, [scrollY, scene])
  
  return null
}


// Mobile background component with camera zoom-out movement
function MobileBackground({ section, scrollY }) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const backgroundRef = useRef(null);
  const imageRef = useRef(null);
  
  // Get the appropriate image path based on section
  const getImagePath = () => {
    switch (section) {
      case 'about':
        return '/1_mobile_comp.jpg';
      case 'services':
        return '/2_mobile_comp.jpg';
      case 'steps':
        return '/3_mobile_comp.jpg';
      case 'contact':
        return null; // No background image for contact, just black
      default:
        return '/1_mobile_comp.jpg';
    }
  };
  
  const imagePath = getImagePath();
  
  // Apply fade effect based on scroll position
  useEffect(() => {
    if (!imageRef.current) return;
    
    const scrollStart = window.innerHeight * 0.5;
    const scrollEnd = window.innerHeight * 2;
    
    let opacity = 1;
    if (scrollY > scrollStart) {
      opacity = 1 - Math.min((scrollY - scrollStart) / (scrollEnd - scrollStart), 1);
    }
    
    // Only fade the image, keep the black background
    imageRef.current.style.opacity = opacity;
  }, [scrollY]);
  
  // Apply camera movement effect based on scroll - zoom OUT instead of in
  useEffect(() => {
    if (!imageRef.current) return;
    
    // Start with a slightly larger image and gradually reduce size to simulate zoom out
    const initialScale = 1.05;
    const finalScale = 0.98;
    const maxMoveDown = 20; // pixels to move down
    
    const scrollProgress = Math.min(scrollY / (window.innerHeight * 3.5), 1);
    const easedProgress = easeOutCubic(scrollProgress);
    
    // Apply transforms to simulate camera movement - zoom out
    const translateY = easedProgress * maxMoveDown;
    const scale = initialScale - (easedProgress * (initialScale - finalScale));
    
    imageRef.current.style.transform = `translateY(${translateY}px) scale(${scale})`;
  }, [scrollY]);
  
  // Same easing function as used in CameraController
  const easeOutCubic = (x) => {
    return 1 - Math.pow(1 - x, 3);
  };
  
  const containerStyle = {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundColor: '#000', // Always black background
    overflow: 'hidden',
    zIndex: 1,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  };
  
  const imageStyle = {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    objectPosition: 'center center',
    transition: 'opacity 0.3s ease',
    transformOrigin: 'center center',
    willChange: 'transform, opacity', // Optimize for animations
    transform: 'scale(1.05)' // Start slightly zoomed in
  };
  
  return (
    <div style={containerStyle} ref={backgroundRef} className="mobile-background">
      {imagePath && (
        <img 
          ref={imageRef}
          src={imagePath} 
          alt="" 
          style={imageStyle}
          onLoad={() => setImageLoaded(true)}
        />
      )}
    </div>
  );
}



export default function AboutPage({ onBack, onNext, onReady, onBackToHome, activeSection = 'about' }) {
  // Use the activeSection prop as the initial state, and update when prop changes
  const [currentSection, setCurrentSection] = useState(activeSection)
  const [scrollY, setScrollY] = useState(0)
  const [isMobile, setIsMobile] = useState(false)
  const overlayRef = useRef(null)
  const lenisRef = useRef(null)
  
  // Add navigation cooldown state to prevent rapid section changes
  const [navigationCooldown, setNavigationCooldown] = useState(false)
  
  // Add navigation state to track if navigation is in progress
  const [isNavigating, setIsNavigating] = useState(false)
  
  // Create wrapped navigation functions with cooldown and visual feedback
  const handleNavigation = (direction) => {
    if (navigationCooldown || isNavigating) return;
    
    setNavigationCooldown(true);
    setIsNavigating(true);
    
    // Add visual feedback for mobile navigation
    if (isMobile) {
      const overlay = document.createElement('div');
      overlay.style.position = 'fixed';
      overlay.style.top = '0';
      overlay.style.left = '0';
      overlay.style.width = '100%';
      overlay.style.height = '100%';
      overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
      overlay.style.zIndex = '1000';
      overlay.style.transition = 'opacity 0.5s';
      document.body.appendChild(overlay);
      
      // Remove overlay after navigation
      setTimeout(() => {
        overlay.style.opacity = '0';
        setTimeout(() => {
          if (document.body.contains(overlay)) {
            document.body.removeChild(overlay);
          }
        }, 500);
      }, 200);
    }
    
    // Execute navigation after a small delay to ensure the visual feedback is visible
    setTimeout(() => {
      if (direction === 'next') {
        onNext();
      } else if (direction === 'back') {
        onBack();
      } else if (direction === 'home') {
        onBackToHome();
      }
      
      // Set a cooldown period longer for mobile to prevent accidental rapid navigation
      setTimeout(() => {
        setNavigationCooldown(false);
        setTimeout(() => {
          setIsNavigating(false);
        }, 200);
      }, isMobile ? 1200 : 800);
    }, 50);
  };
  
  // Update currentSection when activeSection prop changes
  useEffect(() => {
    setCurrentSection(activeSection);
    
    // Reset scroll position when section changes
    if (lenisRef.current) {
      lenisRef.current.scrollTo(0, { immediate: true });
    }
    
    // Add a brief period where navigation is disabled during section transitions
    setNavigationCooldown(true);
    setIsNavigating(true);
    setTimeout(() => {
      setNavigationCooldown(false);
      setTimeout(() => {
        setIsNavigating(false);
      }, 200);
    }, 1200); // Slightly longer than animation duration
  }, [activeSection]);
  
  // Check if device is mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    // Initial check
    checkIfMobile();
    
    // Listen for window resize events
    window.addEventListener('resize', checkIfMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);
  
  // Define scroll navigation hook with separate behavior for mobile and desktop
  const useScrollNavigation = ({ onNext, onBack, scrollThreshold = 3 }) => {
    const ref = useRef(null);
  
    useEffect(() => {
      const element = ref.current;
      if (!element) return;
      
      // Skip setting up scroll navigation for the contact section
      if (currentSection === 'contact') return;
  
      let touchStartY = 0;
      let lastScrollTop = 0;
      let scrollDirection = '';
      let scrollAmount = 0;
      // Track last navigation time to prevent too rapid changes
      let lastNavigationTime = 0;
      // Track if touch has moved enough to be considered a swipe
      let touchHasMoved = false;
  
      const handleWheel = (e) => {
        const currentScrollTop = element.scrollTop;
        
        // Detect scroll direction
        if (currentScrollTop > lastScrollTop) {
          // Scrolling down
          if (scrollDirection !== 'down') {
            scrollDirection = 'down';
            scrollAmount = 0;
          }
          scrollAmount += 1;
        } else if (currentScrollTop < lastScrollTop) {
          // Scrolling up
          if (scrollDirection !== 'up') {
            scrollDirection = 'up';
            scrollAmount = 0;
          }
          scrollAmount += 1;
        }
  
        lastScrollTop = currentScrollTop;
        
        // Check if at top or bottom of the scrollable area
        const isAtTop = currentScrollTop <= 0;
        // More conservative check for bottom of scrollable area
        const isAtBottom = currentScrollTop + element.clientHeight >= element.scrollHeight - 30;
        
        // Get current time to check cooldown
        const now = Date.now();
        // Enforce minimum time between navigations (800ms)
        const cooldownPeriod = isMobile ? 1200 : 500; // Longer cooldown for mobile
        const navigationCooldown = now - lastNavigationTime < cooldownPeriod;
  
        if (isAtTop && scrollDirection === 'up' && scrollAmount >= scrollThreshold && !navigationCooldown && !isNavigating) {
          if (onBack) {
            e.preventDefault();
            onBack(); // Direct call for desktop to maintain original behavior
            scrollAmount = 0;
            lastNavigationTime = now;
          }
        } else if (isAtBottom && scrollDirection === 'down' && scrollAmount >= scrollThreshold && !navigationCooldown && !isNavigating) {
          if (onNext) {
            e.preventDefault();
            onNext(); // Direct call for desktop to maintain original behavior
            scrollAmount = 0;
            lastNavigationTime = now;
          }
        }
      };
  
      const handleTouchStart = (e) => {
        touchStartY = e.touches[0].clientY;
        touchHasMoved = false;
      };
  
      const handleTouchMove = (e) => {
        if (!touchStartY) return;
        
        const touchY = e.touches[0].clientY;
        const diff = touchStartY - touchY;
        
        // Mark that touch has moved a significant amount
        if (Math.abs(diff) > 30) {
          touchHasMoved = true;
        }
        
        // More conservative check for top and bottom of scrollable area
        const isAtTop = element.scrollTop <= 5;
        const isAtBottom = element.scrollTop + element.clientHeight >= element.scrollHeight - 30;
        
        // Get current time to check cooldown
        const now = Date.now();
        // Enforce minimum time between navigations
        const navigationCooldown = now - lastNavigationTime < 1200; // 1.2 seconds cooldown for mobile
  
        // For mobile: Use higher threshold for touch events
        if (isMobile) {
          // Increased threshold from 80 to 120 pixels to prevent accidental navigation
          // And check if we've moved enough to be considered a swipe
          if (isAtTop && diff < -120 && touchHasMoved && !navigationCooldown && !isNavigating) {
            if (onBack) {
              e.preventDefault();
              // Use a timeout to debounce the navigation
              setTimeout(() => {
                handleNavigation('back');
                touchStartY = 0;
                touchHasMoved = false;
                lastNavigationTime = now;
              }, 100);
            }
          } else if (isAtBottom && diff > 120 && touchHasMoved && !navigationCooldown && !isNavigating) {
            if (onNext) {
              e.preventDefault();
              // Use a timeout to debounce the navigation
              setTimeout(() => {
                handleNavigation('next');
                touchStartY = 0;
                touchHasMoved = false;
                lastNavigationTime = now;
              }, 100);
            }
          }
        } else {
          // For desktop: Use original threshold for touch events
          if (isAtTop && diff < -50 && !navigationCooldown && !isNavigating) {
            if (onBack) {
              e.preventDefault();
              onBack(); // Direct call for desktop
              touchStartY = 0;
              lastNavigationTime = now;
            }
          } else if (isAtBottom && diff > 50 && !navigationCooldown && !isNavigating) {
            if (onNext) {
              e.preventDefault();
              onNext(); // Direct call for desktop
              touchStartY = 0;
              lastNavigationTime = now;
            }
          }
        }
      };
  
      const handleTouchEnd = () => {
        touchStartY = 0;
        touchHasMoved = false;
      };
  
      element.addEventListener('scroll', handleWheel, { passive: false });
      element.addEventListener('touchstart', handleTouchStart, { passive: false });
      element.addEventListener('touchmove', handleTouchMove, { passive: false });
      element.addEventListener('touchend', handleTouchEnd, { passive: false });
  
      return () => {
        element.removeEventListener('scroll', handleWheel);
        element.removeEventListener('touchstart', handleTouchStart);
        element.removeEventListener('touchmove', handleTouchMove);
        element.removeEventListener('touchend', handleTouchEnd);
      };
    }, [onNext, onBack, scrollThreshold, currentSection, isMobile, isNavigating]);
  
    return ref;
  };
  
  // Add the scroll navigation hook
  const scrollNavRef = useScrollNavigation({
    onNext, // Direct function reference for desktop
    onBack, // Direct function reference for desktop
    scrollThreshold: 0
  });

  useEffect(() => {
    if (onReady) {
      onReady();
    }
  }, [onReady]);

  // Get current styles based on active section
  const getCurrentStyles = () => {
    switch (currentSection) {
      case 'about':
        return aboutStyles;
      case 'services':
        return servicesStyles;
      case 'steps':
        return stepsStyles;
      case 'contact':
        return contactStyles;
      default:
        return aboutStyles;
    }
  };
  
  const styles = getCurrentStyles();

  // Initialize Lenis smooth scrolling with enhanced smoothness settings
  useEffect(() => {
    // Skip Lenis initialization for contact section
    if (!overlayRef.current || currentSection === 'contact') return;
    
    const contentSection = overlayRef.current.querySelector(`.${styles.contentSection}`);
    if (!contentSection) {
      console.error("Could not find content section");
      return;
    }
    
    lenisRef.current = new Lenis({
      wrapper: overlayRef.current,
      content: contentSection,
      duration: 3,
      easing: (t) => {
        return 1 - Math.pow(1 - t, 5) + Math.sin(t * Math.PI) * 0.01;
      },
      smoothWheel: true,
      wheelMultiplier: 0.8,
      direction: 'vertical',
      gestureDirection: 'vertical',
      smooth: true,
      mouseMultiplier: 0.8,
      smoothTouch: true,
      // Adjust touch multiplier based on device type - reduced for mobile to prevent overshooting
      touchMultiplier: isMobile ? 0.7 : 1.3,
      lerp: isMobile ? 0.1 : 0.075, // Higher lerp (less smoothing) for mobile
      infinite: false,
    })

    // Synchronize scroll values with React state
    const onScrollUpdate = (e) => {
      setScrollY(e.scroll);
    }

    lenisRef.current.on('scroll', onScrollUpdate)

    // Set up requestAnimationFrame for smoother animation
    function raf(time) {
      if (lenisRef.current) {
        lenisRef.current.raf(time)
      }
      requestAnimationFrame(raf)
    }
    
    // Start the animation loop
    requestAnimationFrame(raf)
    
    // Clean up function
    return () => {
      if (lenisRef.current) {
        lenisRef.current.off('scroll', onScrollUpdate)
        lenisRef.current.destroy()
        lenisRef.current = null
      }
    }
  }, [currentSection, styles, isMobile])
  
  // Handler for manual fluid effect on pointer move
  const handlePointerMove = (e) => {
    // Only process pointer events on desktop to save resources on mobile
    if (isMobile) return;
    
    const canvasElement = document.querySelector(`.${getCanvasContainerClass()} canvas`);
    if (canvasElement) {
      const event = new PointerEvent('pointermove', {
        clientX: e.clientX,
        clientY: e.clientY,
        bubbles: true
      });
      canvasElement.dispatchEvent(event);
    }
  };
  
  // Merge the refs (Lenis ref and scroll navigation ref)
  const mergeRefs = (node) => {
    overlayRef.current = node;
    if (scrollNavRef) {
      scrollNavRef.current = node;
    }
  };
  
  // Render appropriate section content based on currentSection
  const renderSectionContent = () => {
    switch (currentSection) {
      case 'about':
        return renderAboutContent();
      case 'services':
        return renderServicesContent();
      case 'steps':
        return renderStepsContent();
      case 'contact':
        return renderContactContent();
      default:
        return renderAboutContent();
    }
  };
  
  // Render Roman numerals based on currentSection
  const renderRomanNumerals = () => {
    switch (currentSection) {
      case 'about':
        return <Romannumber position={[0.175, 0.45, -1]} scale={[1.25, 1.25, 1.25]} />;
      case 'services':
        return (
          <>
            <Romannumber position={[-0.425, 0.45, -1]} scale={[1.25, 1.25, 1.25]} />
            <Romannumber position={[0.775, 0.45, -1]} scale={[1.25, 1.25, 1.25]} />
          </>
        );
      case 'steps':
        return (
          <>
            <Romannumber position={[-1.075, 0.45, -1]} scale={[1.25, 1.25, 1.25]} />
            <Romannumber position={[0.175, 0.45, -1]} scale={[1.25, 1.25, 1.25]} />
            <Romannumber position={[1.425, 0.45, -1]} scale={[1.25, 1.25, 1.25]} />
          </>
        );
      case 'contact':
        return null; // No Roman numerals in contact
      default:
        return <Romannumber position={[0.175, 0.45, -1]} scale={[1.25, 1.25, 1.25]} />;
    }
  };
  
  // Render About content
  const renderAboutContent = () => {
    return (
      <section className={aboutStyles.contentSection}>
        {/* First paragraph with logo instead of heading */}
        <div className={`${aboutStyles.paragraph} ${aboutStyles.paragraph1} ${aboutStyles.left}`}>
          <div className={`${aboutStyles.paragraphContainer} ${isMobile ? aboutStyles.mobileContainer : ''}`}>
            <div className={aboutStyles.logoContainer}>
              {/* Use AnimatedText for the heading with fadeLines effect */}
              <AnimatedText 
                effect="fadeLines" 
                threshold={0.2}
                elementType="h2"
              >
                DESIGN STUDIO
              </AnimatedText>
            </div>
            {/* Use AnimatedText for the paragraph with fadeLines effect */}
            <AnimatedText 
              effect="fadeLines" 
              threshold={0.2}
              elementType="p"
            >
              BLOCKS gestaltet digitale Räume, die mehr sind als nur Webseiten – sie bewegen, erzählen, faszinieren.
              Ob interaktive Interfaces, animierte Oberflächen oder immersive 3D-Elemente: Wir entwickeln smarte, 
              zeitgemäße Weblösungen, die Nutzer:innen fesseln und Marken zum Leben erwecken.
            </AnimatedText>
          </div>
        </div>
        
        <div className={`${aboutStyles.paragraph} ${aboutStyles.paragraph2} ${aboutStyles.right}`}>
          <div className={`${aboutStyles.paragraphContainer} ${isMobile ? aboutStyles.mobileContainer : ''}`}>
            {/* Use AnimatedText for the heading with fadeLines effect */}
            <AnimatedText 
              effect="fadeLines" 
              threshold={0.2}
              elementType="h2"
            >
              BEYOND TRENDS
            </AnimatedText>
            
            {/* Use AnimatedText for the paragraph with fadeLines effect */}
            <AnimatedText 
              effect="fadeLines" 
              threshold={0.2}
              elementType="p"
            >
              Bei BLCKS folgen wir nicht einfach Trends - wir setzen auf eine neue Art der digitalen Transformation. 
              Eine, die sich an dir, deiner Marke und deiner Zielgruppe orientiert, um nachhaltige, personalisierte Erlebnisse zu schaffen.
            </AnimatedText>
          </div>
        </div>
        
        {/* Modified third paragraph with image container and small subheading */}
        <div className={`${aboutStyles.paragraph} ${aboutStyles.paragraph3} ${aboutStyles.left}`}>
          <div className={`${aboutStyles.paragraphContainer} ${isMobile ? aboutStyles.mobileContainer : ''}`}>
            <div className={aboutStyles.imageContainer}>
              {/* Keep the image as is */}
              <img src="/phil_transparent.avif" alt="Philipp Fuchs" className={aboutStyles.sectionImage} />
            </div>
            <div className={aboutStyles.smallSubheading}>
              {/* Use AnimatedText for the subheading with fadeLines effect */}
              <AnimatedText 
                effect="fadeLines" 
                threshold={0.2}
                elementType="h3"
              >
                Philipp Fuchs
              </AnimatedText>
            </div>
          </div>
        </div>
        
        {/* Modified fourth paragraph with image container and small subheading */}
        <div className={`${aboutStyles.paragraph} ${aboutStyles.paragraph4} ${aboutStyles.right}`}>
          <div className={`${aboutStyles.paragraphContainer} ${isMobile ? aboutStyles.mobileContainer : ''}`}>
            <div className={aboutStyles.imageContainer}>
              {/* Keep the image as is */}
              <img src="/maxi_blur.avif" alt="Maximilian Dirnberger" className={aboutStyles.sectionImage} />
            </div>
            <div className={aboutStyles.smallSubheading}>
              {/* Use AnimatedText for the subheading with fadeLines effect */}
              <AnimatedText 
                effect="fadeLines" 
                threshold={0.2}
                elementType="h3"
              >
                Maximilian Dirnberger
              </AnimatedText>
            </div>
          </div>
        </div>
      </section>
    );
  };
  
  // Render Services content
  const renderServicesContent = () => {
    return (
      <section className={servicesStyles.contentSection}>
        {/* First paragraph with logo instead of heading */}
        <div className={`${servicesStyles.paragraph} ${servicesStyles.paragraph1} ${servicesStyles.left}`}>
          <div className={`${servicesStyles.paragraphContainer} ${isMobile ? servicesStyles.mobileContainer : ''}`}>
            <div className={servicesStyles.logoContainer}>
              {/* Replace h2 with AnimatedText */}
              <AnimatedText
                effect="fadeLines"
                threshold={0.2}
                elementType="h2"
              >
                SERVICES
              </AnimatedText>
            </div>
            {/* Replace p with AnimatedText */}
            <AnimatedText
              effect="fadeLines"
              threshold={0.2}
              elementType="p"
            >
              Unsere Leistungen reichen von modernen, smarten Websites mit Fokus auf Nutzerfreundlichkeit und Ästhetik bis hin zu interaktiven 3D-Animationen und Web-Erlebnissen, die Besucher:innen nachhaltig begeistern.
            </AnimatedText>
          </div>
        </div>
        
        <div className={`${servicesStyles.paragraph} ${servicesStyles.paragraph2} ${servicesStyles.right}`}>
          <div className={`${servicesStyles.paragraphContainer} ${isMobile ? servicesStyles.mobileContainer : ''}`}>
              <div className={servicesStyles.smallSubheading}>
                  {/* Replace h3 with AnimatedText */}
                  <AnimatedText
                    effect="fadeLines"
                    threshold={0.2}
                    elementType="h3"
                  >
                    Digitale Lösungen, die deine Marke voranbringen.
                  </AnimatedText>
              </div>
          </div>
        </div>
        
        {/* Modified third paragraph - bullet points with AnimatedText */}
        <div className={`${servicesStyles.paragraph} ${servicesStyles.paragraph3} ${servicesStyles.left}`}>
          <div className={`${servicesStyles.paragraphContainer} ${isMobile ? servicesStyles.mobileContainer : ''}`}>
              <div className={`${servicesStyles.bulletPointsContainer} ${isMobile ? servicesStyles.mobileBulletPoints : ''}`}>
                  {/* Replace each bullet point with AnimatedText */}
                  <AnimatedText
                    effect="fadeLines"
                    threshold={0.1}
                    elementType="p"
                    className={servicesStyles.bulletPoint}
                  >
                    Webdesign
                  </AnimatedText>
                  
                  <AnimatedText
                    effect="fadeLines"
                    threshold={0.1}
                    elementType="p"
                    className={servicesStyles.bulletPoint}
                  >
                    Webentwicklung
                  </AnimatedText>
                  
                  <AnimatedText
                    effect="fadeLines"
                    threshold={0.1}
                    elementType="p"
                    className={servicesStyles.bulletPoint}
                  >
                    3D-Animationen
                  </AnimatedText>
                  
                  <AnimatedText
                    effect="fadeLines"
                    threshold={0.1}
                    elementType="p"
                    className={servicesStyles.bulletPoint}
                  >
                    E-Commerce
                  </AnimatedText>
                  
                  <AnimatedText
                    effect="fadeLines"
                    threshold={0.1}
                    elementType="p"
                    className={servicesStyles.bulletPoint}
                  >
                    Shopify
                  </AnimatedText>
                  
                  <AnimatedText
                    effect="fadeLines"
                    threshold={0.1}
                    elementType="p"
                    className={servicesStyles.bulletPoint}
                  >
                    Content Management
                  </AnimatedText>
                  
                  <AnimatedText
                    effect="fadeLines"
                    threshold={0.1}
                    elementType="p"
                    className={servicesStyles.bulletPoint}
                  >
                    SEO & Performance
                  </AnimatedText>
                  
                  <AnimatedText
                    effect="fadeLines"
                    threshold={0.1}
                    elementType="p"
                    className={servicesStyles.bulletPoint}
                  >
                    Branding
                  </AnimatedText>
                  
                  <AnimatedText
                    effect="fadeLines"
                    threshold={0.1}
                    elementType="p"
                    className={servicesStyles.bulletPoint}
                  >
                    Hosting & Wartung
                  </AnimatedText>
              </div>
          </div>
        </div>
      </section>
    );
  };
  
  // Render Steps content
  const renderStepsContent = () => {
    return (
      <section className={stepsStyles.contentSection}>
        {/* First paragraph with logo instead of heading */}
        <div className={`${stepsStyles.paragraph} ${stepsStyles.paragraph1} ${stepsStyles.left}`}>
          <div className={`${stepsStyles.paragraphContainer} ${isMobile ? stepsStyles.mobileContainer : ''}`}>
            <div className={stepsStyles.logoContainer}>
              {/* Replace h2 with AnimatedText */}
              <AnimatedText
                effect="fadeLines"
                threshold={0.2}
                elementType="h2"
              >
                Unser Prozess beginnt mit dem tiefen Eintauchen in deine Marke
              </AnimatedText>
            </div>
            {/* Replace p with AnimatedText */}
            <AnimatedText
              effect="fadeLines"
              threshold={0.2}
              elementType="p"
            >
              Wir nehmen uns Zeit, deine Marke wirklich zu verstehen – von deiner Geschichte über deine Werte bis hin zu den Zielen, die du erreichen möchtest. Nur so können wir eine Lösung schaffen, die wirklich zu dir passt.
            </AnimatedText>
          </div>
        </div>
        
        <div className={`${stepsStyles.paragraph} ${stepsStyles.paragraph2} ${stepsStyles.right}`}>
          <div className={`${stepsStyles.paragraphContainer} ${isMobile ? stepsStyles.mobileContainer : ''}`}>
            <div className={stepsStyles.smallHeading}>
              {/* Replace h3 with AnimatedText */}
              <AnimatedText
                effect="fadeLines"
                threshold={0.2}
                elementType="h3"
              >
                ANALYSE
              </AnimatedText>
            </div>
            {/* Replace p with AnimatedText */}
            <AnimatedText
              effect="fadeLines"
              threshold={0.2}
              elementType="p"
            >
              Gemeinsam analysieren wir deine Zielgruppe, den Markt und deine Wettbewerber. Darauf aufbauend entwickeln wir eine klare, individuelle Strategie, die den Rahmen für das gesamte Projekt bildet und sicherstellt, dass wir genau die richtigen Akzente setzen.
            </AnimatedText>
          </div>
        </div>
        
        {/* Modified third paragraph with image container and small subheading */}
        <div className={`${stepsStyles.paragraph} ${stepsStyles.paragraph3} ${stepsStyles.left}`}>
          <div className={`${stepsStyles.paragraphContainer} ${isMobile ? stepsStyles.mobileContainer : ''}`}>
            <div className={stepsStyles.smallHeading}>
              {/* Replace h3 with AnimatedText */}
              <AnimatedText
                effect="fadeLines"
                threshold={0.2}
                elementType="h3"
              >
                DESIGN & ENTWICKLUNG
              </AnimatedText>
            </div>
            {/* Replace p with AnimatedText */}
            <AnimatedText
              effect="fadeLines"
              threshold={0.2}
              elementType="p"
            >
              Wir verwandeln die Strategie in ein modernes, intuitives Design, das deine Nutzer:innen begeistert und deine Marke stark macht. Dabei achten wir nicht nur auf Optik, sondern auch auf einfache Bedienbarkeit und technische Qualität. So entsteht ein digitales Erlebnis, das sowohl schön als auch funktional ist.
            </AnimatedText>
          </div>
        </div>
        
        {/* Modified fourth paragraph with image container and small subheading */}
        <div className={`${stepsStyles.paragraph} ${stepsStyles.paragraph4} ${stepsStyles.right}`}>
          <div className={`${stepsStyles.paragraphContainer} ${isMobile ? stepsStyles.mobileContainer : ''}`}>
            <div className={stepsStyles.smallHeading}>
              {/* Replace h3 with AnimatedText */}
              <AnimatedText
                effect="fadeLines"
                threshold={0.2}
                elementType="h3"
              >
                LAUNCH & SUPPORT
              </AnimatedText>
            </div>
            {/* Replace p with AnimatedText */}
            <AnimatedText
              effect="fadeLines"
              threshold={0.2}
              elementType="p"
            >
              Wenn alles fertig ist, bekommst du ein rundum fertiges Produkt, das wir gemeinsam prüfen und optimieren. Auch nach dem Launch sind wir an deiner Seite – mit persönlichem Support, regelmäßigen Updates und Anpassungen, damit deine Website immer up to date und leistungsfähig bleibt.
            </AnimatedText>
          </div>
        </div>
      </section>
    );
  };
  
  // Render Contact content - modified to be a fixed 100vh section
  const renderContactContent = () => {
    return (
      <section className={contactStyles.contentSection}>
        <div className={contactStyles.contactWrapper}>
          <section className={contactStyles.contactSection}>
            <div className={contactStyles.contactContent}>
              {/* Big Contact Email Heading in Center */}
              <div className={contactStyles.contactEmailContainer}>
                <a href="mailto:<EMAIL>" className={contactStyles.contactEmailLink}>
                  <h2 className={contactStyles.contactEmailHeading}><EMAIL></h2>
                </a>
              </div>
              
              {/* Bottom Bar with Copyright and Built By */}
              <div className={contactStyles.contactBottom}>
                <div className={contactStyles.contactCopyright}>All rights reserved. © 2025 BLCKs</div>
                <div className={contactStyles.contactBuiltBy}>built by BLCKs with ❤</div>

                {/* Footer section with 3 elements positioned as requested */}
                <div className={contactStyles.footerSection}>
                  {/* Footer links on the left */}
                  <div className={contactStyles.footerLinksContainer}>
                    <div className={contactStyles.footerLinks}>
                      <a href="https://aback-syrup-e00.notion.site/BLCKS-IMPRESSUM-20d5934475c580629a78fdc6bbee5d60" className={contactStyles.footerLink} target="_blank" rel="noopener noreferrer">Impressum</a>
                      <a href="https://aback-syrup-e00.notion.site/BLCKS-DATENSCHUTZERKL-RUNG-20d5934475c580b69fdae48e3a5c6848" className={contactStyles.footerLink} target="_blank" rel="noopener noreferrer">Datenschutz</a>
                      <a href="https://aback-syrup-e00.notion.site/BLCKS-COOKIE-RICHTLINIE-20d5934475c58034a1edc5c56179f398" className={contactStyles.footerLink} target="_blank" rel="noopener noreferrer">Cookies</a>
                    </div>
                  </div>
                  
                  {/* Social icons in the middle */}
                  <div className={contactStyles.socialIcons}>
                    <a href="#" className={contactStyles.socialIcon}>
                      <img src="/instagram.svg" alt="Instagram" />
                    </a>
                    <a href="https://www.linkedin.com/in/philippfuchs-blcks" className="social-icon" target="_blank" rel="noopener noreferrer">
                      <img src="/linkedin.svg" alt="LinkedIn" />
                    </a>
                    <a href="#" className={contactStyles.socialIcon}>
                      <img src="/twitter.svg" alt="Twitter" />
                    </a>
                  </div>

                  {/* QR Code section on the right */}
                  <div className={contactStyles.qrCodeContainer}>
                    <img src="/qr-code.svg" alt="WhatsApp QR Code" className={contactStyles.qrCodeImage} />
                    <h3 className={contactStyles.qrCodeHeading}>WhatsApp Coming</h3>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </section>
    );
  };
  
  // Define navigation buttons based on current section
  const renderNavigationButtons = () => {
    const commonBackButtonStyle = {
      position: 'fixed',
      top: '2rem',
      left: '50%',
      transform: 'translateX(-50%)',
      zIndex: 100,
      background: 'transparent',
      border: 'none',
      cursor: 'pointer',
      padding: '10px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      fontSize: '0.90rem',
    };
    
    const commonNextButtonStyle = {
      position: 'fixed',
      bottom: '2rem',
      left: '50%',
      transform: 'translateX(-50%)',
      zIndex: 100,
      background: 'transparent',
      border: 'none',
      cursor: 'pointer',
      padding: '10px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      fontSize: '0.90rem',
    };
    
    return (
      <>
        <button 
          className="back-button"
          onClick={isMobile ? () => handleNavigation('back') : onBack}
          style={commonBackButtonStyle}
          disabled={navigationCooldown || isNavigating}
        >
          Back
        </button>
        
        {currentSection !== 'contact' ? (
          <button 
            className="next-button"
            onClick={isMobile ? () => handleNavigation('next') : onNext}
            style={commonNextButtonStyle}
            disabled={navigationCooldown || isNavigating}
          >
            Next
          </button>
        ) : (
          <button 
            className="back-to-home-button"
            onClick={isMobile ? () => handleNavigation('home') : onBackToHome}
            style={commonNextButtonStyle}
            disabled={navigationCooldown || isNavigating}
          >
            Back to Home
          </button>
        )}
      </>
    );
  };
  
  // Get the appropriate component style based on currentSection
  const getComponentStyle = () => {
    switch (currentSection) {
      case 'about':
        return aboutStyles.aboutComponent;
      case 'services':
        return servicesStyles.servicesComponent;
      case 'steps':
        return stepsStyles.stepsComponent;
      case 'contact':
        return contactStyles.contactComponent;
      default:
        return aboutStyles.aboutComponent;
    }
  };
  
  // Get the appropriate canvas container class based on currentSection
  const getCanvasContainerClass = () => {
    switch (currentSection) {
      case 'about':
        return 'about-canvas-container';
      case 'services':
        return 'services-canvas-container';
      case 'steps':
        return 'steps-canvas-container';
      case 'contact':
        return 'contact-canvas-container';
      default:
        return 'about-canvas-container';
    }
  };
  
  return (
    <div className={getComponentStyle()} style={{ width: '100%', height: '100vh', position: 'relative' }}>
      {/* Navigation buttons */}
      {renderNavigationButtons()}

      {/* Mobile background images - only shown on mobile */}
      {isMobile && <MobileBackground section={currentSection} scrollY={scrollY} />}

      {/* 3D Canvas with Container - only shown on desktop */}
      {!isMobile && (
        <div className={getCanvasContainerClass()} style={{ position: 'absolute', width: '100%', height: '100%' }}>
          <Canvas camera={{ position: [0, 0, 5], fov: 60 }}>
            <ambientLight intensity={0.5} />
            <pointLight position={[10, 10, 10]} intensity={0.5} />

            <color attach="background" args={['#000']} />
            <fog attach="fog" args={['#060606', 1, 200]} />

            {/* Render Roman numerals based on active section */}
            {renderRomanNumerals()}
            
            <ReflectiveFloor />
            
            {/* Camera controllers */}
            <CameraController scrollY={scrollY} />
            <SceneFadeController scrollY={scrollY} />
          </Canvas>
        </div>
      )}
      
      {/* Scroll Overlay - use mergeRefs to combine both refs */}
      <div 
        className={`${styles.scrollOverlay} ${isMobile ? 'mobile-mode' : ''} ${currentSection === 'contact' ? contactStyles.contactScrollOverlay : ''}`}
        ref={mergeRefs}
        onPointerMove={isMobile ? null : handlePointerMove}
      >
        {/* Render content based on active section */}
        {renderSectionContent()}
      </div>
    </div>
  )
}