import React, { useRef, useState, useEffect, useMemo, useCallback, Suspense } from 'react';
import { useThree, useLoader, useFrame } from '@react-three/fiber';
import * as THREE from 'three';
import WaterMatcapFloor from '../WaterMatcap/WaterMatcapFloor';
import { Cameras, useCameraStore } from '../WaterMatcap/scene_floor/cameras3';
import { useControls } from 'leva';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { easing } from 'maath';

// BlackBackground component (from HeroSectionV2)
function BlackBackground() {
  const { scene } = useThree();
  React.useEffect(() => {
    scene.background = new THREE.Color('#000000');
    return () => {
      scene.background = null;
    };
  }, [scene]);
  return null;
}

// Camera Movement Controller - handles smooth camera transitions
function CameraMovementController({ cameraProgress, scrollPhase, isActive }) {
  const setOrbitPosition = useCameraStore((state) => state.setOrbitPosition);
  const setOrbitRotation = useCameraStore((state) => state.setOrbitRotation);

  // Default camera position for Scene1
  const defaultPosition = { x: 0, y: 0.35, z: 2 };
  const defaultRotation = { x: -0.12, y: 0, z: 0 };

  // Camera positions and rotations - 3 phases
  const startPosition = { x: 0, y: 0.35, z: 2 };
  const startRotation = { x: -0.12, y: 0, z: 0 };
  const centerPosition = { x: 0, y: 0.35, z: 1.25 }; // Inside the sphere
  const centerRotation = { x: 0, y: 0, z: 0 }; // Looking straight ahead when inside
  const finalPosition = { x: 0, y: 0.35, z: 2 }; // Match Canvas camera position for smooth transition
  const finalRotation = { x: -0.12, y: 0, z: 0 }; // Match start rotation for smooth transition

  // Easing functions for smooth transitions
  const easeInOutCubic = (t) => {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  };

  // Additional easing for extra smoothness on entry/exit
  const easeOutCubic = (t) => {
    return 1 - Math.pow(1 - t, 3);
  };

  useFrame(() => {
    // Reset camera to default position when Scene1 becomes inactive
    if (!isActive) {
      setOrbitPosition(defaultPosition);
      setOrbitRotation(defaultRotation);
      return;
    }

    let currentPosition, currentRotation;

    if (scrollPhase === 'toCenter') {
      // Phase 1: Start to Center (0 to 1)
      const easedProgress = easeOutCubic(cameraProgress);
      currentPosition = {
        x: startPosition.x + (centerPosition.x - startPosition.x) * easedProgress,
        y: startPosition.y + (centerPosition.y - startPosition.y) * easedProgress,
        z: startPosition.z + (centerPosition.z - startPosition.z) * easedProgress,
      };
      currentRotation = {
        x: startRotation.x + (centerRotation.x - startRotation.x) * easedProgress,
        y: startRotation.y + (centerRotation.y - startRotation.y) * easedProgress,
        z: startRotation.z + (centerRotation.z - startRotation.z) * easedProgress,
      };
    } else if (scrollPhase === 'atCenter') {
      // Phase 2: Stay at Center
      currentPosition = centerPosition;
      currentRotation = centerRotation;
    } else if (scrollPhase === 'toFinal') {
      // Phase 3: Center to Final (0 to 1)
      const easedProgress = easeInOutCubic(cameraProgress);
      currentPosition = {
        x: centerPosition.x + (finalPosition.x - centerPosition.x) * easedProgress,
        y: centerPosition.y + (finalPosition.y - centerPosition.y) * easedProgress,
        z: centerPosition.z + (finalPosition.z - centerPosition.z) * easedProgress,
      };
      currentRotation = {
        x: centerRotation.x + (finalRotation.x - centerRotation.x) * easedProgress,
        y: centerRotation.y + (finalRotation.y - centerRotation.y) * easedProgress,
        z: centerRotation.z + (finalRotation.z - centerRotation.z) * easedProgress,
      };
    }

    // Update camera store with smooth interpolated values
    setOrbitPosition(currentPosition);
    setOrbitRotation(currentRotation);
  });

  return null;
}

// SphereHeroMesh component (renamed from Block1Mesh, with explosion animations)
const SphereHeroMesh = React.forwardRef(function SphereHeroMesh({ displacement = 0.5, intensity = 1, explosionProgress = 0, ...props }, ref) {
  const gltf = useLoader(
    GLTFLoader,
    '/models/sphere_draco.glb',
    loader => {
      const dracoLoader = new DRACOLoader();
      dracoLoader.setDecoderPath('https://www.gstatic.com/draco/v1/decoders/');
      loader.setDRACOLoader(dracoLoader);
    }
  );

  // Load the normal map texture (will convert to KTX2 later for better performance)
  const normalMap = useLoader(THREE.TextureLoader, '/textures/sphere.normal_comp.jpg');

  const groupRef = useRef();
  const debrisGroupRef = useRef();
  const vec = useMemo(() => new THREE.Vector3(), []);
  const [isHovering, setIsHovering] = useState(false);
  const [hoverPoint, setHoverPoint] = useState(new THREE.Vector3());
  const [mouseVelocity, setMouseVelocity] = useState(new THREE.Vector3());
  const previousMousePos = useRef(new THREE.Vector3());
  const [debrisMeshes, setDebrisMeshes] = useState([]);

  // Apply performance-friendly glass material with normal map and cleanup
  useEffect(() => {
    if (!gltf.scene || !normalMap) return;

    const materialsToCleanup = [];

    gltf.scene.traverse((child) => {
      if (child.isMesh && child.material) {
        // Create a performance-friendly glass material using MeshPhysicalMaterial
        const glassMaterial = new THREE.MeshPhysicalMaterial({
          color: '#000000',
          normalMap: normalMap,
          metalness: 0,
          roughness: 0.1,
          transmission: 0.9,  // Glass-like transparency
          transparent: false,
          opacity: 1,
          clearcoat: 1.0,  // Adds glass-like reflection
          clearcoatRoughness: 0.1,
          ior: 1.5,  // Index of refraction for glass
          thickness: 0.5,  // Much thinner than MeshTransmissionMaterial
          envMapIntensity: 1.0,
          side: THREE.FrontSide,  // Single-sided for better performance
        });

        // Apply the glass material
        child.material = glassMaterial;
        child.material.needsUpdate = true;
        materialsToCleanup.push(glassMaterial);
      }
    });

    // Cleanup function to dispose materials
    return () => {
      materialsToCleanup.forEach(material => {
        if (material && material.dispose) {
          material.dispose();
        }
      });
    };
  }, [gltf.scene, normalMap]);

  // Create debris instances from specific cells with proper cleanup
  useEffect(() => {
    if (!gltf.scene) return;

    const debrisObjectNames = ["Sphere_cell108_cell006", "Sphere_cell122_cell006"];
    const debrisCount = 200; // Further reduced for better performance (was 400)
    const debrisPerType = Math.round(debrisCount / debrisObjectNames.length);

    // Store materials and geometries for cleanup
    const materialsToDispose = [];
    const geometriesToDispose = [];
    const newDebrisMeshes = [];

    debrisObjectNames.forEach(objectName => {
      // Find the specific cell in the GLB
      let debrisGeometry = null;
      gltf.scene.traverse((child) => {
        if (child.isMesh && child.name === objectName) {
          debrisGeometry = child.geometry.clone();
        }
      });

      if (debrisGeometry) {
        // Create instanced mesh for this debris type
        const debrisMaterial = new THREE.MeshPhysicalMaterial({
          color: '#ffffff',
          normalMap: normalMap,
          metalness: 0,
          roughness: 0.1,
          transmission: 0.9,
          transparent: false,
          opacity: 0.0, // Start invisible
          clearcoat: 1.0,
          clearcoatRoughness: 0.1,
          ior: 1.5,
          thickness: 0.5,
          envMapIntensity: 1.0,
          side: THREE.FrontSide,
        });

        const instancedMesh = new THREE.InstancedMesh(debrisGeometry, debrisMaterial, debrisPerType);

        // Set random positions for debris instances
        const matrix = new THREE.Matrix4();
        const position = new THREE.Vector3();
        const rotation = new THREE.Euler();
        const scale = new THREE.Vector3();

        for (let i = 0; i < debrisPerType; i++) {
          // Random position INSIDE the sphere - 50% wider distribution
          const radius = Math.random() * 1.2; // Increased from 0.8 to 1.2 (50% wider)
          const theta = Math.random() * Math.PI * 2; // Azimuth angle
          const phi = Math.acos(2 * Math.random() - 1); // Polar angle for uniform distribution

          position.set(
            radius * Math.sin(phi) * Math.cos(theta),
            radius * Math.sin(phi) * Math.sin(theta),
            radius * Math.cos(phi)
          );

          // Random rotation
          rotation.set(
            Math.random() * Math.PI * 2,
            Math.random() * Math.PI * 2,
            Math.random() * Math.PI * 2
          );

          // Random sizes - MUCH SMALLER like original!
          const scaleValue = 0.02 + Math.random() * 0.08; // 0.02 to 0.10 (tiny debris pieces)
          scale.set(scaleValue, scaleValue, scaleValue);

          matrix.compose(position, new THREE.Quaternion().setFromEuler(rotation), scale);
          instancedMesh.setMatrixAt(i, matrix);
        }

        instancedMesh.instanceMatrix.needsUpdate = true;
        // Store for cleanup
        materialsToDispose.push(debrisMaterial);
        geometriesToDispose.push(debrisGeometry);

        newDebrisMeshes.push({
          mesh: instancedMesh,
          material: debrisMaterial,
          originalPositions: Array.from({length: debrisPerType}, (_, i) => {
            const matrix = new THREE.Matrix4();
            instancedMesh.getMatrixAt(i, matrix);
            const pos = new THREE.Vector3();
            matrix.decompose(pos, new THREE.Quaternion(), new THREE.Vector3());
            return pos.clone();
          }),
          rotationSpeeds: Array.from({length: debrisPerType}, () => ({
            x: (Math.random() - 0.5) * 0.02, // Random rotation speeds
            y: (Math.random() - 0.5) * 0.02,
            z: (Math.random() - 0.5) * 0.02
          })),
          currentRotations: Array.from({length: debrisPerType}, () => ({
            x: 0,
            y: 0,
            z: 0
          }))
        });
      }
    });

    setDebrisMeshes(newDebrisMeshes);

    // Cleanup function to dispose materials and geometries
    return () => {
      materialsToDispose.forEach(material => {
        if (material && material.dispose) {
          material.dispose();
        }
      });
      geometriesToDispose.forEach(geometry => {
        if (geometry && geometry.dispose) {
          geometry.dispose();
        }
      });
      // Dispose debris meshes
      newDebrisMeshes.forEach(({ mesh, material }) => {
        if (mesh && mesh.geometry && mesh.geometry.dispose) {
          mesh.geometry.dispose();
        }
        if (material && material.dispose) {
          material.dispose();
        }
      });
    };
  }, [gltf.scene, normalMap]);

  // Store original positions of all mesh children
  const originalPositions = useMemo(() => {
    if (!gltf.scene) return [];
    const positions = [];
    gltf.scene.traverse((child) => {
      if (child.isMesh) {
        positions.push({
          mesh: child,
          originalPosition: child.position.clone(),
          originalColor: child.material ? (child.material.color ? child.material.color.clone() : new THREE.Color(1, 1, 1)) : new THREE.Color(1, 1, 1)
        });
      }
    });
    return positions;
  }, [gltf.scene]);

  // Handle pointer events on the block
  const handlePointerMove = useCallback((event) => {
    if (event.point) {
      // Calculate mouse velocity for directional effect
      const currentMousePos = event.point.clone();
      const velocity = currentMousePos.clone().sub(previousMousePos.current);

      setHoverPoint(currentMousePos);
      setMouseVelocity(velocity);
      setIsHovering(true);

      // Update previous position for next frame
      previousMousePos.current.copy(currentMousePos);
    }
  }, []);

  const handlePointerLeave = useCallback(() => {
    setIsHovering(false);
  }, []);

  // Animation frame logic (wave zone animations and hover effects, NO scroll/explosion)
  useFrame(({ clock, camera }, delta) => {
    if (!groupRef.current || originalPositions.length === 0) return;

    // Performance optimization: Calculate distance to camera for LOD
    const distanceToCamera = camera.position.distanceTo(groupRef.current.position);
    const isNearCamera = distanceToCamera < 3; // Only do complex animations when close
    const animationQuality = isNearCamera ? 1.0 : 0.3; // Reduce animation quality when far

    // Apply ambient animation to all pieces (always running)
    // Performance optimization: Skip some pieces when far from camera
    const skipFactor = isNearCamera ? 1 : 3; // Process every 3rd piece when far

    originalPositions.forEach(({ mesh, originalPosition, originalColor }, index) => {
      // Skip some animations when far from camera for performance
      if (!isNearCamera && index % skipFactor !== 0) {
        return;
      }

      // Calculate direction from center (0,0,0) to this piece
      const distanceFromCenter = originalPosition.length();
      let directionFromCenter;

      // Special handling for pieces too close to center (avoid division by zero and weird behavior)
      if (distanceFromCenter < 0.1) {
        // For center pieces, use a default forward direction (towards camera)
        directionFromCenter = new THREE.Vector3(0, 0, 1);
      } else {
        directionFromCenter = originalPosition.clone().normalize();
      }

      // Create overlapping animation zones for continuous, random-like movement
      const animationGroup = index % 8; // 8 different animation patterns

      // Calculate spherical coordinates for wave propagation
      const phi = Math.atan2(originalPosition.z, originalPosition.x); // Azimuth angle
      const theta = Math.acos(originalPosition.y / distanceFromCenter); // Polar angle

      // Create multiple overlapping wave zones that move independently and continuously
      // Each zone has different timing and phase offsets to create constant movement

      // Zone 1: Top hemisphere waves
      const topZoneWave = Math.sin(clock.elapsedTime * 1.8 + phi * 2.5 + theta * 1.2 + index * 0.1);

      // Zone 2: Bottom hemisphere waves
      const bottomZoneWave = Math.sin(clock.elapsedTime * 2.2 + phi * 1.8 + theta * 2.0 + index * 0.15);

      // Zone 3: Left side waves
      const leftZoneWave = Math.sin(clock.elapsedTime * 1.6 + phi * 3.0 + theta * 0.8 + index * 0.08);

      // Zone 4: Right side waves
      const rightZoneWave = Math.sin(clock.elapsedTime * 2.4 + phi * 1.5 + theta * 1.8 + index * 0.12);

      // Zone 5: Front waves
      const frontZoneWave = Math.sin(clock.elapsedTime * 2.0 + phi * 2.2 + theta * 1.5 + index * 0.09);

      // Zone 6: Back waves
      const backZoneWave = Math.sin(clock.elapsedTime * 1.4 + phi * 1.2 + theta * 2.5 + index * 0.11);

      // Determine which zones this piece belongs to based on its position
      const isTop = originalPosition.y > 0;
      const isLeft = originalPosition.x < 0;
      const isFront = originalPosition.z > 0;

      // Combine waves based on position (pieces can belong to multiple zones)
      let combinedWave = 0;
      let waveCount = 0;

      if (isTop) {
        combinedWave += topZoneWave;
        waveCount++;
      } else {
        combinedWave += bottomZoneWave;
        waveCount++;
      }

      if (isLeft) {
        combinedWave += leftZoneWave;
        waveCount++;
      } else {
        combinedWave += rightZoneWave;
        waveCount++;
      }

      if (isFront) {
        combinedWave += frontZoneWave;
        waveCount++;
      } else {
        combinedWave += backZoneWave;
        waveCount++;
      }

      // Average the waves to prevent excessive movement
      combinedWave = combinedWave / waveCount;

      // Add individual randomness for more organic feel
      const individualOffset = Math.sin(clock.elapsedTime * 2.6 + index * 0.2 + phi * 0.5 + theta * 0.3) * 0.3;
      combinedWave += individualOffset;

      // Amplitude based on animation group and distance from center
      let amplitude;
      const distanceFactor = Math.min(1.0, distanceFromCenter / 2.0); // Reduce amplitude for inner pieces

      if (animationGroup === 0) {
        amplitude = 0.04 * distanceFactor; // Minimal movement
      } else if (animationGroup === 1) {
        amplitude = 0.08 * distanceFactor; // Light movement
      } else if (animationGroup === 2) {
        amplitude = 0.12 * distanceFactor; // Medium movement
      } else if (animationGroup === 3) {
        amplitude = 0.10 * distanceFactor; // Medium movement
      } else if (animationGroup === 4) {
        amplitude = 0.14 * distanceFactor; // Strong movement
      } else if (animationGroup === 5) {
        amplitude = 0.09 * distanceFactor; // Medium-light movement
      } else if (animationGroup === 6) {
        amplitude = 0.11 * distanceFactor; // Medium-strong movement
      } else {
        amplitude = 0.07 * distanceFactor; // Light-medium movement
      }

      // Apply continuous radial movement (asymmetric for outward bias)
      const radialMovement = combinedWave > 0 ? combinedWave * amplitude : combinedWave * (amplitude * 0.4);
      const radialOffset = directionFromCenter.clone().multiplyScalar(radialMovement);

      // Add explosion effect based on explosionProgress (from HeroSectionV2)
      const explosionOffset = directionFromCenter.clone().multiplyScalar(explosionProgress * 3.0); // Explosion strength

      const baseAnimatedPosition = vec.copy(originalPosition).add(radialOffset).add(explosionOffset);

      // Add hover effect on top of ambient animation
      let finalPosition = baseAnimatedPosition.clone();
      let finalColor = originalColor.clone();

      if (isHovering && hoverPoint) {
        // Get world position of the mesh
        const worldPosition = new THREE.Vector3();
        mesh.getWorldPosition(worldPosition);

        // Calculate distance from hover point to mesh in world space
        const dist = worldPosition.distanceTo(hoverPoint);

        // Apply directional destroying effect within displacement radius
        if (dist < displacement) {
          // Calculate displacement strength
          const distanceRatio = 1 - (dist / displacement);
          const intensity = distanceRatio * distanceRatio; // Quadratic falloff

          // Directional effect based on mouse velocity
          const velocityMagnitude = mouseVelocity.length();
          const normalizedVelocity = velocityMagnitude > 0.001 ? mouseVelocity.clone().normalize() : new THREE.Vector3(0, 0, 1);

          // Convert world velocity to local space
          const worldToLocal = new THREE.Matrix4().copy(groupRef.current.matrixWorld).invert();
          const localVelocity = normalizedVelocity.clone().transformDirection(worldToLocal);

          // Apply directional displacement (pieces move in mouse direction)
          const directionalForce = localVelocity.multiplyScalar(intensity * velocityMagnitude * 8.0); // Strong directional effect

          // Always apply a minimum radial outward force, even when mouse is stationary
          const baseRadialForce = directionFromCenter.clone().multiplyScalar(intensity * 1.2); // Stronger base outward expansion

          // Additional radial force when mouse is moving
          const velocityRadialForce = directionFromCenter.clone().multiplyScalar(intensity * velocityMagnitude * 2.0);

          // Combine all forces: directional + base radial + velocity-based radial
          const totalDisplacement = directionalForce.add(baseRadialForce).add(velocityRadialForce);
          finalPosition.add(totalDisplacement);

          // Keep original color during hover (no color change)
          finalColor = originalColor.clone();
        }
      }

      // Apply the final position and color with much faster damping for smooth movement
      easing.damp3(mesh.position, finalPosition, 0.8, delta); // Much faster damping for smooth movement

      if (mesh.material && mesh.material.color) {
        easing.dampC(mesh.material.color, finalColor, 0.6, delta); // Faster color transitions
      }

      // Apply rotation to more moving pieces for enhanced living effect
      if (animationGroup >= 2 && animationGroup <= 6) {
        const rotationSpeed = animationGroup === 3 || animationGroup === 5 ? 0.8 : 0.6; // Varied rotation speeds
        const rotationOffset = Math.sin(clock.elapsedTime * rotationSpeed + index * 0.2) * 0.04;
        mesh.rotation.y = originalPosition.y * 0.1 + rotationOffset;

        // Add subtle X and Z rotation for more organic movement
        if (animationGroup === 3 || animationGroup === 4) {
          mesh.rotation.x = Math.sin(clock.elapsedTime * 0.7 + index * 0.15) * 0.03;
          mesh.rotation.z = Math.cos(clock.elapsedTime * 0.5 + index * 0.1) * 0.02;
        }
      }
    });

    // Add slower, more subtle rotation to the whole sphere
    if (groupRef.current) {
      groupRef.current.rotation.y = clock.elapsedTime * 0.05; // Much slower rotation around Y-axis
      groupRef.current.rotation.x = Math.sin(clock.elapsedTime * 0.2) * 0.03; // Slower, subtler wobble on X-axis
    }

    // Animate debris instances (no hover effects, smooth rotation)
    // Performance optimization: Only animate debris when near camera
    if (isNearCamera) {
      debrisMeshes.forEach(({ mesh, material, originalPositions, rotationSpeeds, currentRotations }) => {
        if (!mesh || !material || !rotationSpeeds || !currentRotations) return;

      // Control debris visibility based on explosion progress - appears earlier
      // Debris becomes visible starting at 25% explosion (explosionProgress = 0.25)
      // Once visible, it stays visible (persistence)
      if (explosionProgress < 0.15) {
        mesh.visible = false; // Completely hide the mesh before 25% explosion
      } else {
        mesh.visible = true; // Make mesh visible and keep it visible
        // Fade in from 25% to 35% explosion, then stay at full opacity
        const fadeProgress = Math.min(1.0, (explosionProgress - 0.15) / 0.1); // 0.25 to 0.35 range
        material.opacity = fadeProgress * 0.50; // Fade to full opacity (0.90) and maintain it
      }

      const matrix = new THREE.Matrix4();
      const position = new THREE.Vector3();
      const rotation = new THREE.Euler();
      const scale = new THREE.Vector3();

      for (let i = 0; i < originalPositions.length; i++) {
        const originalPos = originalPositions[i];

        // Calculate direction from center for subtle movement
        const directionFromCenter = originalPos.clone().normalize();

        // Add ambient movement (smaller than main pieces)
        const ambientOffset = Math.sin(clock.elapsedTime * 1.5 + i * 0.1) * 0.01;
        const ambientMovement = directionFromCenter.clone().multiplyScalar(ambientOffset);

        // Add explosion effect - debris moves outward but stays more centered (from HeroSectionV2)
        const explosionOffset = directionFromCenter.clone().multiplyScalar(explosionProgress * 1.0); // Small movement to stay inside/center

        // Calculate final position
        const finalPosition = originalPos.clone().add(ambientMovement).add(explosionOffset);

        // Get current matrix and update position
        mesh.getMatrixAt(i, matrix);
        matrix.decompose(position, new THREE.Quaternion().setFromEuler(rotation), scale);

        // Update position
        position.copy(finalPosition);

        // Smooth rotation using stored speeds (no jumping)
        currentRotations[i].x += rotationSpeeds[i].x;
        currentRotations[i].y += rotationSpeeds[i].y;
        currentRotations[i].z += rotationSpeeds[i].z;

        rotation.set(currentRotations[i].x, currentRotations[i].y, currentRotations[i].z);

        matrix.compose(position, new THREE.Quaternion().setFromEuler(rotation), scale);
        mesh.setMatrixAt(i, matrix);
      }

      mesh.instanceMatrix.needsUpdate = true;
    });
    } // Close the isNearCamera condition
  });

  return (
    <group
      ref={groupRef}
      {...props}
      onPointerMove={handlePointerMove}
      onPointerLeave={handlePointerLeave}
    >
      <primitive
        ref={ref}
        object={gltf.scene}
      />
      {/* Render debris instances */}
      <group ref={debrisGroupRef}>
        {debrisMeshes.map((debrisData, index) => (
          <primitive key={index} object={debrisData.mesh} />
        ))}
      </group>
    </group>
  );
});

// Scene1 Component - With BlackBackground, SphereHeroMesh, WaterMatcapFloor, cameras3 system, and helpers
export default function Scene1({ isActive = true, scrollProgress = 0, globalScrollProgress = 0 }) {
  // Scroll progress state management - 3 phases
  const [cameraProgress, setCameraProgress] = useState(0); // 0 to 1 for current phase
  const [scrollPhase, setScrollPhase] = useState('toCenter'); // 'toCenter', 'atCenter', 'toFinal'

  // Use the scroll progress directly (0 to 1) - already calculated in AppAI
  const explosionProgress = scrollProgress;

  // Helper controls
  const { showGrid, showAxes, gridSize, showDebugInfo } = useControls('Scene1 Helpers', {
    showGrid: false,
    showAxes: false,
    gridSize: { value: 10, min: 1, max: 20, step: 1 },
    showDebugInfo: true
  });

  // Debug info display
  useEffect(() => {
    if (showDebugInfo) {
      console.log('Scene1 Debug:', {
        isActive,
        scrollProgress: scrollProgress.toFixed(3),
        cameraProgress: cameraProgress.toFixed(3),
        explosionProgress: explosionProgress.toFixed(3),
        scrollPhase: scrollPhase
      });
    }
  }, [isActive, scrollProgress, cameraProgress, explosionProgress, scrollPhase, showDebugInfo]);

  // Update phases based on scroll progress (no longer handling scroll events directly)
  useEffect(() => {
    // Update phases based on Scene1's scroll progress (0 to 1)
    if (scrollProgress <= 0.35) {
      setScrollPhase('toCenter');
      const phaseProgress = scrollProgress / 0.35;
      setCameraProgress(phaseProgress);
    } else if (scrollProgress <= 0.65) {
      setScrollPhase('atCenter');
      setCameraProgress(1);
    } else {
      setScrollPhase('toFinal');
      const phaseProgress = (scrollProgress - 0.65) / 0.35;
      setCameraProgress(phaseProgress);
    }
  }, [scrollProgress]);

  return (
    <group>
          {/* BlackBackground from HeroSectionV2 */}
          <BlackBackground />

          {/* Camera system from scene_floor */}
          <Cameras />

          {/* Camera Movement Controller */}
          <CameraMovementController cameraProgress={cameraProgress} scrollPhase={scrollPhase} isActive={isActive} />

        {/* Helper Grid */}
        {showGrid && (
          <gridHelper args={[gridSize, 10, '#888888', '#444444']} />
        )}

        {/* Axes Helper */}
        {showAxes && (
          <axesHelper args={[gridSize / 2]} />
        )}

        {/* Lighting */}
        <ambientLight intensity={0.5} />
        <pointLight position={[5, 5, 5]} intensity={1} />

        {/* SphereHeroMesh (renamed Block1Mesh with all animations) - Wrapped in Suspense for progressive loading */}
        <Suspense fallback={null}>
          <SphereHeroMesh
            position={[0, 0.4, 0.70]}
            scale={[0.40, 0.40, 0.40]}
            displacement={0.6}
            intensity={0.15}
            explosionProgress={explosionProgress}
          />
        </Suspense>

        {/* WaterMatcap Floor */}
        <WaterMatcapFloor position={[0, 0, 0]} />
      </group>
  );
}
